(function(b,f){f=f(b,b.document);b.lazySizes=f;"object"==typeof module&&module.exports&&(module.exports=f)})("undefined"!=typeof window?window:{},function(b,f){var e,d;(function(){var a,g={lazyClass:"lazyload",loadedClass:"lazyloaded",loadingClass:"lazyloading",preloadClass:"lazypreload",errorClass:"lazyerror",autosizesClass:"lazyautosizes",srcAttr:"data-src",srcsetAttr:"data-srcset",sizesAttr:"data-sizes",minSize:40,customMedia:{},init:!0,expFactor:1,hFac:.8,loadMode:1,loadHidden:!1,ricTimeout:0,
throttleDelay:125,expand:1};d=b.lazySizesConfig||b.lazysizesConfig||{};for(a in g)a in d||(d[a]=g[a])})();if(!f||!f.getElementsByClassName)return{init:function(){},cfg:d,noSupport:!0};var c=f.documentElement,a=b.Date,g=b.HTMLPictureElement,h=b.addEventListener,p=b.setTimeout,n=b.requestAnimationFrame||p,l=b.requestIdleCallback,k=/^picture$/i,m=["load","error","lazyincluded","_lazyloaded"],q={},x=Array.prototype.forEach,r=function(a,b){q[b]||(q[b]=new RegExp("(\\s|^)"+b+"(\\s|$)"));return q[b].test(a.getAttribute("class")||
"")&&q[b]},B=function(a,b){r(a,b)||a.setAttribute("class",(a.getAttribute("class")||"").trim()+" "+b)},D=function(a,b){var g;(g=r(a,b))&&a.setAttribute("class",(a.getAttribute("class")||"").replace(g," "))},E=function(a,b,g){var c=g?"addEventListener":"removeEventListener";g&&E(a,b);m.forEach(function(g){a[c](g,b)})},t=function(a,b,g,c,h){var d=f.createEvent("Event");g||(g={});g.instance=e;d.initEvent(b,!c,!h);d.detail=g;a.dispatchEvent(d);return d},C=function(a,c){var h;!g&&(h=b.picturefill||d.pf)?
(c&&c.src&&!a.getAttribute("srcset")&&a.setAttribute("srcset",c.src),h({reevaluate:!0,elements:[a]})):c&&c.src&&(a.src=c.src)},v=function(a,b,g){for(g=g||a.offsetWidth;g<d.minSize&&b&&!a._lazysizesWidth;)g=b.offsetWidth,b=b.parentNode;return g},y=function(){var a,b,g=[],c=[],h=g,d=function(){var d=h;h=g.length?c:g;a=!0;for(b=!1;d.length;)d.shift()();a=!1},e=function(g,c){a&&!c?g.apply(this,arguments):(h.push(g),b||(b=!0,(f.hidden?p:n)(d)))};e._lsFlush=d;return e}(),K=function(a,b){return b?function(){y(a)}:
function(){var b=this,g=arguments;y(function(){a.apply(b,g)})}},Z=function(b){var g,c=0,h=d.throttleDelay,e=d.ricTimeout,f=function(){g=!1;c=a.now();b()},n=l&&49<e?function(){l(f,{timeout:e});e!==d.ricTimeout&&(e=d.ricTimeout)}:K(function(){p(f)},!0);return function(b){var d;if(b=!0===b)e=33;g||(g=!0,d=h-(a.now()-c),0>d&&(d=0),b||39>d?n():p(n,d))}},S=function(b){var g,c,d=function(){g=null;b()},h=function(){var b=a.now()-c;99>b?p(h,99-b):(l||d)(d)};return function(){c=a.now();g||(g=p(h,99))}},O=function(){var g,
n,l,v,m,q,R,G,H,I,J,A,O=/^img$/i,aa=/^iframe$/i,ba="onscroll"in b&&!/(gle|ing)bot/.test(navigator.userAgent),L=0,z=0,F=-1,T=function(a){z--;if(!a||0>z||!a.target)z=0},U=function(a){null==A&&(A="hidden"==(getComputedStyle(f.body,null)||{}).visibility);return A||"hidden"!=(getComputedStyle(a.parentNode,null)||{}).visibility&&"hidden"!=(getComputedStyle(a,null)||{}).visibility},V=function(){var a,b,h,p,l,k,m,r,Q,y,u,w=e.elements;if((v=d.loadMode)&&8>z&&(a=w.length)){b=0;for(F++;b<a;b++)if(w[b]&&!w[b]._lazyRace)if(!ba||
e.prematureUnveil&&e.prematureUnveil(w[b]))M(w[b]);else{(r=w[b].getAttribute("data-expand"))&&(k=1*r)||(k=L);y||(y=!d.expand||1>d.expand?500<c.clientHeight&&500<c.clientWidth?500:370:d.expand,e._defEx=y,h=y*d.expFactor,u=d.hFac,A=null,L<h&&1>z&&2<F&&2<v&&!f.hidden?(L=h,F=0):L=1<v&&1<F&&6>z?y:0);Q!==k&&(q=innerWidth+k*u,R=innerHeight+k,m=-1*k,Q=k);h=w[b].getBoundingClientRect();if((h=(J=h.bottom)>=m&&(G=h.top)<=R&&(I=h.right)>=m*u&&(H=h.left)<=q&&(J||I||H||G)&&(d.loadHidden||U(w[b])))&&!(h=n&&3>z&&
!r&&(3>v||4>F))){var x=w[b],t=k;h=x;x=U(x);G-=t;J+=t;H-=t;for(I+=t;x&&(h=h.offsetParent)&&h!=f.body&&h!=c;)(x=0<((getComputedStyle(h,null)||{}).opacity||1))&&"visible"!=(getComputedStyle(h,null)||{}).overflow&&(t=h.getBoundingClientRect(),x=I>t.left&&H<t.right&&J>t.top-1&&G<t.bottom+1);h=x}if(h){if(M(w[b]),l=!0,9<z)break}else!l&&n&&!p&&4>z&&4>F&&2<v&&(g[0]||d.preloadAfterLoad)&&(g[0]||!r&&(J||I||H||G||"auto"!=w[b].getAttribute(d.sizesAttr)))&&(p=g[0]||w[b])}p&&!l&&M(p)}},u=Z(V),X=function(a){var b=
a.target;b._lazyCache?delete b._lazyCache:(T(a),B(b,d.loadedClass),D(b,d.loadingClass),E(b,W),t(b,"lazyloaded"))},ca=K(X),W=function(a){ca({target:a.target})},da=function(a,b){try{a.contentWindow.location.replace(b)}catch(g){a.src=b}},ea=function(a){var b,g=a.getAttribute(d.srcsetAttr);(b=d.customMedia[a.getAttribute("data-media")||a.getAttribute("media")])&&a.setAttribute("media",b);g&&a.setAttribute("srcset",g)},fa=K(function(a,b,g,c,h){var e,f,n,v;if(!(n=t(a,"lazybeforeunveil",b)).defaultPrevented){if(a.parentElement&&
a.parentElement.classList.contains("s-picture")){var m=a.parentElement.querySelector(".blank-source");m&&m.remove()}c&&(g?B(a,d.autosizesClass):a.setAttribute("sizes",c));c=a.getAttribute(d.srcsetAttr);g=a.getAttribute(d.srcAttr);h&&(f=(e=a.parentNode)&&k.test(e.nodeName||""));v=b.firesLoad||"src"in a&&(c||g||f);n={target:a};B(a,d.loadingClass);v&&(clearTimeout(l),l=p(T,2500),E(a,W,!0));f&&x.call(e.getElementsByTagName("source"),ea);c?a.setAttribute("srcset",c):g&&!f&&(aa.test(a.nodeName)?da(a,g):
a.src=g);h&&(c||f)&&C(a,{src:g})}a._lazyRace&&delete a._lazyRace;D(a,d.lazyClass);y(function(){var b=a.complete&&1<a.naturalWidth;if(!v||b)b&&B(a,"ls-is-cached"),X(n),a._lazyCache=!0,p(function(){"_lazyCache"in a&&delete a._lazyCache},9);"lazy"==a.loading&&z--},!0)}),M=function(a){if(!a._lazyRace){var b,g=O.test(a.nodeName),c=g&&(a.getAttribute(d.sizesAttr)||a.getAttribute("sizes")),h="auto"==c;if(!h&&n||!g||!a.getAttribute("src")&&!a.srcset||a.complete||r(a,d.errorClass)||!r(a,d.lazyClass))b=t(a,
"lazyunveilread").detail,h&&P.updateElem(a,!0,a.offsetWidth),a._lazyRace=!0,z++,fa(a,b,h,c,g)}},ga=S(function(){d.loadMode=3;u()}),Y=function(){3==d.loadMode&&(d.loadMode=2);ga()},N=function(){n||(999>a.now()-m?p(N,999):(n=!0,d.loadMode=3,u(),h("scroll",Y,!0)))};return{_:function(){m=a.now();e.elements=f.getElementsByClassName(d.lazyClass);g=f.getElementsByClassName(d.lazyClass+" "+d.preloadClass);h("scroll",u,!0);h("resize",u,!0);b.MutationObserver?(new MutationObserver(u)).observe(c,{childList:!0,
subtree:!0,attributes:!0}):(c.addEventListener("DOMNodeInserted",u,!0),c.addEventListener("DOMAttrModified",u,!0),setInterval(u,999));h("hashchange",u,!0);"focus mouseover click load transitionend animationend".split(" ").forEach(function(a){f.addEventListener(a,u,!0)});/d$|^c/.test(f.readyState)?N():(h("load",N),f.addEventListener("DOMContentLoaded",u),p(N,2E4));e.elements.length?(V(),y._lsFlush()):u()},checkElems:u,unveil:M,_aLSL:Y}}(),P=function(){var a,b=K(function(a,b,g,c){var h,d;a._lazysizesWidth=
c;c+="px";a.setAttribute("sizes",c);if(k.test(b.nodeName||""))for(b=b.getElementsByTagName("source"),h=0,d=b.length;h<d;h++)b[h].setAttribute("sizes",c);g.detail.dataAttr||C(a,g.detail)}),g=function(a,g,c){var h=a.parentNode;h&&(c=v(a,h,c),g=t(a,"lazybeforesizes",{width:c,dataAttr:!!g}),g.defaultPrevented||(c=g.detail.width)&&c!==a._lazysizesWidth&&b(a,h,g,c))},c=S(function(){var b,c=a.length;if(c)for(b=0;b<c;b++)g(a[b])});return{_:function(){a=f.getElementsByClassName(d.autosizesClass);h("resize",
c)},checkElems:c,updateElem:g}}(),A=function(){!A.i&&f.getElementsByClassName&&(A.i=!0,P._(),O._())};p(function(){d.init&&A()});return e={cfg:d,autoSizer:P,loader:O,init:A,uP:C,aC:B,rC:D,hC:r,fire:t,gW:v,rAF:y}});var edrUtils={checkCookie:function(b){if(!$.cookie(b.trackCookieName)){var f=new Date;f.setTime(f.getTime()+36E5);$.cookie(b.trackCookieName,b.trackId,{expires:f,path:"/",domain:"sahibinden.com"})}},projectFunnelEventTrigger:function(b,f,e,d,c){$.ajax({url:"/ajax/projects/events/projectsFunnel/trigger",type:"POST",data:JSON.stringify({page:b,action:f,searchResultMeta:e,projectId:c}),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(a){edrUtils.checkCookie(a.data);d&&
d()}})},projectLetUsCallYouFunnelEventTrigger:function(b){$.ajax({url:"/ajax/projects-service/projects/edr/let-us-call-you",type:"POST",data:JSON.stringify(b),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(b){edrUtils.checkCookie(b.data)}})},projectCallLeadFunnel:function(b,f,e,d){$.ajax({url:"/ajax/projects-service/projects/edr/project-call-lead-funnel",type:"POST",data:JSON.stringify({page:"ProjectDetail",action:b,projectId:f,userId:e,ownerStoreId:d}),dataType:"json",
contentType:"application/json; charset\x3dutf-8",success:function(b){edrUtils.checkCookie(b.data)}})},projectsLandingPage:function(b,f,e){$.ajax({url:"/ajax/projects-service/projects/edr/project-office",type:"POST",data:JSON.stringify({section:b,action:f}),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(b){edrUtils.checkCookie(b.data);e&&e()}})},projectUserInteraction:function(b,f,e,d){$.ajax({url:"/ajax/projects-service/projects/edr/user-interaction",type:"POST",
data:JSON.stringify({page:b,action:f,projectId:e,floorPlanName:d}),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(b){edrUtils.checkCookie(b.data)}})},sahibindenAcademyEdr:function(b,f,e){$.ajax({url:"/stores/sahibindenAcademyEdr",type:"POST",data:JSON.stringify({section:b,action:f,storeId:e}),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(b){edrUtils.checkCookie(b.data)}})},londonPurchaseDemandWarningsEdr:function(b,f){$.ajax({url:"/ajax/london/edr/purchase-demand-warning",
type:"POST",data:JSON.stringify({page:b,action:f,errorCode:"NOT_VERIFIED_GSM_CO"}),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(b){edrUtils.checkCookie(b.data)}})},londonAuctionSearchEdr:function(b,f,e){return new Promise(function(d,c){$.ajax({url:"/ajax/landingPage/triggerLondonAuctionSearchEdr",type:"POST",contentType:"application/json; charset\x3dutf-8",data:JSON.stringify({page:b,action:f,uniqTrackId:e}),success:function(a){d(a)},error:function(a){c(a)}})})},
sendMarketplaceEdr:function(b,f){$.ajax({url:"/ajax/refurbishment/marketplaceLandingEdr",type:"POST",dataType:"json",contentType:"application/json; charset\x3dutf-8",data:JSON.stringify(b),complete:function(){f&&f()}})},sendBuybackEdr:function(b,f){$.ajax({url:"/ajax/refurbishment/buybackEdr",type:"POST",dataType:"json",contentType:"application/json; charset\x3dutf-8",data:JSON.stringify(b),complete:function(){f&&f()}})}};window.globalGenerateGUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(b){var f=16*Math.random()|0;return("x"===b?f:f&3|8).toString(16)})};var MarketplaceSearchEdrHelper={cookieName:"refurb-mp-tid",getTrackId:function(b){var f=cookieUtils.readCookie(this.cookieName);if(!f||b)f=globalGenerateGUID(),cookieUtils.setCookie(this.cookieName,f);return f},postEdr:function(b,f,e){b.uniqTrackId&&!f||Object.assign(b,{uniqTrackId:this.getTrackId(f)});$.ajax({url:"/ajax/refurbishment/marketplaceSearchEdr",type:"POST",data:JSON.stringify(b),dataType:"json",contentType:"application/json; charset\x3dutf-8",success:function(b){if(e&&e.onSuccess)e.onSuccess(b)},
error:function(b){if(e&&e.onError)e.onError(b)}})}};$(function(){if($.browser.msie&&9>=parseInt($.browser.version,10)){var b=$("#browserUpdateClose"),f=$("#topMessage");sessionStorage.getItem("ie9DontShow")?f.hide():f.html($("#browserUpdateTemplate").html()).show();if(b)f.on("click","#browserUpdateClose",function(a){a.preventDefault();f.hide();sessionStorage.setItem("ie9DontShow",!0)})}var b=cookieUtils.readCookie("vid"),e=$(".download-app-side-bar"),d=e.find("label"),c=e.find(".close"),a=e.find(".content"),g=e.find(".hide"),h=$(".download-app-banner"),
p=h.find(".close"),n=function(){var a=new Date;a.setFullYear(a.getFullYear()+1);return a};cookieUtils.getStorage("disabledDownloadAppBanner")||(100<b&&100>=b&&e.show(),500<b&&500>=b&&h.show());d.on("click",function(b){b.preventDefault();a.addClass("active")});g.on("click",function(b){b.preventDefault();a.removeClass("active")});p.on("click",function(a){a.preventDefault();h.hide();cookieUtils.setStorage("disabledDownloadAppBanner",!0,n())});c.on("click",function(a){a.preventDefault();e.hide();cookieUtils.setStorage("disabledDownloadAppBanner",
!0,n())});var l=$(".daily-opportunity-content ul"),b=$(".daily-opportunity-content ul li"),d=$("#dailyOpportunityPrevButton"),c=$("#dailyOpportunityNextButton"),k=0,m=parseInt(b.length/3)-1,q=!0,x=$(".daily-opportunity-slide-container").width(),r=!0,B=function(){l.animate({left:k*-x},300,function(){q=!0})},D=function(){r=clearInterval(r);r=setInterval(function(){E()},5E3)},E=function(){q&&(q=!1,k<m?k++:k=0,B())};D();c.on("click",function(a){a.preventDefault();r=clearInterval(r);E()});d.on("click",
function(a){a.preventDefault();q&&0<k&&(q=!1,k--,r=clearInterval(r),B())});l.mouseenter(function(){r=clearInterval(r)}).mouseleave(function(){D()});$('[title\x3d"Projeler"]').on("click",function(a){a.preventDefault();var b=$(this);edrUtils.projectFunnelEventTrigger("Homepage","ProjectsClick",null,function(){window.location.href=b.attr("href")})});$(".auto-360-showcase .service-link, .estate-360-showcase .service-link").on("click",function(a){a.preventDefault();sessionStorage.setItem("previousPage",
"HomepageShowcase");window.open(this.href,"_blank")});$(".edr-trigger").on("click",function(){sessionStorage.setItem("previousPage","HomepageAccordion-Auto360")});var b=$("#yepy-link-primary"),d=$("#yepy-link-category-tree"),c=$('.category-0 a[data-synthetic-category-id\x3d"7"]'),t=$("div.homepage-content \x3e div.yepy-banner");if(b)b.on("click",function(a){a.preventDefault();var b=$(this).attr("href");MarketplaceSearchEdrHelper.postEdr({page:"HomePage",action:"YepyIconClick"},!0);var g=$(this).attr("target")||
"_self";setTimeout(function(){window.open(b,g)},100)});if(d)d.on("click",function(a){a.preventDefault();a=$(this).attr("href");MarketplaceSearchEdrHelper.postEdr({page:"HomePage",action:"YepyClick"},!0);var b=$(this).attr("target")||"_self";window.open(a,b)});if(c)c.on("click",function(a){a.preventDefault();a=$(this).attr("href");MarketplaceSearchEdrHelper.postEdr({page:"HomePage",action:"YepyRefurbishedTechnologicalDevicesClick"},!0);var b=$(this).attr("target")||"_self";window.open(a,b)});var C=
function(){MarketplaceSearchEdrHelper.postEdr({page:"HomePage",action:"YepyBannerClick"},!0,{onSuccess:function(){t.one("click",C)},onError:function(){t.one("click",C)}})};if(t)t.one("click",C);(function(){var a=[];$(".projects").each(function(){a.push($(this).data("id"))});0<a.length&&$.ajax({url:"/ajax/counter/projects/increment",type:"POST",data:JSON.stringify({projectIds:a,incrementType:"showCaseView"}),dataType:"json",contentType:"application/json; charset\x3dutf-8"})})();PixelRatioTracker.send();
(function(){"undefined"!=typeof $("#popularBrands").owlCarousel&&$("#popularBrands").owlCarousel({slideSpeed:2E3,items:12,nav:!1,autoplay:!1,dots:!1,onDragged:function(){$(".owl-carousel").trigger("refresh.owl.carousel")}})})()});/*
 The MIT License (MIT)
 @todo Lazy Load Icon
 @todo prevent animationend bubling
 @todo itemsScaleUp
 @todo Test Zepto
 @todo stagePadding calculate wrong active classes
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
 The MIT License (MIT)
*/
(function(b,f,e,d){function c(a,g){this.settings=null;this.options=b.extend({},c.Defaults,g);this.$element=b(a);this._handlers={};this._plugins={};this._supress={};this._speed=this._current=null;this._coordinates=[];this._width=this._breakpoint=null;this._items=[];this._clones=[];this._mergers=[];this._widths=[];this._invalidated={};this._pipe=[];this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null};this._states={current:{},tags:{initializing:["busy"],animating:["busy"],
dragging:["interacting"]}};b.each(["onResize","onThrottledResize"],b.proxy(function(a,g){this._handlers[g]=b.proxy(this[g],this)},this));b.each(c.Plugins,b.proxy(function(a,b){this._plugins[a.charAt(0).toLowerCase()+a.slice(1)]=new b(this)},this));b.each(c.Workers,b.proxy(function(a,g){this._pipe.push({filter:g.filter,run:b.proxy(g.run,this)})},this));this.setup();this.initialize()}c.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,
margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:f,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",
grabClass:"owl-grab"};c.Width={Default:"default",Inner:"inner",Outer:"outer"};c.Type={Event:"event",State:"state"};c.Plugins={};c.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(a){a.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(a){var b=this.settings.margin||
"",c=this.settings.rtl,b={width:"auto","margin-left":c?b:"","margin-right":c?"":b};this.settings.autoWidth&&this.$stage.children().css(b);a.css=b}},{filter:["width","items","settings"],run:function(a){var b=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,c,d=this._items.length,e=!this.settings.autoWidth,f=[];for(a.items={merge:!1,width:b};d--;)c=this._mergers[d],c=this.settings.mergeFit&&Math.min(c,this.settings.items)||c,a.items.merge=1<c||a.items.merge,f[d]=e?b*c:this._items[d].width();
this._widths=f}},{filter:["items","settings"],run:function(){for(var a=[],g=this._items,c=this.settings,d=Math.max(2*c.items,4),e=2*Math.ceil(g.length/2),c=c.loop&&g.length?c.rewind?d:Math.max(d,e):0,e=d="",c=c/2;0<c;)a.push(this.normalize(a.length/2,!0)),d+=g[a[a.length-1]][0].outerHTML,a.push(this.normalize(g.length-1-(a.length-1)/2,!0)),e=g[a[a.length-1]][0].outerHTML+e,--c;this._clones=a;b(d).addClass("cloned").appendTo(this.$stage);b(e).addClass("cloned").prependTo(this.$stage)}},{filter:["width",
"items","settings"],run:function(){for(var a=this.settings.rtl?1:-1,b=this._clones.length+this._items.length,c=-1,d,e,f=[];++c<b;)d=f[c-1]||0,e=this._widths[this.relative(c)]+this.settings.margin,f.push(d+e*a);this._coordinates=f}},{filter:["width","items","settings"],run:function(){var a=this.settings.stagePadding,b=this._coordinates;this.$stage.css({width:Math.ceil(Math.abs(b[b.length-1]))+2*a,"padding-left":a||"","padding-right":a||""})}},{filter:["width","items","settings"],run:function(a){var b=
this._coordinates.length,c=!this.settings.autoWidth,d=this.$stage.children();if(c&&a.items.merge)for(;b--;)a.css.width=this._widths[this.relative(b)],d.eq(b).css(a.css);else c&&(a.css.width=a.items.width,d.css(a.css))}},{filter:["items"],run:function(){1>this._coordinates.length&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(a){a.current=a.current?this.$stage.children().index(a.current):0;a.current=Math.max(this.minimum(),Math.min(this.maximum(),a.current));this.reset(a.current)}},
{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){var a=this.settings.rtl?1:-1,b=2*this.settings.stagePadding,c=this.coordinates(this.current())+b,d=c+this.width()*a,e,f,k=[],m,q;m=0;for(q=this._coordinates.length;m<q;m++)e=this._coordinates[m-1]||0,f=Math.abs(this._coordinates[m])+b*a,(this.op(e,"\x3c\x3d",c)&&this.op(e,"\x3e",d)||this.op(f,"\x3c",c)&&this.op(f,"\x3e",d))&&k.push(m);this.$stage.children(".active").removeClass("active");
this.$stage.children(":eq("+k.join("), :eq(")+")").addClass("active");this.$stage.children(".center").removeClass("center");this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}];c.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass);this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=b("\x3c"+this.settings.stageElement+"\x3e",{"class":this.settings.stageClass}).wrap(b("\x3cdiv/\x3e",{"class":this.settings.stageOuterClass})),
this.$element.append(this.$stage.parent()))};c.prototype.initializeItems=function(){var a=this.$element.find(".owl-item");a.length?(this._items=a.get().map(function(a){return b(a)}),this._mergers=this._items.map(function(){return 1}),this.refresh()):(this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass))};c.prototype.initialize=function(){this.enter("initializing");
this.trigger("initialize");this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl);if(this.settings.autoWidth&&!this.is("pre-loading")){var a,b;a=this.$element.find("img");b=this.$element.children(this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:d).width();a.length&&0>=b&&this.preloadAutoWidthImages(a)}this.initializeStage();this.initializeItems();this.registerEventHandlers();this.leave("initializing");this.trigger("initialized")};c.prototype.isVisible=function(){return this.settings.checkVisibility?
this.$element.is(":visible"):!0};c.prototype.setup=function(){var a=this.viewport(),c=this.options.responsive,d=-1,e=null;c?(b.each(c,function(b){b<=a&&b>d&&(d=Number(b))}),e=b.extend({},this.options,c[d]),"function"===typeof e.stagePadding&&(e.stagePadding=e.stagePadding()),delete e.responsive,e.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+d))):e=b.extend({},this.options);this.trigger("change",{property:{name:"settings",
value:e}});this._breakpoint=d;this.settings=e;this.invalidate("settings");this.trigger("changed",{property:{name:"settings",value:this.settings}})};c.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)};c.prototype.prepare=function(a){var c=this.trigger("prepare",{content:a});c.data||(c.data=b("\x3c"+this.settings.itemElement+"/\x3e").addClass(this.options.itemClass).append(a));this.trigger("prepared",{content:c.data});return c.data};c.prototype.update=
function(){for(var a=0,c=this._pipe.length,d=b.proxy(function(a){return this[a]},this._invalidated),e={};a<c;)(this._invalidated.all||0<b.grep(this._pipe[a].filter,d).length)&&this._pipe[a].run(e),a++;this._invalidated={};!this.is("valid")&&this.enter("valid")};c.prototype.width=function(a){a=a||c.Width.Default;switch(a){case c.Width.Inner:case c.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}};c.prototype.refresh=function(){this.enter("refreshing");
this.trigger("refresh");this.setup();this.optionsLogic();this.$element.addClass(this.options.refreshClass);this.update();this.$element.removeClass(this.options.refreshClass);this.leave("refreshing");this.trigger("refreshed")};c.prototype.onThrottledResize=function(){f.clearTimeout(this.resizeTimer);this.resizeTimer=f.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)};c.prototype.onResize=function(){if(!this._items.length||this._width===this.$element.width()||!this.isVisible())return!1;
this.enter("resizing");if(this.trigger("resize").isDefaultPrevented())return this.leave("resizing"),!1;this.invalidate("width");this.refresh();this.leave("resizing");this.trigger("resized")};c.prototype.registerEventHandlers=function(){if(b.support.transition)this.$stage.on(b.support.transition.end+".owl.core",b.proxy(this.onTransitionEnd,this));if(!1!==this.settings.responsive)this.on(f,"resize",this._handlers.onThrottledResize);this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),
this.$stage.on("mousedown.owl.core",b.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1}));this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",b.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",b.proxy(this.onDragEnd,this)))};c.prototype.onDragStart=function(a){var c=null;3!==a.which&&(b.support.transform?(c=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","),c={x:c[16===c.length?12:4],y:c[16===c.length?
13:5]}):(c=this.$stage.position(),c={x:this.settings.rtl?c.left+this.$stage.width()-this.width()+this.settings.margin:c.left,y:c.top}),this.is("animating")&&(b.support.transform?this.animate(c.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===a.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=b(a.target),this._drag.stage.start=c,this._drag.stage.current=c,this._drag.pointer=this.pointer(a),b(e).on("mouseup.owl.core touchend.owl.core",
b.proxy(this.onDragEnd,this)),b(e).one("mousemove.owl.core touchmove.owl.core",b.proxy(function(a){var c=this.difference(this._drag.pointer,this.pointer(a));b(e).on("mousemove.owl.core touchmove.owl.core",b.proxy(this.onDragMove,this));Math.abs(c.x)<Math.abs(c.y)&&this.is("valid")||(a.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))};c.prototype.onDragMove=function(a){var b,c;c=this.difference(this._drag.pointer,this.pointer(a));var d=this.difference(this._drag.stage.start,c);
this.is("dragging")&&(a.preventDefault(),this.settings.loop?(a=this.coordinates(this.minimum()),b=this.coordinates(this.maximum()+1)-a,d.x=((d.x-a)%b+b)%b+a):(a=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),b=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),c=this.settings.pullDrag?-1*c.x/5:0,d.x=Math.max(Math.min(d.x,a+c),b+c)),this._drag.stage.current=d,this.animate(d.x))};c.prototype.onDragEnd=function(a){a=this.difference(this._drag.pointer,
this.pointer(a));var c=this._drag.stage.current,d=0<a.x^this.settings.rtl?"left":"right";b(e).off(".owl.core");this.$element.removeClass(this.options.grabClass);if(0!==a.x&&this.is("dragging")||!this.is("valid"))if(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(c.x,0!==a.x?d:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=d,3<Math.abs(a.x)||300<(new Date).getTime()-this._drag.time)this._drag.target.one("click.owl.core",
function(){return!1});this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))};c.prototype.closest=function(a,c){var h=-1,e=this.width(),f=this.coordinates();this.settings.freeDrag||b.each(f,b.proxy(function(b,k){"left"===c&&a>k-30&&a<k+30?h=b:"right"===c&&a>k-e-30&&a<k-e+30?h=b+1:this.op(a,"\x3c",k)&&this.op(a,"\x3e",f[b+1]!==d?f[b+1]:k-e)&&(h="left"===c?b+1:b);return-1===h},this));this.settings.loop||(this.op(a,"\x3e\x3d",f[this.minimum()])?h=a=this.minimum():this.op(a,"\x3c",f[this.maximum()])&&
(h=a=this.maximum()));return h};c.prototype.animate=function(a){var c=0<this.speed();this.is("animating")&&this.onTransitionEnd();c&&(this.enter("animating"),this.trigger("translate"));b.support.transform3d&&b.support.transition?this.$stage.css({transform:"translate3d("+a+"px,0px,0px)",transition:this.speed()/1E3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):c?this.$stage.animate({left:a+"px"},this.speed(),this.settings.fallbackEasing,b.proxy(this.onTransitionEnd,this)):
this.$stage.css({left:a+"px"})};c.prototype.is=function(a){return this._states.current[a]&&0<this._states.current[a]};c.prototype.current=function(a){if(a===d)return this._current;if(0===this._items.length)return d;a=this.normalize(a);if(this._current!==a){var b=this.trigger("change",{property:{name:"position",value:a}});b.data!==d&&(a=this.normalize(b.data));this._current=a;this.invalidate("position");this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current};
c.prototype.invalidate=function(a){"string"===b.type(a)&&(this._invalidated[a]=!0,this.is("valid")&&this.leave("valid"));return b.map(this._invalidated,function(a,b){return b})};c.prototype.reset=function(a){a=this.normalize(a);a!==d&&(this._speed=0,this._current=a,this.suppress(["translate","translated"]),this.animate(this.coordinates(a)),this.release(["translate","translated"]))};c.prototype.normalize=function(a,b){var c=this._items.length;b=b?0:this._clones.length;if(!this.isNumeric(a)||1>c)a=
d;else if(0>a||a>=c+b)a=((a-b/2)%c+c)%c+b/2;return a};c.prototype.relative=function(a){a-=this._clones.length/2;return this.normalize(a,!0)};c.prototype.maximum=function(a){var b=this.settings,c,d;if(b.loop)b=this._clones.length/2+this._items.length-1;else if(b.autoWidth||b.merge){if(b=this._items.length)for(c=this._items[--b].width(),d=this.$element.width();b--&&!(c+=this._items[b].width()+this.settings.margin,c>d););b+=1}else b=b.center?this._items.length-1:this._items.length-b.items;a&&(b-=this._clones.length/
2);return Math.max(b,0)};c.prototype.minimum=function(a){return a?0:this._clones.length/2};c.prototype.items=function(a){if(a===d)return this._items.slice();a=this.normalize(a,!0);return this._items[a]};c.prototype.mergers=function(a){if(a===d)return this._mergers.slice();a=this.normalize(a,!0);return this._mergers[a]};c.prototype.clones=function(a){var c=this._clones.length/2,h=c+this._items.length;return a===d?b.map(this._clones,function(a,b){return 0===b%2?h+b/2:c-(b+1)/2}):b.map(this._clones,
function(b,d){return b===a?0===d%2?h+d/2:c-(d+1)/2:null})};c.prototype.speed=function(a){a!==d&&(this._speed=a);return this._speed};c.prototype.coordinates=function(a){var c=1,h=a-1;if(a===d)return b.map(this._coordinates,b.proxy(function(a,b){return this.coordinates(b)},this));this.settings.center?(this.settings.rtl&&(c=-1,h=a+1),a=this._coordinates[a],a+=(this.width()-a+(this._coordinates[h]||0))/2*c):a=this._coordinates[h]||0;return a=Math.ceil(a)};c.prototype.duration=function(a,b,c){return 0===
c?0:Math.min(Math.max(Math.abs(b-a),1),6)*Math.abs(c||this.settings.smartSpeed)};c.prototype.to=function(a,b){var c=this.current(),d,e=a-this.relative(c);d=(0<e)-(0>e);var f=this._items.length,k=this.minimum(),m=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(e)>f/2&&(e+=-1*d*f),a=c+e,d=((a-k)%f+f)%f+k,d!==a&&d-e<=m&&0<d-e&&(c=d-e,a=d,this.reset(c))):this.settings.rewind?(m+=1,a=(a%m+m)%m):a=Math.max(k,Math.min(m,a));this.speed(this.duration(c,a,b));this.current(a);this.isVisible()&&
this.update()};c.prototype.next=function(a){a=a||!1;this.to(this.relative(this.current())+1,a)};c.prototype.prev=function(a){a=a||!1;this.to(this.relative(this.current())-1,a)};c.prototype.onTransitionEnd=function(a){if(a!==d&&(a.stopPropagation(),(a.target||a.srcElement||a.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating");this.trigger("translated")};c.prototype.viewport=function(){var a;this.options.responsiveBaseElement!==f?a=b(this.options.responsiveBaseElement).width():f.innerWidth?
a=f.innerWidth:e.documentElement&&e.documentElement.clientWidth?a=e.documentElement.clientWidth:console.warn("Can not detect viewport width.");return a};c.prototype.replace=function(a){this.$stage.empty();this._items=[];a&&(a=a instanceof jQuery?a:b(a));this.settings.nestedItemSelector&&(a=a.find("."+this.settings.nestedItemSelector));a.filter(function(){return 1===this.nodeType}).each(b.proxy(function(a,b){b=this.prepare(b);this.$stage.append(b);this._items.push(b);this._mergers.push(1*b.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||
1)},this));this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0);this.invalidate("items")};c.prototype.add=function(a,c){var e=this.relative(this._current);c=c===d?this._items.length:this.normalize(c,!0);a=a instanceof jQuery?a:b(a);this.trigger("add",{content:a,position:c});a=this.prepare(a);0===this._items.length||c===this._items.length?(0===this._items.length&&this.$stage.append(a),0!==this._items.length&&this._items[c-1].after(a),this._items.push(a),this._mergers.push(1*
a.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[c].before(a),this._items.splice(c,0,a),this._mergers.splice(c,0,1*a.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1));this._items[e]&&this.reset(this._items[e].index());this.invalidate("items");this.trigger("added",{content:a,position:c})};c.prototype.remove=function(a){a=this.normalize(a,!0);a!==d&&(this.trigger("remove",{content:this._items[a],position:a}),this._items[a].remove(),this._items.splice(a,
1),this._mergers.splice(a,1),this.invalidate("items"),this.trigger("removed",{content:null,position:a}))};c.prototype.preloadAutoWidthImages=function(a){a.each(b.proxy(function(a,c){this.enter("pre-loading");c=b(c);b(new Image).one("load",b.proxy(function(a){c.attr("src",a.target.src);c.css("opacity",1);this.leave("pre-loading");this.is("pre-loading")||this.is("initializing")||this.refresh()},this)).attr("src",c.attr("src")||c.attr("data-src")||c.attr("data-src-retina"))},this))};c.prototype.destroy=
function(){this.$element.off(".owl.core");this.$stage.off(".owl.core");b(e).off(".owl.core");!1!==this.settings.responsive&&(f.clearTimeout(this.resizeTimer),this.off(f,"resize",this._handlers.onThrottledResize));for(var a in this._plugins)this._plugins[a].destroy();this.$stage.children(".cloned").remove();this.$stage.unwrap();this.$stage.children().contents().unwrap();this.$stage.children().unwrap();this.$stage.remove();this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",
this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")};c.prototype.op=function(a,b,c){var d=this.settings.rtl;switch(b){case "\x3c":return d?a>c:a<c;case "\x3e":return d?a<c:a>c;case "\x3e\x3d":return d?a<=c:a>=c;case "\x3c\x3d":return d?a>=c:a<=c}};c.prototype.on=function(a,b,c,d){a.addEventListener?a.addEventListener(b,c,d):a.attachEvent&&a.attachEvent("on"+b,c)};c.prototype.off=function(a,b,c,d){a.removeEventListener?a.removeEventListener(b,
c,d):a.detachEvent&&a.detachEvent("on"+b,c)};c.prototype.trigger=function(a,d,e){var f={item:{count:this._items.length,index:this.current()}},n=b.camelCase(b.grep(["on",a,e],function(a){return a}).join("-").toLowerCase()),l=b.Event([a,"owl",e||"carousel"].join(".").toLowerCase(),b.extend({relatedTarget:this},f,d));this._supress[a]||(b.each(this._plugins,function(a,b){if(b.onTrigger)b.onTrigger(l)}),this.register({type:c.Type.Event,name:a}),this.$element.trigger(l),this.settings&&"function"===typeof this.settings[n]&&
this.settings[n].call(this,l));return l};c.prototype.enter=function(a){b.each([a].concat(this._states.tags[a]||[]),b.proxy(function(a,b){this._states.current[b]===d&&(this._states.current[b]=0);this._states.current[b]++},this))};c.prototype.leave=function(a){b.each([a].concat(this._states.tags[a]||[]),b.proxy(function(a,b){this._states.current[b]--},this))};c.prototype.register=function(a){if(a.type===c.Type.Event){if(b.event.special[a.name]||(b.event.special[a.name]={}),!b.event.special[a.name].owl){var d=
b.event.special[a.name]._default;b.event.special[a.name]._default=function(a){return!d||!d.apply||a.namespace&&-1!==a.namespace.indexOf("owl")?a.namespace&&-1<a.namespace.indexOf("owl"):d.apply(this,arguments)};b.event.special[a.name].owl=!0}}else a.type===c.Type.State&&(this._states.tags[a.name]=this._states.tags[a.name]?this._states.tags[a.name].concat(a.tags):a.tags,this._states.tags[a.name]=b.grep(this._states.tags[a.name],b.proxy(function(c,d){return b.inArray(c,this._states.tags[a.name])===
d},this)))};c.prototype.suppress=function(a){b.each(a,b.proxy(function(a,b){this._supress[b]=!0},this))};c.prototype.release=function(a){b.each(a,b.proxy(function(a,b){delete this._supress[b]},this))};c.prototype.pointer=function(a){var b={x:null,y:null};a=a.originalEvent||a||f.event;a=a.touches&&a.touches.length?a.touches[0]:a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:a;a.pageX?(b.x=a.pageX,b.y=a.pageY):(b.x=a.clientX,b.y=a.clientY);return b};c.prototype.isNumeric=function(a){return!isNaN(parseFloat(a))};
c.prototype.difference=function(a,b){return{x:a.x-b.x,y:a.y-b.y}};b.fn.owlCarousel=function(a){var d=Array.prototype.slice.call(arguments,1);return this.each(function(){var e=b(this),f=e.data("owl.carousel");f||(f=new c(this,"object"==typeof a&&a),e.data("owl.carousel",f),b.each("next prev to destroy refresh replace add remove".split(" "),function(a,d){f.register({type:c.Type.Event,name:d});f.$element.on(d+".owl.carousel.core",b.proxy(function(a){a.namespace&&a.relatedTarget!==this&&(this.suppress([d]),
f[d].apply(this,[].slice.call(arguments,1)),this.release([d]))},f))}));"string"==typeof a&&"_"!==a.charAt(0)&&f[a].apply(f,d)})};b.fn.owlCarousel.Constructor=c})(window.Zepto||window.jQuery,window,document);
(function(b,f){var e=function(d){this._core=d;this._visible=this._interval=null;this._handlers={"initialized.owl.carousel":b.proxy(function(b){b.namespace&&this._core.settings.autoRefresh&&this.watch()},this)};this._core.options=b.extend({},e.Defaults,this._core.options);this._core.$element.on(this._handlers)};e.Defaults={autoRefresh:!0,autoRefreshInterval:500};e.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=f.setInterval(b.proxy(this.refresh,this),
this._core.settings.autoRefreshInterval))};e.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())};e.prototype.destroy=function(){var b,c;f.clearInterval(this._interval);for(b in this._handlers)this._core.$element.off(b,this._handlers[b]);for(c in Object.getOwnPropertyNames(this))"function"!=typeof this[c]&&(this[c]=null)};
b.fn.owlCarousel.Constructor.Plugins.AutoRefresh=e})(window.Zepto||window.jQuery,window,document);
(function(b,f,e,d){var c=function(a){this._core=a;this._loaded=[];this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":b.proxy(function(a){if(a.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(a.property&&"position"==a.property.name||"initialized"==a.type)){var c=this._core.settings,e=c.center&&Math.ceil(c.items/2)||c.items,f=c.center&&-1*e||0;a=(a.property&&a.property.value!==d?a.property.value:this._core.current())+f;var l=this._core.clones().length,
k=b.proxy(function(a,b){this.load(b)},this);0<c.lazyLoadEager&&(e+=c.lazyLoadEager,c.loop&&(a-=c.lazyLoadEager,e++));for(;f++<e;)this.load(l/2+this._core.relative(a)),l&&b.each(this._core.clones(this._core.relative(a)),k),a++}},this)};this._core.options=b.extend({},c.Defaults,this._core.options);this._core.$element.on(this._handlers)};c.Defaults={lazyLoad:!1,lazyLoadEager:0};c.prototype.load=function(a){var c=(a=this._core.$stage.children().eq(a))&&a.find(".owl-lazy");!c||-1<b.inArray(a.get(0),this._loaded)||
(c.each(b.proxy(function(a,c){var d=b(c),e=1<f.devicePixelRatio&&d.attr("data-src-retina")||d.attr("data-src")||d.attr("data-srcset");this._core.trigger("load",{element:d,url:e},"lazy");d.is("img")?d.one("load.owl.lazy",b.proxy(function(){d.css("opacity",1);this._core.trigger("loaded",{element:d,url:e},"lazy")},this)).attr("src",e):d.is("source")?d.one("load.owl.lazy",b.proxy(function(){this._core.trigger("loaded",{element:d,url:e},"lazy")},this)).attr("srcset",e):(a=new Image,a.onload=b.proxy(function(){d.css({"background-image":'url("'+
e+'")',opacity:"1"});this._core.trigger("loaded",{element:d,url:e},"lazy")},this),a.src=e)},this)),this._loaded.push(a.get(0)))};c.prototype.destroy=function(){var a,b;for(a in this.handlers)this._core.$element.off(a,this.handlers[a]);for(b in Object.getOwnPropertyNames(this))"function"!=typeof this[b]&&(this[b]=null)};b.fn.owlCarousel.Constructor.Plugins.Lazy=c})(window.Zepto||window.jQuery,window,document);
(function(b,f){var e=function(d){this._core=d;this._previousHeight=null;this._handlers={"initialized.owl.carousel refreshed.owl.carousel":b.proxy(function(a){a.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":b.proxy(function(a){a.namespace&&this._core.settings.autoHeight&&"position"===a.property.name&&this.update()},this),"loaded.owl.lazy":b.proxy(function(a){a.namespace&&this._core.settings.autoHeight&&a.element.closest("."+this._core.settings.itemClass).index()===
this._core.current()&&this.update()},this)};this._core.options=b.extend({},e.Defaults,this._core.options);this._core.$element.on(this._handlers);this._intervalId=null;var c=this;b(f).on("load",function(){c._core.settings.autoHeight&&c.update()});b(f).resize(function(){c._core.settings.autoHeight&&(null!=c._intervalId&&clearTimeout(c._intervalId),c._intervalId=setTimeout(function(){c.update()},250))})};e.Defaults={autoHeight:!1,autoHeightClass:"owl-height"};e.prototype.update=function(){var d=this._core._current,
c=d+this._core.settings.items,a=this._core.settings.lazyLoad,d=this._core.$stage.children().toArray().slice(d,c),e=[],c=0;b.each(d,function(a,c){e.push(b(c).height())});c=Math.max.apply(null,e);1>=c&&a&&this._previousHeight&&(c=this._previousHeight);this._previousHeight=c;this._core.$stage.parent().height(c).addClass(this._core.settings.autoHeightClass)};e.prototype.destroy=function(){var b,c;for(b in this._handlers)this._core.$element.off(b,this._handlers[b]);for(c in Object.getOwnPropertyNames(this))"function"!==
typeof this[c]&&(this[c]=null)};b.fn.owlCarousel.Constructor.Plugins.AutoHeight=e})(window.Zepto||window.jQuery,window,document);
(function(b,f,e){var d=function(c){this._core=c;this._videos={};this._playing=null;this._handlers={"initialized.owl.carousel":b.proxy(function(a){a.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":b.proxy(function(a){a.namespace&&this._core.settings.video&&this.isInFullScreen()&&a.preventDefault()},this),"refreshed.owl.carousel":b.proxy(function(a){a.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},
this),"changed.owl.carousel":b.proxy(function(a){a.namespace&&"position"===a.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":b.proxy(function(a){if(a.namespace){var c=b(a.content).find(".owl-video");c.length&&(c.css("display","none"),this.fetch(c,b(a.content)))}},this)};this._core.options=b.extend({},d.Defaults,this._core.options);this._core.$element.on(this._handlers);this._core.$element.on("click.owl.video",".owl-video-play-icon",b.proxy(function(a){this.play(a)},this))};
d.Defaults={video:!1,videoHeight:!1,videoWidth:!1};d.prototype.fetch=function(b,a){var d;b.attr("data-vimeo-id")||b.attr("data-vzaar-id");var e;b.attr("data-vimeo-id")||b.attr("data-youtube-id")||b.attr("data-vzaar-id");var f=b.attr("data-width")||this._core.settings.videoWidth,n=b.attr("data-height")||this._core.settings.videoHeight,l=b.attr("href");if(l){e=l.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/);
if(-1<e[3].indexOf("youtu"))d="youtube";else if(-1<e[3].indexOf("vimeo"))d="vimeo";else if(-1<e[3].indexOf("vzaar"))d="vzaar";else throw Error("Video URL not supported.");e=e[6]}else throw Error("Missing video URL.");this._videos[l]={type:d,id:e,width:f,height:n};a.attr("data-video",l);this.thumbnail(b,this._videos[l])};d.prototype.thumbnail=function(c,a){var d,e,f=a.width&&a.height?"width:"+a.width+"px;height:"+a.height+"px;":"",n=c.find("img"),l="src",k="",m=this._core.settings,q=function(a){d=
m.lazyLoad?b("\x3cdiv/\x3e",{"class":"owl-video-tn "+k,srcType:a}):b("\x3cdiv/\x3e",{"class":"owl-video-tn",style:"opacity:1;background-image:url("+a+")"});c.after(d);c.after('\x3cdiv class\x3d"owl-video-play-icon"\x3e\x3c/div\x3e')};c.wrap(b("\x3cdiv/\x3e",{"class":"owl-video-wrapper",style:f}));this._core.settings.lazyLoad&&(l="data-src",k="owl-lazy");if(n.length)return q(n.attr(l)),n.remove(),!1;"youtube"===a.type?(e="//img.youtube.com/vi/"+a.id+"/hqdefault.jpg",q(e)):"vimeo"===a.type?b.ajax({type:"GET",
url:"//vimeo.com/api/v2/video/"+a.id+".json",jsonp:"callback",dataType:"jsonp",success:function(a){e=a[0].thumbnail_large;q(e)}}):"vzaar"===a.type&&b.ajax({type:"GET",url:"//vzaar.com/api/videos/"+a.id+".json",jsonp:"callback",dataType:"jsonp",success:function(a){e=a.framegrab_url;q(e)}})};d.prototype.stop=function(){this._core.trigger("stop",null,"video");this._playing.find(".owl-video-frame").remove();this._playing.removeClass("owl-video-playing");this._playing=null;this._core.leave("playing");
this._core.trigger("stopped",null,"video")};d.prototype.play=function(c){c=b(c.target).closest("."+this._core.settings.itemClass);var a=this._videos[c.attr("data-video")],d=a.width||"100%",e=a.height||this._core.$stage.height(),f;this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),c=this._core.items(this._core.relative(c.index())),this._core.reset(c.index()),f=b('\x3ciframe frameborder\x3d"0" allowfullscreen mozallowfullscreen webkitAllowFullScreen \x3e\x3c/iframe\x3e'),
f.attr("height",e),f.attr("width",d),"youtube"===a.type?f.attr("src","//www.youtube.com/embed/"+a.id+"?autoplay\x3d1\x26rel\x3d0\x26v\x3d"+a.id):"vimeo"===a.type?f.attr("src","//player.vimeo.com/video/"+a.id+"?autoplay\x3d1"):"vzaar"===a.type&&f.attr("src","//view.vzaar.com/"+a.id+"/player?autoplay\x3dtrue"),iframe=b(f).wrap('\x3cdiv class\x3d"owl-video-frame" /\x3e').insertAfter(c.find(".owl-video")),this._playing=c.addClass("owl-video-playing"))};d.prototype.isInFullScreen=function(){var c=e.fullscreenElement||
e.mozFullScreenElement||e.webkitFullscreenElement;return c&&b(c).parent().hasClass("owl-video-frame")};d.prototype.destroy=function(){var b,a;this._core.$element.off("click.owl.video");for(b in this._handlers)this._core.$element.off(b,this._handlers[b]);for(a in Object.getOwnPropertyNames(this))"function"!=typeof this[a]&&(this[a]=null)};b.fn.owlCarousel.Constructor.Plugins.Video=d})(window.Zepto||window.jQuery,window,document);
(function(b,f,e,d){var c=function(a){this.core=a;this.core.options=b.extend({},c.Defaults,this.core.options);this.swapping=!0;this.next=this.previous=d;this.handlers={"change.owl.carousel":b.proxy(function(a){a.namespace&&"position"==a.property.name&&(this.previous=this.core.current(),this.next=a.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":b.proxy(function(a){a.namespace&&(this.swapping="translated"==a.type)},this),"translate.owl.carousel":b.proxy(function(a){a.namespace&&
this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)};this.core.$element.on(this.handlers)};c.Defaults={animateOut:!1,animateIn:!1};c.prototype.swap=function(){if(1===this.core.settings.items&&b.support.animation&&b.support.transition){this.core.speed(0);var a,c=b.proxy(this.clear,this),d=this.core.$stage.children().eq(this.previous),e=this.core.$stage.children().eq(this.next),f=this.core.settings.animateIn,l=this.core.settings.animateOut;this.core.current()!==
this.previous&&(l&&(a=this.core.coordinates(this.previous)-this.core.coordinates(this.next),d.one(b.support.animation.end,c).css({left:a+"px"}).addClass("animated owl-animated-out").addClass(l)),f&&e.one(b.support.animation.end,c).addClass("animated owl-animated-in").addClass(f))}};c.prototype.clear=function(a){b(a.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut);this.core.onTransitionEnd()};
c.prototype.destroy=function(){var a,b;for(a in this.handlers)this.core.$element.off(a,this.handlers[a]);for(b in Object.getOwnPropertyNames(this))"function"!=typeof this[b]&&(this[b]=null)};b.fn.owlCarousel.Constructor.Plugins.Animate=c})(window.Zepto||window.jQuery,window,document);
(function(b,f,e){var d=function(c){this._core=c;this._call=null;this._timeout=this._time=0;this._paused=!0;this._handlers={"changed.owl.carousel":b.proxy(function(a){a.namespace&&"settings"===a.property.name?this._core.settings.autoplay?this.play():this.stop():a.namespace&&"position"===a.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":b.proxy(function(a){a.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":b.proxy(function(a,b,c){a.namespace&&
this.play(b,c)},this),"stop.owl.autoplay":b.proxy(function(a){a.namespace&&this.stop()},this),"mouseover.owl.autoplay":b.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":b.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":b.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":b.proxy(function(){this._core.settings.autoplayHoverPause&&
this.play()},this)};this._core.$element.on(this._handlers);this._core.options=b.extend({},d.Defaults,this._core.options)};d.Defaults={autoplay:!1,autoplayTimeout:5E3,autoplayHoverPause:!1,autoplaySpeed:!1};d.prototype._next=function(c){this._call=f.setTimeout(b.proxy(this._next,this,c),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read());this._core.is("interacting")||e.hidden||this._core.next(c||this._core.settings.autoplaySpeed)};d.prototype.read=function(){return(new Date).getTime()-
this._time};d.prototype.play=function(c,a){var d;this._core.is("rotating")||this._core.enter("rotating");c=c||this._core.settings.autoplayTimeout;d=Math.min(this._time%(this._timeout||c),c);this._paused?(this._time=this.read(),this._paused=!1):f.clearTimeout(this._call);this._time+=this.read()%c-d;this._timeout=c;this._call=f.setTimeout(b.proxy(this._next,this,a),c-d)};d.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,f.clearTimeout(this._call),this._core.leave("rotating"))};
d.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,f.clearTimeout(this._call))};d.prototype.destroy=function(){var b,a;this.stop();for(b in this._handlers)this._core.$element.off(b,this._handlers[b]);for(a in Object.getOwnPropertyNames(this))"function"!=typeof this[a]&&(this[a]=null)};b.fn.owlCarousel.Constructor.Plugins.autoplay=d})(window.Zepto||window.jQuery,window,document);
(function(b){var f=function(e){this._core=e;this._initialized=!1;this._pages=[];this._controls={};this._templates=[];this.$element=this._core.$element;this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to};this._handlers={"prepared.owl.carousel":b.proxy(function(d){d.namespace&&this._core.settings.dotsData&&this._templates.push('\x3cdiv class\x3d"'+this._core.settings.dotClass+'"\x3e'+b(d.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"\x3c/div\x3e")},this),
"added.owl.carousel":b.proxy(function(b){b.namespace&&this._core.settings.dotsData&&this._templates.splice(b.position,0,this._templates.pop())},this),"remove.owl.carousel":b.proxy(function(b){b.namespace&&this._core.settings.dotsData&&this._templates.splice(b.position,1)},this),"changed.owl.carousel":b.proxy(function(b){b.namespace&&"position"==b.property.name&&this.draw()},this),"initialized.owl.carousel":b.proxy(function(b){b.namespace&&!this._initialized&&(this._core.trigger("initialize",null,
"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":b.proxy(function(b){b.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)};this._core.options=b.extend({},f.Defaults,this._core.options);this.$element.on(this._handlers)};f.Defaults={nav:!1,navText:['\x3cspan aria-label\x3d"Previous"\x3e\x26#x2039;\x3c/span\x3e',
'\x3cspan aria-label\x3d"Next"\x3e\x26#x203a;\x3c/span\x3e'],navSpeed:!1,navElement:'button type\x3d"button" role\x3d"presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1};f.prototype.initialize=function(){var e,d=this._core.settings;this._controls.$relative=(d.navContainer?b(d.navContainer):b("\x3cdiv\x3e").addClass(d.navContainerClass).appendTo(this.$element)).addClass("disabled");
this._controls.$previous=b("\x3c"+d.navElement+"\x3e").addClass(d.navClass[0]).html(d.navText[0]).prependTo(this._controls.$relative).on("click",b.proxy(function(){this.prev(d.navSpeed)},this));this._controls.$next=b("\x3c"+d.navElement+"\x3e").addClass(d.navClass[1]).html(d.navText[1]).appendTo(this._controls.$relative).on("click",b.proxy(function(){this.next(d.navSpeed)},this));d.dotsData||(this._templates=[b('\x3cbutton role\x3d"button"\x3e').addClass(d.dotClass).append(b("\x3cspan\x3e")).prop("outerHTML")]);
this._controls.$absolute=(d.dotsContainer?b(d.dotsContainer):b("\x3cdiv\x3e").addClass(d.dotsClass).appendTo(this.$element)).addClass("disabled");this._controls.$absolute.on("click","button",b.proxy(function(c){var a=b(c.target).parent().is(this._controls.$absolute)?b(c.target).index():b(c.target).parent().index();c.preventDefault();this.to(a,d.dotsSpeed)},this));for(e in this._overrides)this._core[e]=b.proxy(this[e],this)};f.prototype.destroy=function(){var b,d,c,a,f;f=this._core.settings;for(b in this._handlers)this.$element.off(b,
this._handlers[b]);for(d in this._controls)"$relative"===d&&f.navContainer?this._controls[d].html(""):this._controls[d].remove();for(a in this.overides)this._core[a]=this._overrides[a];for(c in Object.getOwnPropertyNames(this))"function"!=typeof this[c]&&(this[c]=null)};f.prototype.update=function(){var b,d,c,a=this._core.clones().length/2,f=a+this._core.items().length,h=this._core.maximum(!0);b=this._core.settings;var p=b.center||b.dotsData?1:b.dotsEach||b.items;"page"!==b.slideBy&&(b.slideBy=Math.min(b.slideBy,
b.items));if(b.dots||"page"==b.slideBy)for(this._pages=[],b=a,c=d=0;b<f;b++){if(d>=p||0===d){this._pages.push({start:Math.min(h,b-a),end:b-a+p-1});if(Math.min(h,b-a)===h)break;d=0;++c}d+=this._core.mergers(this._core.relative(b))}};f.prototype.draw=function(){var e,d=this._core.settings;e=this._core.items().length<=d.items;var c=this._core.relative(this._core.current()),a=d.loop||d.rewind;this._controls.$relative.toggleClass("disabled",!d.nav||e);d.nav&&(this._controls.$previous.toggleClass("disabled",
!a&&c<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!a&&c>=this._core.maximum(!0)));this._controls.$absolute.toggleClass("disabled",!d.dots||e);d.dots&&(e=this._pages.length-this._controls.$absolute.children().length,d.dotsData&&0!==e?this._controls.$absolute.html(this._templates.join("")):0<e?this._controls.$absolute.append(Array(e+1).join(this._templates[0])):0>e&&this._controls.$absolute.children().slice(e).remove(),this._controls.$absolute.find(".active").removeClass("active"),
this._controls.$absolute.children().eq(b.inArray(this.current(),this._pages)).addClass("active"))};f.prototype.onTrigger=function(e){var d=this._core.settings;e.page={index:b.inArray(this.current(),this._pages),count:this._pages.length,size:d&&(d.center||d.autoWidth||d.dotsData?1:d.dotsEach||d.items)}};f.prototype.current=function(){var e=this._core.relative(this._core.current());return b.grep(this._pages,b.proxy(function(b){return b.start<=e&&b.end>=e},this)).pop()};f.prototype.getPosition=function(e){var d,
c;c=this._core.settings;"page"==c.slideBy?(d=b.inArray(this.current(),this._pages),c=this._pages.length,e?++d:--d,d=this._pages[(d%c+c)%c].start):(d=this._core.relative(this._core.current()),this._core.items(),e?d+=c.slideBy:d-=c.slideBy);return d};f.prototype.next=function(e){b.proxy(this._overrides.to,this._core)(this.getPosition(!0),e)};f.prototype.prev=function(e){b.proxy(this._overrides.to,this._core)(this.getPosition(!1),e)};f.prototype.to=function(e,d,c){!c&&this._pages.length?(c=this._pages.length,
b.proxy(this._overrides.to,this._core)(this._pages[(e%c+c)%c].start,d)):b.proxy(this._overrides.to,this._core)(e,d)};b.fn.owlCarousel.Constructor.Plugins.Navigation=f})(window.Zepto||window.jQuery,window,document);
(function(b,f,e,d){var c=function(a){this._core=a;this._hashes={};this.$element=this._core.$element;this._handlers={"initialized.owl.carousel":b.proxy(function(a){a.namespace&&"URLHash"===this._core.settings.startPosition&&b(f).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":b.proxy(function(a){if(a.namespace){var c=b(a.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");c&&(this._hashes[c]=a.content)}},this),"changed.owl.carousel":b.proxy(function(a){if(a.namespace&&
"position"===a.property.name){var c=this._core.items(this._core.relative(this._core.current()));(a=b.map(this._hashes,function(a,b){return a===c?b:null}).join())&&f.location.hash.slice(1)!==a&&(f.location.hash=a)}},this)};this._core.options=b.extend({},c.Defaults,this._core.options);this.$element.on(this._handlers);b(f).on("hashchange.owl.navigation",b.proxy(function(){var a=f.location.hash.substring(1),b=this._core.$stage.children(),a=this._hashes[a]&&b.index(this._hashes[a]);a!==d&&a!==this._core.current()&&
this._core.to(this._core.relative(a),!1,!0)},this))};c.Defaults={URLhashListener:!1};c.prototype.destroy=function(){var a,c;b(f).off("hashchange.owl.navigation");for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(c in Object.getOwnPropertyNames(this))"function"!=typeof this[c]&&(this[c]=null)};b.fn.owlCarousel.Constructor.Plugins.Hash=c})(window.Zepto||window.jQuery,window,document);
(function(b,f,e,d){function c(c,e){var f=!1,h=c.charAt(0).toUpperCase()+c.slice(1);b.each((c+" "+g.join(h+" ")+h).split(" "),function(b,c){if(a[c]!==d)return f=e?c:!0,!1});return f}var a=b("\x3csupport\x3e").get(0).style,g=["Webkit","Moz","O","ms"];f={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"};e={WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"};var h=
{csstransforms:function(){return!!c("transform")},csstransforms3d:function(){return!!c("perspective")},csstransitions:function(){return!!c("transition")},cssanimations:function(){return!!c("animation")}};h.csstransitions()&&(b.support.transition=new String(c("transition",!0)),b.support.transition.end=f[b.support.transition]);h.cssanimations()&&(b.support.animation=new String(c("animation",!0)),b.support.animation.end=e[b.support.animation]);h.csstransforms()&&(b.support.transform=new String(c("transform",
!0)),b.support.transform3d=h.csstransforms3d())})(window.Zepto||window.jQuery,window,document);(function(b){var f={isTracked:!1,defaultValue:0,processPixelRatioGA:function(){if(!this.isTracked){var e={devicePixelRatio:b.devicePixelRatio||this.defaultValue};"function"===typeof b.gaTrackData&&(b.gaTrackData(e),this.isTracked=!0)}},send:function(){this.processPixelRatioGA()},reset:function(){this.isTracked=!1}};"undefined"!==typeof b&&(b.PixelRatioTracker=f)})("undefined"!==typeof window?window:this);