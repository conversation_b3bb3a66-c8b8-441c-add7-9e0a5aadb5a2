(function(){function a(a,e){$.cookie(a,e,{domain:".sahibinden.com",path:"/"})}$(function(){function c(b,a){switch(b.suggestionType){case "Category":return b.breadcrumb.push(b.name),b.breadcrumb.join(" \x3e ");case "ApartmentComplex":return b.name.trim()+" - "+b.locationData.townName+", "+b.locationData.cityName;case "POI":return b.name.trim()+(a?" - \x3cspan class\x3d'suggestion-address'\x3e"+b.locationData.cityAndTownName+"\x3c/span\x3e":"");case "S360":return b.showingText+(a?'\x3cimg src\x3d"'+
b.iconURL+'" /\x3e':"");case "Store":case "Project":return b.name.trim();case "Brand":return b.name.trim();default:return b.phrase.trim()}}function e(b,a){var f=0,c=0,e=0,l=!0,g=!1,p=!1,m=!1,q=!1,r=!1,u=!1;a.forEach(function(b){"Store"==b.value.suggestionType&&f++;"Project"==b.value.suggestionType&&c++;"S360"==b.value.suggestionType&&e++});b.addClass("searchSuggestion");$.each(a,function(a,d){g||"Category"!=d.value.suggestionType?m||"ApartmentComplex"!=d.value.suggestionType?q||"POI"!=d.value.suggestionType?
p||"Store"!=d.value.suggestionType?r||"Project"!=d.value.suggestionType?!u&&"Phrase"==d.value.suggestionType&&e&&(a="\x3cspan\x3e"+t.phrase+"\x3c/span\x3e",$('\x3cli class\x3d"categorySeparator"\x3e\x3c/li\x3e').append(a).appendTo(b),u=l=!0):(a="\x3cspan\x3e"+t.projects+"\x3c/span\x3e",$('\x3cli class\x3d"categorySeparator"\x3e\x3c/li\x3e').append(a).appendTo(b),4<c&&(a="\x3cp\x3e"+_e("search.searchByKeyword.moreThanFiveProjects")+'\x3c/p\x3e\x3cdiv class\x3d"linkWrapper"\x3e\x3cspan class\x3d"show-all-items show-all-projects"\x3e'+
_e("search.searchByKeyword.showAllProjects")+"\x3c/span\x3e\x3c/div\x3e",$('\x3cli class\x3d"contentInfo"\x3e\x3c/li\x3e').append(a).appendTo(b)),r=l=!0):(a="\x3cspan\x3e"+t.stores+"\x3c/span\x3e",$('\x3cli class\x3d"storeSeparator"\x3e\x3c/li\x3e').append(a).appendTo(b),4<f&&(a="\x3cp\x3e"+_e("search.searchByKeyword.moreThanFiveStores")+'\x3c/p\x3e\x3cdiv class\x3d"linkWrapper"\x3e\x3cspan class\x3d"show-all-items show-all-stores"\x3e'+_e("search.searchByKeyword.showAllStores")+"\x3c/span\x3e\x3c/div\x3e",
$('\x3cli class\x3d"contentInfo"\x3e\x3c/li\x3e').append(a).appendTo(b)),p=l=!0):(a="\x3cspan\x3e"+t.poi+"\x3c/span\x3e",$('\x3cli class\x3d"categorySeparator"\x3e\x3c/li\x3e').append(a).appendTo(b),q=l=!0):(a="\x3cspan\x3e"+t.apartmentComplex+"\x3c/span\x3e",$('\x3cli class\x3d"categorySeparator"\x3e\x3c/li\x3e').append(a).appendTo(b),m=l=!0):(a="\x3cspan\x3e"+t.categories+"\x3c/span\x3e",$('\x3cli class\x3d"categorySeparator"\x3e\x3c/li\x3e').append(a).appendTo(b),g=l=!0);if(!(4<f&&"Store"==d.value.suggestionType||
4<c&&"Project"==d.value.suggestionType)){a=l;var n=k.val().replace("I","\u0131").toLowerCase().replace(/[\u011fg]/g,"[\u011fg]").replace(/[\u00fcu]/g,"[\u00fcu]").replace(/[\u015fs]/g,"[\u015fs]").replace(/[i\u0131]/g,"[i\u0131]").replace(/[\u00f6o]/g,"[\u00f6o]").replace(/[c\u00e7]/g,"[\u00e7c]"),n=d.label.replace(new RegExp("((^| )"+n+")","gi"),"\x3cspan\x3e$1\x3c/span\x3e"),h="Category"===d.value.suggestionType?'\x3cspan class\x3d"category-title"\x3e'+d.value.name+"\x3c/span\x3e":"";if(d.value.categoryDetail){for(h=
d.value.categoryDetail.defaultIconUrl;h.includes("/");)h=h.replace("/","");h='\x3ca href\x3d"" class\x3d"'+h.substring(0,h.indexOf("."))+'"\x3e'}else h=h?'\x3ca href\x3d"" title\x3d"'+d.label+'"\x3e'+h:'\x3ca href\x3d""\x3e';if(d.value.brand)var v=d.value,n='\x3cspan class\x3d"search-suggestion__brand"\x3e'+(v.imageUrl?'\x3cimg src\x3d"'+v.imageUrl+'" class\x3d"search-suggestion__brand-icon" onerror\x3d"this.style.display\x3d\'none\'"/\x3e':"")+'\x3cspan class\x3d"search-suggestion__brand-label"\x3e'+
n+'\x3c/span\x3e\x3cspan class\x3d"search-suggestion__brand-badge"\x3e'+_e("search.suggestion.brand")+"\x3c/span\x3e\x3c/span\x3e";n=h+n+(d.desc?"\x3cbr/\x3e\x3cem\x3e"+d.desc+"\x3c/em\x3e":"")+"\x3c/a\x3e";h="";switch(d.value.suggestionType){case "Category":case "Project":h="searchSuggestionCategory";break;case "Store":h="searchSuggestionStore";break;case "ApartmentComplex":h="searchSuggestionApartmentComplex";break;case "POI":h="searchSuggestionPoi";break;case "S360":h="searchSuggestionS360"}$('\x3cli class\x3d"'+
(h+(a?" first-child":""))+'"\x3e\x3c/li\x3e').data("ui-autocomplete-item",d).append(n).appendTo(b);l&&(l=!1)}})}function f(b,a){b=encodeURIComponent(b);b=m.replace("{phrase}",b);a&&(b+="\x26suggestedByAdmin\x3dtrue");window.location.href=b}function l(b){return b.trim().replace("I","\u0131").toLowerCase().replace(/\u011f/g,"g").replace(/\u00fc/g,"u").replace(/\u015f/g,"s").replace(/\u0131/g,"i").replace(/\u00f6/g,"o").replace(/\u00e7/g,"c").split(/\b\s+/).sort().join("-")}var p='#searchSuggestionLoadingWrapper{position:relative}#searchSuggestionLoading{display:none}.searchSuggestion.ui-autocomplete{background:0;z-index:9999999!important;position:absolute;top:38px;left:5px;width:384px;border:0;background-color:#fff;box-shadow:1px 1px 4px 1px #666;border-radius:2px}.searchSuggestion.ui-menu .ui-menu-item{display:block;clear:left}.searchSuggestion.ui-menu .ui-menu-item a{font-size:11px;border:0;background:0;font-weight:700;float:none;display:block;color:#999;text-shadow:none;line-height:16px;padding:6px 24px 6px 20px;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.searchSuggestion.ui-menu .ui-menu-item a span:not(.suggestion-address){color:#00339f;font-weight:bold}.searchSuggestion.ui-menu .ui-menu-item a span.suggestion-address{font-size:9px}.searchSuggestion.ui-menu .ui-menu-item a span.category-title{display:block}.searchSuggestion.ui-menu .ui-menu-item a strong{color:#00339f;display:block}.searchSuggestion.ui-menu .ui-menu-item a em{color:#a8a8a8;font-size:10px;font-weight:normal;font-style:normal}.searchSuggestion.ui-menu .ui-menu-item .search-suggestion__brand{display:flex;align-items:center;width:100%}.searchSuggestion.ui-menu .ui-menu-item .search-suggestion__brand-icon{width:20px;height:20px;margin-right:5px}.searchSuggestion.ui-menu .ui-menu-item .search-suggestion__brand-label{flex-grow:1}.searchSuggestion.ui-menu .ui-menu-item .search-suggestion__brand-badge{color:#999!important}.searchSuggestion.ui-menu .ui-menu-item.searchSuggestionS360 a{zoom:1;padding:3px 10px 3px 43px;line-height:22px;position:relative}.searchSuggestion.ui-menu .ui-menu-item.searchSuggestionS360 a:before,.searchSuggestion.ui-menu .ui-menu-item.searchSuggestionS360 a:after{display:table;content:"";zoom:1}.searchSuggestion.ui-menu .ui-menu-item.searchSuggestionS360 a:after{clear:both}.searchSuggestion.ui-menu .ui-menu-item.searchSuggestionS360 a img{position:absolute;left:16px;top:5px;width:20px;height:20px}.searchSuggestion.ui-menu .searchSuggestionCategory a{direction:rtl}.searchSuggestion.ui-menu .searchSuggestionStore a{color:#00339f;font-weight:bold}.searchSuggestion.ui-menu .categorySeparator,.searchSuggestion.ui-menu .storeSeparator{position:relative;margin-bottom:10px;top:0;clear:left;border-top:0;text-align:center;border-bottom:1px solid #d8d8d8}.searchSuggestion.ui-menu .categorySeparator span,.searchSuggestion.ui-menu .storeSeparator span{position:relative;padding:0 10px;background-color:#fff;display:inline;font-size:12px;top:6px}.searchSuggestion.ui-menu .contentInfo{padding:15px 24px 15px 14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;font-size:11px;font-weight:bold}.searchSuggestion.ui-menu .contentInfo .linkWrapper{text-align:right}.searchSuggestion.ui-menu .contentInfo .linkWrapper span{display:inline-block;margin-top:10px;padding:0;cursor:pointer;color:#039}.searchSuggestion.ui-menu .contentInfo .linkWrapper span:hover{text-decoration:underline}.searchSuggestion.ui-menu .noSuggestion{color:#999;font-size:11px;text-align:center;font-style:italic;border-top:0;padding:15px 0}.searchSuggestion.ui-menu .ui-menu-item:first-child,.searchSuggestion.ui-menu .ui-menu-item.first-child{border-top:0}.searchSuggestion.ui-menu .ui-menu-item a.ui-state-focus,.searchSuggestion.ui-menu .ui-menu-item a.ui-state-active{text-decoration:none;background-color:#eee;border-radius:0!important}.ui-helper-hidden-accessible{display:none}.ui-autocomplete{padding:10px 0!important}',
q=-1<p.indexOf("[[INCLUDE"),g=$("meta[http-equiv\x3dContent-Language]").attr("content");null==g&&"undefined"!=typeof documentLanguage&&(g=documentLanguage);null==g&&(g="tr");var m="tr"==g?"/kelime-ile-arama?query_text\x3d{phrase}":"/en/kelime-ile-arama?query_text\x3d{phrase}",u="tr"==g?"/arama/ara?query_text\x3d{phrase}\x26category\x3d{categoryId}":"/search/do?query_text\x3d{phrase}\x26category\x3d{categoryId}",x="tr"==g?"/haritada-emlak-arama?searchPoiItem\x3d{searchPoiItem}\x26category\x3d3518":
"/search-map?searchPoiItem\x3d{searchPoiItem}\x26category\x3d3518",y="tr"===g?"/arama/ara?address_country\x3d{countryId}\x26address_quarter\x3d{quarterId}\x26a103651\x3d1139073\x26language\x3dtr\x26address_apartmentComplex\x3d{apartmentComplexId}\x26category\x3d3613\x26address_district\x3d{districtId}\x26address_town\x3d{townId}\x26address_city\x3d{cityId}":"/search/do?address_country\x3d{countryId}\x26address_quarter\x3d{quarterId}\x26a103651\x3d1139073\x26language\x3den\x26address_apartmentComplex\x3d{apartmentComplexId}\x26category\x3d3613\x26address_district\x3d{districtId}\x26address_town\x3d{townId}\x26address_city\x3d{cityId}",
t={categories:"tr"==g?"Kategoriler":"Categories",stores:"tr"==g?"Ma\u011fazalar":"Stores",apartmentComplex:"tr"==g?"Siteler":"Apartment Complex",poi:"tr"==g?"Araman\u0131z\u0131n Yak\u0131n\u0131ndaki Emlak \u0130lanlar\u0131":"Nearby Real Estates",projects:"tr"==g?"Projeler":"Projects",360:"tr"==g?"360 Servisleri":"360 Services",phrase:"tr"==g?"Araman\u0131za Uygun \u00d6neriler":"Phrases"},k=$("#searchText:visible"),r,v=function(b){var a;$("ul.searchSuggestion li a").each(function(){if(l($(this).text())===
b)return a=$(this),!1});return a},g=function(b){var a=l(k.val());if(a)a=v(a),b.preventDefault(),a?a.trigger("click"):(window.setSearchTypeCookie("TEXT","ENTER"),b.target.submit());else return k.hasClass("focused")||(k.focus(),$("#searchSuggestionForm button").attr("disabled","disabled")),b.preventDefault(),!1},w=function(a,d){var f={action:a};if("SEARCHED"!==a){var c=[];$("#search_cats \x3e ul li").each(function(){var a=$(this).attr("data-categorybreadcrumbid");c.push(a)});a=$("#categoryId").val();
Object.assign(f,{searchResultCategoryLevel0:c[0],searchResultCategoryLevel1:c[1],searchResultCategoryLevel2:c[2],searchResultCategoryLevel3:c[3],searchResultCategoryLevel4:c[4],searchResultCategoryLevel5:c[5],searchResultCategoryLevel6:c[6],categoryId:a})}SearchFunnelEdrHelper.postEdr(f,d)},z=function(a){var d=l(k.val());0!==d.length&&(d=v(d),a.preventDefault(),d?d.trigger("click"):(window.setSearchTypeCookie("TEXT","ENTER"),w("SEARCHED",{queryText:k.val(),searchType:"SEARCHBAR_TEXT_SEARCH"}),f(k.val(),
!1)))};0!==k.length&&(k.attr("autocomplete","off"),$.ajaxSetup({cache:!1}),k.before('\x3cdiv id\x3d"searchSuggestionLoadingWrapper"\x3e\x3cdiv id\x3d"searchSuggestionLoading"\x3e\x3c/div\x3e\x3c/div\x3e'),q?document.createStyleSheet?document.createStyleSheet("/resources/css/searchSuggestion/searchSuggestion.styl.css"):$("head").append($('\x3clink rel\x3d"stylesheet" type\x3d"text/css" href\x3d"/resources/css/searchSuggestion/searchSuggestion.styl.css"/\x3e')):(q=location.protocol,"https:"==q&&(p=
p.replace("http:",q)),$("head").append($('\x3cstyle type\x3d"text/css"\x3e'+p+"\x3c/style\x3e")),m="https://www.sahibinden.com"+m,u="https://www.sahibinden.com"+u),k.autocomplete({source:function(a,d){$("#searchSuggestionLoading").show();$.ajax({url:"/ajax/search/phrase",dataType:"json",data:{q:a.term,categoryId:0},success:function(a){$("#searchSuggestionLoading").hide();var b=a.phrasesSuggestedByAdmin;r=null;d($.map(a.items,function(a){a.suggestedByAdmin=b;var d=c(a,!0),f;a:switch(a.suggestionType){case "Category":case "Project":f=
a.company;break a;default:f=null}return{label:d,desc:f,value:a}}))}})},minLength:2,delay:500,focus:function(a,d){if(/^key/.test(a.originalEvent.originalEvent.type))return a=d.item.value,k.val(c(a,!1)),r=a,!1},select:function(b,d){b.preventDefault();r=b=d.item.value;switch(b.suggestionType){case "Category":"SYNTHETIC_CATEGORY"===b.categorySuggestionType&&b.webUrl?(window.setSearchTypeCookie("TEXT","SYNTHETIC_CATEGORY"),window.location.href=b.webUrl):(window.setSearchTypeCookie("TEXT","CATEGORY"),d=
u.replace("{phrase}","").replace("{categoryId}",b.categoryId),window.location.href=d);break;case "Store":window.setSearchTypeCookie("TEXT","STORE");d="https://{url}.sahibinden.com".replace("{url}",b.url);window.location.href=d;break;case "ApartmentComplex":b=b.locationData;d=y.replace("{countryId}",b.countryId).replace("{quarterId}",b.quarterId).replace("{districtId}",b.districtId).replace("{townId}",b.townId).replace("{cityId}",b.cityId).replace("{apartmentComplexId}",b.apartmentComplexId);window.location.href=
d;break;case "POI":window.setSearchTypeCookie("TEXT","POINT_OF_INTEREST");d=x.replace("{searchPoiItem}",b.id);window.location.href=d;break;case "Project":window.setSearchTypeCookie("TEXT","PROJECT");d="https://www.sahibinden.com{url}".replace("{url}",b.url);window.location.href=d;break;case "S360":window.setSearchTypeCookie("TEXT","S360");d="https://www.sahibinden.com{url}".replace("{url}",b.url);a("ss-s360-s",b.edrInfo.sourcePage);a("ss-s360-p",b.edrInfo.page);a("ss-s360-a",b.edrInfo.action);window.location.href=
d;break;case "PHRASE":window.setSearchTypeCookie("TEXT","PHRASE");d=m.replace("{url}",b.url);window.location.href=d;break;case "Brand":window.setSearchTypeCookie("TEXT","BRAND");window.location.href="/"+b.url;break;default:window.setSearchTypeCookie("TEXT",r?"SUGGESTION":"ENTER"),w("SEARCHED",{queryText:b.phrase,searchType:"SUGGESTION_SEARCH"}),f(b.phrase,b.suggestedByAdmin)}},open:function(){$(".ui-autocomplete").css("z-index",2E4)},close:function(){$(".ui-autocomplete").css("z-index",-1)}}).data("ui-autocomplete")._renderMenu=
e,$(k).keyup(function(){l(k.val())?$("#searchSuggestionForm button").removeAttr("disabled"):$("#searchSuggestionForm button").attr("disabled","disabled")}).focus(function(){l(k.val())?$("#searchSuggestionForm button").removeAttr("disabled"):$("#searchSuggestionForm button").attr("disabled","disabled")}).focusout(function(){$("#searchSuggestionForm button").removeAttr("disabled")}),$("body").on("click",".show-all-items",function(a){a.preventDefault();a=encodeURIComponent(k.val());var d=$(this).hasClass("show-all-stores")?
"\x26type\x3dStore":$(this).hasClass("show-all-projects")?"\x26type\x3dProject":"";window.location.href=m.replace("{phrase}",a)+d}),$("body").on("submit","#searchSuggestionForm",g),$("body").on("click","#searchSuggestionForm button",z))})})();$(function(){function a(a,c,e){a&&c&&(a=a+"/"+c+"/"+(e||window.location.queryString.viewType||"CLASSIC"),c=new Date,c.setTime(c.getTime()+9E5),cookieUtils.setCookie("searchType",a.toUpperCase(),c))}function c(c){var e=c.closest("[data-search-type]").data("searchType")||cookieUtils.readCookie("searchType");c=c.closest("[data-search-view-type]").data("searchViewType");e=(e||"").split("/");a(e[0],e[1],c)}function e(){$("[data-search-type]").find("a, button").click(function(){c($(this))})}window.setSearchTypeCookie=
a;window.setSearchTypeCookieWithElement=c;window.readSearchTypeCookie=function(){var a=cookieUtils.readCookie("searchType");if(!a)return{};var a=a.split("/"),c={};a[0]&&(c.searchTypePrimary=a[0]);a[1]&&(c.searchTypeSecondary=a[1]);a[2]&&(c.searchTypeView=a[2]);return c};window.initSearchTypeCookieHandler=e;e()});var cookieUtils={localStorageEnabled:function(){try{return localStorage.setItem("test","test"),localStorage.getItem("test"),!0}catch(a){return!1}}(),setStorage:function(a,c,e){this.localStorageEnabled?window.localStorage[a]=c:(e=e?{expires:e}:null)?$.cookie(a,c,e):$.cookie(a,c)},setCookie:function(a,c,e){var f=new Date;e?f=e:f.setTime(f.getTime()+9E5);document.cookie=a+"\x3d"+c+";expires\x3d"+f.toGMTString()+";domain\x3d.sahibinden.com;path\x3d/"},readCookie:function(a){a+="\x3d";for(var c=document.cookie.split(";"),
e=0,f;e<c.length;e++){for(f=c[e];" "==f.charAt(0);)f=f.substring(1,f.length);if(0===f.indexOf(a))return f.substring(a.length,f.length)}return null},getStorage:function(a){return this.localStorageEnabled?window.localStorage[a]:$.cookie(a)},removeCookie:function(a){a&&(document.cookie=a+"\x3d; expires\x3dThu, 01 Jan 1970 00:00:01 GMT; domain\x3d.sahibinden.com; path\x3d/")},removeStorage:function(a){if(this.localStorageEnabled)return window.localStorage.removeItem(a);$.removeCookie(a)},setSessionStorage:function(a,
c){window.sessionStorage[a]=c},getSessionStorage:function(a){return window.sessionStorage[a]},removeSessionStorage:function(a){return window.sessionStorage.removeItem(a)}};window.cookieUtils=cookieUtils;window.globalGenerateGUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var c=16*Math.random()|0;return("x"===a?c:c&3|8).toString(16)})};var SearchFunnelEdrHelper={trackIdCookieName:"search-result-tid",sessionIdCookieName:"search-result-sid",getSessionId:function(){var a=cookieUtils.readCookie(this.sessionIdCookieName);a||(a=globalGenerateGUID());var c=new Date;c.setTime(c.getTime()+12E5);cookieUtils.setCookie(this.sessionIdCookieName,a,c);return a},getTrackId:function(a){var c=cookieUtils.readCookie(this.trackIdCookieName);c&&"SEARCHED"!==a||(c=globalGenerateGUID());a=new Date;a.setTime(a.getTime()+12E5);cookieUtils.setCookie(this.trackIdCookieName,
c,a);return c},postEdr:function(a,c,e){a.uniqueTrackId||Object.assign(a,{uniqueTrackId:this.getTrackId(a.action)});a.sessionId||Object.assign(a,{sessionId:this.getSessionId()});Object.assign(a,{domain:window.location.origin,path:window.location.pathname,referrerPage:document.referrer});void 0!==c&&null!==c&&"object"===typeof c&&0<Object.keys(c).length&&Object.assign(a,c);$.ajax({url:"/ajax/search/generateSearchEdr",type:"POST",data:JSON.stringify(a),dataType:"json",contentType:"application/json; charset\x3dutf-8",
success:function(a){if(e&&e.onSuccess)e.onSuccess(a)},error:function(a){if(e&&e.onError)e.onError(a)}}).always(function(){window.sessionStorage.setItem("SEARCH_EDR_lastSearchEdrAction",a.action);e&&e()})}};