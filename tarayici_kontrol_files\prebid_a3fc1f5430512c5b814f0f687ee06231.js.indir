function _toSetter(t,e,n){e||(e=[]);var r=e.length++;return Object.defineProperty({},"_",{set:function set(o){e[r]=o,t.apply(n,e)}})}
function _regenerator(){var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var c=n&&n.prototype instanceof Generator?n:Generator,u=Object.create(c.prototype);return _regeneratorDefine2(u,"_invoke",function(r,n,o){var i,c,u,f=0,p=o||[],y=!1,G={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function d(t,r){return i=t,c=0,u=e,G.n=r,a}};function d(r,n){for(c=r,u=n,t=0;!y&&f&&!o&&t<p.length;t++){var o,i=p[t],d=G.p,l=i[2];r>3?(o=l===n)&&(u=i[(c=
i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=r<2&&d<i[1])?(c=0,G.v=n,G.n=i[1]):d<l&&(o=r<3||i[0]>n||n>l)&&(i[4]=r,i[5]=n,G.n=l,c=0))}if(o||r>1)return a;throw y=!0,n;}return function(o,p,l){if(f>1)throw TypeError("Generator is already running");for(y&&1===p&&d(p,l),c=p,u=l;(t=c<2?e:u)||!y;){i||(c?c<3?(c>1&&(G.n=-1),d(c,u)):G.n=u:G.v=u);try{if(f=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=
i["return"])&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(y=G.n<0)?u:r.call(n,G))!==a)break}catch(t){i=e,c=1,u=t}finally{f=1}}return{value:t,done:y}}}(r,o,i),!0),u}var a={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}t=Object.getPrototypeOf;var c=[][n]?t(t([][n]())):(_regeneratorDefine2(t={},n,function(){return this}),t),u=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(c);
function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,_regeneratorDefine2(e,o,"GeneratorFunction")),e.prototype=Object.create(u),e}return GeneratorFunction.prototype=GeneratorFunctionPrototype,_regeneratorDefine2(u,"constructor",GeneratorFunctionPrototype),_regeneratorDefine2(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",_regeneratorDefine2(GeneratorFunctionPrototype,
o,"GeneratorFunction"),_regeneratorDefine2(u),_regeneratorDefine2(u,o,"Generator"),_regeneratorDefine2(u,n,function(){return this}),_regeneratorDefine2(u,"toString",function(){return"[object Generator]"}),(_regenerator=function _regenerator(){return{w:i,m:f}})()}
function _regeneratorDefine2(e,r,n,t){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}_regeneratorDefine2=function _regeneratorDefine(e,r,n,t){if(r)i?i(e,r,{value:n,enumerable:!t,configurable:!t,writable:!t}):e[r]=n;else{var o=function o(r,n){_regeneratorDefine2(e,r,function(e){return this._invoke(r,n,e)})};o("next",0),o("throw",1),o("return",2)}},_regeneratorDefine2(e,r,n,t)}
function asyncGeneratorStep(n,t,e,r,o,a,c){try{var i=n[a](c),u=i.value}catch(n){return void e(n)}i.done?t(u):Promise.resolve(u).then(r,o)}function _asyncToGenerator(n){return function(){var t=this,e=arguments;return new Promise(function(r,o){var a=n.apply(t,e);function _next(n){asyncGeneratorStep(a,r,o,_next,_throw,"next",n)}function _throw(n){asyncGeneratorStep(a,r,o,_next,_throw,"throw",n)}_next(void 0)})}}
function _callSuper(t,o,e){return o=_getPrototypeOf(o),_possibleConstructorReturn(t,_isNativeReflectConstruct()?Reflect.construct(o,e||[],_getPrototypeOf(t).constructor):o.apply(t,e))}function _possibleConstructorReturn(t,e){if(e&&("object"==_typeof(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(t)}
function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}
function _getPrototypeOf(t){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_getPrototypeOf(t)}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_setPrototypeOf(t,e)}
function _setPrototypeOf(t,e){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_setPrototypeOf(t,e)}function _classPrivateFieldInitSpec(e,t,a){_checkPrivateRedeclaration(e,t),t.set(e,a)}function _classPrivateFieldGet(s,a){return s.get(_assertClassBrand(s,a))}function _classPrivateFieldSet(s,a,r){return s.set(_assertClassBrand(s,a),r),r}
function _classCallCheck(a,n){if(!(a instanceof n))throw new TypeError("Cannot call a class as a function");}function _defineProperties(e,r){for(var t=0;t<r.length;t++){var o=r[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}
function _classPrivateMethodInitSpec(e,a){_checkPrivateRedeclaration(e,a),a.add(e)}function _checkPrivateRedeclaration(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object");}function _assertClassBrand(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object");}
function ownKeys(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}
function _objectSpread(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(t),!0).forEach(function(r){_defineProperty(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}
function _toConsumableArray(r){return _arrayWithoutHoles(r)||_iterableToArray(r)||_unsupportedIterableToArray(r)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}
function _arrayWithoutHoles(r){if(Array.isArray(r))return _arrayLikeToArray(r)}
function _createForOfIteratorHelper(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=_unsupportedIterableToArray(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var _n41=0,F=function F(){};return{s:F,n:function n(){return _n41>=r.length?{done:!0}:{done:!1,value:r[_n41++]}},e:function e(r){throw r;},f:F}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}var o,
a=!0,u=!1;return{s:function s(){t=t.call(r)},n:function n(){var r=t.next();return a=r.done,r},e:function e(r){u=!0,o=r},f:function f(){try{a||null==t["return"]||t["return"]()}finally{if(u)throw o;}}}}function _defineProperty(e,r,t){return(r=_toPropertyKey(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _toPropertyKey(t){var i=_toPrimitive(t,"string");return"symbol"==_typeof(i)?i:i+""}
function _toPrimitive(t,r){if("object"!=_typeof(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return("string"===r?String:Number)(t)}function _slicedToArray(r,e){return _arrayWithHoles(r)||_iterableToArrayLimit(r,e)||_unsupportedIterableToArray(r,e)||_nonIterableRest()}
function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}
function _unsupportedIterableToArray(r,a){if(r){if("string"==typeof r)return _arrayLikeToArray(r,a);var t={}.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(r,a):void 0}}function _arrayLikeToArray(r,a){(null==a||a>r.length)&&(a=r.length);for(var e=0,n=Array(a);e<a;e++)n[e]=r[e];return n}
function _iterableToArrayLimit(r,l){var t=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=t){var e,n,i,u,a=[],f=!0,o=!1;try{if(i=(t=t.call(r)).next,0===l){if(Object(t)!==t)return;f=!1}else for(;!(f=(e=i.call(t)).done)&&(a.push(e.value),a.length!==l);f=!0);}catch(r){o=!0,n=r}finally{try{if(!f&&null!=t["return"]&&(u=t["return"](),Object(u)!==u))return}finally{if(o)throw n;}}return a}}function _arrayWithHoles(r){if(Array.isArray(r))return r}
function _typeof(o){"@babel/helpers - typeof";return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},_typeof(o)}
if(window.pbjs&&window.pbjs.libLoaded)try{window.pbjs.getConfig("debug")&&console.warn("Attempted to load a copy of Prebid.js that clashes with the existing 'pbjs' instance. Load aborted.")}catch(e){}else(function(){(function(){var r,t={433:function _(r,t,e){function n(r,t,e,n,o){for(t=t.split?t.split("."):t,n=0;n<t.length;n++)r=r?r[t[n]]:o;return r===o?e:r}e.d(t,{A:function A(){return n}})},8128:function _(r){i.SYNC=1,i.ASYNC=2,i.QUEUE=4;var t="fun-hooks",e=Object.freeze({ready:0}),n=new WeakMap;
function o(r,t){return Array.prototype.slice.call(r,t)}function i(r){var a,f={},c=[];function u(r,t){return"function"==typeof r?s.call(null,"sync",r,t):"string"==typeof r&&"function"==typeof t?s.apply(null,arguments):"object"==_typeof(r)?l.apply(null,arguments):void 0}function l(r,t,e){var n=!0;void 0===t&&(t=Object.getOwnPropertyNames(r).filter(function(r){return!r.match(/^_/)}),n=!1);var o={},i=["constructor"];do t.forEach(function(t){var n=t.match(/(?:(sync|async):)?(.+)/),a=n[1]||"sync",f=n[2];
if(!o[f]&&"function"==typeof r[f]&&-1===i.indexOf(f)){var c=r[f];o[f]=r[f]=s(a,c,e?[e,f]:void 0)}}),r=Object.getPrototypeOf(r);while(n&&r);return o}function p(r){var e=Array.isArray(r)?r:r.split(".");return e.reduce(function(n,o,i){var f=n[o],u=!1;return f||(i===e.length-1?(a||c.push(function(){u||console.warn(t+": referenced '"+r+"' but it was never created")}),n[o]=y(function(r){n[o]=r,u=!0})):n[o]={})},f)}function y(r){var t=[],e=[],o=function o(){},i={before:function before(r,e){return f.call(this,
t,"before",r,e)},after:function after(r,t){return f.call(this,e,"after",r,t)},getHooks:function getHooks(r){var n=t.concat(e);"object"==_typeof(r)&&(n=n.filter(function(t){return Object.keys(r).every(function(e){return t[e]===r[e]})}));try{Object.assign(n,{remove:function remove(){return n.forEach(function(r){r.remove()}),this}})}catch(r){console.error("error adding `remove` to array, did you modify Array.prototype?")}return n},removeAll:function removeAll(){return this.getHooks().remove()}},a={install:function install(n,
i,a){this.type=n,o=a,a(t,e),r&&r(i)}};return n.set(i.after,a),i;function f(r,n,i,a){var f={hook:i,type:n,priority:a||10,remove:function remove(){var n=r.indexOf(f);-1!==n&&(r.splice(n,1),o(t,e))}};return r.push(f),r.sort(function(r,t){return t.priority-r.priority}),o(t,e),this}}function s(e,f,u){var l=f.after&&n.get(f.after);if(l){if(l.type!==e)throw t+": recreated hookable with different type";return f}var s,v=u?p(u):y(),h={get:function get(r,t){return v[t]||Reflect.get.apply(Reflect,arguments)}};
a||c.push(b);var d=new Proxy(f,h);return n.get(d.after).install(e,d,function(r,t){var n,i=[];r.length||t.length?(r.forEach(a),n=i.push(void 0)-1,t.forEach(a),s=function s(r,t,a){var f,c=i.slice(),u=0,l="async"===e&&"function"==typeof a[a.length-1]&&a.pop();function p(r){"sync"===e?f=r:l&&l.apply(null,arguments)}function y(r){if(c[u]){var n=o(arguments);return y.bail=p,n.unshift(y),c[u++].apply(t,n)}"sync"===e?f=r:l&&l.apply(null,arguments)}return c[n]=function(){var n=o(arguments,1);"async"===e&&
l&&(delete y.bail,n.push(y));var i=r.apply(t,n);"sync"===e&&y(i)},y.apply(null,a),f}):s=void 0;function a(r){i.push(r.hook)}b()}),d;function b(){!a&&("sync"!==e||r.ready&i.SYNC)&&("async"!==e||r.ready&i.ASYNC)?"sync"!==e&&r.ready&i.QUEUE?h.apply=function(){var r=arguments;c.push(function(){d.apply(r[1],r[2])})}:h.apply=function(){throw t+": hooked function not ready";}:h.apply=s}}return(r=Object.assign({},e,r)).ready?u.ready=function(){a=!0,function(r){for(var t;t=r.shift();)t()}(c)}:a=!0,u.get=p,
u}r.exports=i},3172:function _(r,t,e){function n(r,t,e){t.split&&(t=t.split("."));for(var n,o,i=0,a=t.length,f=r;i<a&&"__proto__"!=(o=""+t[i++])&&"constructor"!==o&&"prototype"!==o;)f=f[o]=i===a?e:_typeof(n=f[o])==_typeof(t)?n:0*t[i]!=0||~(""+t[i]).indexOf(".")?{}:[]}e.d(t,{J:function J(){return n}})},5751:function _(r,t,e){function n(r){var t,e,o;if(Array.isArray(r)){for(e=Array(t=r.length);t--;)e[t]=(o=r[t])&&"object"==_typeof(o)?n(o):o;return e}if("[object Object]"===Object.prototype.toString.call(r)){for(t in e=
{},r)"__proto__"===t?Object.defineProperty(e,t,{value:n(r[t]),configurable:!0,enumerable:!0,writable:!0}):e[t]=(o=r[t])&&"object"==_typeof(o)?n(o):o;return e}return r}e.d(t,{Q:function Q(){return n}})}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.m=t,r=[],n.O=function(t,e,o,i){if(!e){var a=1/0;for(l=0;l<r.length;l++){for(var _r$l=_slicedToArray(r[l],3),e=_r$l[0],o=_r$l[1],i=_r$l[2],f=!0,c=0;c<e.length;c++)(!1&i||a>=i)&&
Object.keys(n.O).every(function(r){return n.O[r](e[c])})?e.splice(c--,1):(f=!1,i<a&&(a=i));if(f){r.splice(l--,1);var u=o();void 0!==u&&(t=u)}}return t}i=i||0;for(var l=r.length;l>0&&r[l-1][2]>i;l--)r[l]=r[l-1];r[l]=[e,o,i]},n.n=function(r){var t=r&&r.__esModule?function(){return r["default"]}:function(){return r};return n.d(t,{a:t}),t},n.d=function(r,t){for(var e in t)n.o(t,e)&&!n.o(r,e)&&Object.defineProperty(r,e,{enumerable:!0,get:t[e]})},n.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,
t)},function(){var r={673:0};n.O.j=function(t){return 0===r[t]};var t=function t(_t,e){var o,i,_e2=_slicedToArray(e,3),a=_e2[0],f=_e2[1],c=_e2[2],u=0;if(a.some(function(t){return 0!==r[t]})){for(o in f)n.o(f,o)&&(n.m[o]=f[o]);if(c)var l=c(n)}for(_t&&_t(e);u<a.length;u++)i=a[u],n.o(r,i)&&r[i]&&r[i][0](),r[i]=0;return n.O(l)},e=self.pbjsChunk=self.pbjsChunk||[];e.forEach(t.bind(null,0)),e.push=t.bind(null,e.push.bind(e))}();var o=n.O(void 0,[802,769,139,85],function(){return n(8934)});o=n.O(o)})();
(self.pbjsChunk=self.pbjsChunk||[]).push([[802],{5789:function _(e,t,n){n.d(t,{A4:function A4(){return c},J7:function J7(){return u},Pg:function Pg(){return l}});var i=n(1580),r=n(1069),o=n(7873),s=n(5569);var a=(0,o.m)(),d="outstream";function c(e){var _this=this;var t=e.url,n=e.config,o=e.id,c=e.callback,u=e.loaded,l=e.adUnitCode,f=e.renderNow;this.url=t,this.config=n,this.handlers={},this.id=o,this.renderNow=f,this.adUnitCode=l,this.loaded=u,this.cmd=[],this.push=function(e){"function"==typeof e?
_this.loaded?e.call():_this.cmd.push(e):(0,r.vV)("Commands given to Renderer.push must be wrapped in a function")},this.callback=c||function(){_this.loaded=!0,_this.process()},this.render=function(){var _this2=this;var e=arguments,n=function n(){_this2._render?_this2._render.apply(_this2,e):(0,r.JE)("No render function was provided, please use .setRender on the renderer")};!function(e,_t$mediaTypes){var t=a.adUnits.find(function(t){return t.code===e});if(!t)return!1;var n=t===null||t===void 0?void 0:
t.renderer,i=!!(n&&n.url&&n.render),r=t===null||t===void 0||(_t$mediaTypes=t.mediaTypes)===null||_t$mediaTypes===void 0||(_t$mediaTypes=_t$mediaTypes.video)===null||_t$mediaTypes===void 0?void 0:_t$mediaTypes.renderer,o=!!(r&&r.url&&r.render);return!!(i&&!0!==n.backupOnly||o&&!0!==r.backupOnly)}(l)?f?n():(this.cmd.unshift(n),(0,i.R)(t,s.tp,d,this.callback,this.documentContext)):((0,r.JE)("External Js not loaded by Renderer since renderer url and callback is already defined on adUnit ".concat(l)),
n())}.bind(this)}function u(e){return!(!e||!e.url&&!e.renderNow)}function l(e,t,n){var i=null;e.config&&e.config.documentResolver&&(i=e.config.documentResolver(t,document,n)),i||(i=document),e.documentContext=i,e.render(t,e.documentContext)}c.install=function(e){var t=e.url,n=e.config,i=e.id,r=e.callback,o=e.loaded,s=e.adUnitCode,a=e.renderNow;return new c({url:t,config:n,id:i,callback:r,loaded:o,adUnitCode:s,renderNow:a})},c.prototype.getConfig=function(){return this.config},c.prototype.setRender=
function(e){this._render=e},c.prototype.setEventHandlers=function(e){this.handlers=e},c.prototype.handleVideoEvent=function(e){var t=e.id,n=e.eventName;"function"==typeof this.handlers[n]&&this.handlers[n](),(0,r.OG)("Prebid Renderer event for id ".concat(t," type ").concat(n))},c.prototype.process=function(){for(;this.cmd.length>0;)try{this.cmd.shift().call()}catch(e){(0,r.vV)("Error processing Renderer command on ad unit '".concat(this.adUnitCode,"':"),e)}}},6811:function _(e,t,n){n.d(t,{DL:function DL(){return u},
Ml:function Ml(){return r},Ue:function Ue(){return i},VJ:function VJ(){return f},hE:function hE(){return l},hq:function hq(){return c},mo:function mo(){return d},pY:function pY(){return g},qX:function qX(){return o},uc:function uc(){return a},yl:function yl(){return s}});var i="accessDevice",r="syncUser",o="enrichUfpd",s="enrichEids",a="fetchBids",d="reportAnalytics",c="transmitEids",u="transmitUfpd",l="transmitPreciseGeo",f="transmitTid",g="loadExternalScript"},3441:function _(e,t,n){n.d(t,{s:function s(){return r}});
var i=n(8046);var r=(0,n(2604).ZI)(function(e){return i.Ay.resolveAlias(e)})},5569:function _(e,t,n){n.d(t,{Tn:function Tn(){return a},fW:function fW(){return o},tW:function tW(){return r},tp:function tp(){return i},zu:function zu(){return s}});var i="prebid",r="bidder",o="userId",s="rtd",a="analytics"},2604:function _(e,t,n){n.d(t,{Dk:function Dk(){return s},Ii:function Ii(){return o},TQ:function TQ(){return g},U3:function U3(){return h},XG:function XG(){return u},ZI:function ZI(){return p},Zw:function Zw(){return c},
bt:function bt(){return l},e3:function e3(){return f},iK:function iK(){return a},q7:function q7(){return d}});var i=n(5569),r=n(9214);var o="component",s=o+"Type",a=o+"Name",d="adapterCode",c="storageType",u="configName",l="syncType",f="syncUrl",g="_config";function p(e){return function(t,n,r){var c=_defineProperty(_defineProperty(_defineProperty({},s,t),a,n),o,"".concat(t,".").concat(n));return t===i.tW&&(c[d]=e(n)),h(Object.assign(c,r))}}var h=(0,r.A_)("sync",function(e){return e})},5139:function _(e,
t,n){n.d(t,{io:function io(){return s},qB:function qB(){return o}});var i=n(1069),r=n(2604);var _ref=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:(0,i.h0)("Activity control:");var t={};function n(e){return t[e]=t[e]||[]}function o(t,n,i,o){var s;try{s=i(o)}catch(i){e.logError("Exception in rule ".concat(n," for '").concat(t,"'"),i),s={allow:!1,reason:i}}return s&&Object.assign({activity:t,name:n,component:o[r.Ii]},s)}var s={};function a(t){var n=t.activity,i=t.name,r=t.allow,
o=t.reason,a=t.component;var d="".concat(i," ").concat(r?"allowed":"denied"," '").concat(n,"' for '").concat(a,"'").concat(o?":":""),c=s.hasOwnProperty(d);if(c&&clearTimeout(s[d]),s[d]=setTimeout(function(){return delete s[d]},1E3),!c){var _t2=[d];o&&_t2.push(o),(r?e.logInfo:e.logWarn).apply(e,_t2)}}return[function(e,t,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;var o=n(e),s=o.findIndex(function(e){var _e3=_slicedToArray(e,1),t=_e3[0];return r<t}),a=[r,t,i];return o.splice(s<
0?o.length:s,0,a),function(){var e=o.indexOf(a);e>=0&&o.splice(e,1)}},function(e,t){var i,r;var _iterator=_createForOfIteratorHelper(n(e)),_step;try{for(_iterator.s();!(_step=_iterator.n()).done;){var _step$value=_slicedToArray(_step.value,3),_s=_step$value[0],d=_step$value[1],c=_step$value[2];if(i!==_s&&r)break;i=_s;var _n=o(e,d,c,t);if(_n){if(!_n.allow)return a(_n),!1;r=_n}}}catch(err){_iterator.e(err)}finally{_iterator.f()}return r&&a(r),!0}]}(),_ref2=_slicedToArray(_ref,2),o=_ref2[0],s=_ref2[1]},
9075:function _(e,t,n){n.d(t,{$A:function $A(){return I},BS:function BS(){return P},Hh:function Hh(){return x},Pk:function Pk(){return N},Uc:function Uc(){return R},XO:function XO(){return V},bw:function bw(){return _},n6:function n6(){return C},qn:function qn(){return $},vB:function vB(){return W},vW:function vW(){return S},vd:function vd(){return U}});var i=n(1069),r=n(5023),o=n(8969),s=n(3272),a=n(5789),d=n(1371),c=n(6881),u=n(6031),l=n(9214),f=n(2449),g=n(5555),p=n(8046),h=n(6894),m=n(7779),b=
n(3005);var _o$qY=o.qY,y=_o$qY.AD_RENDER_FAILED,v=_o$qY.AD_RENDER_SUCCEEDED,E=_o$qY.STALE_RENDER,A=_o$qY.BID_WON,w=_o$qY.EXPIRED_RENDER,T=o.as.EXCEPTION,I=(0,l.A_)("sync",function(e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:g.U9.resolve()).then(function(t){return t!==null&&t!==void 0?t:c.n.findBidByAdId(e)})["catch"](function(){})}),C=(0,l.A_)("sync",function(e){var _b$RO;(((_b$RO=(0,b.$T)(e.eventtrackers)[b.RO])===null||_b$RO===void 0?void 0:_b$RO[b.Ni])||[]).forEach(function(e){return(0,
i.z$)(e)}),r.Ic(A,e),c.n.addWinningBid(e)});function B(e){var t=e.reason,n=e.message,o=e.bid,s=e.id;var a={reason:t,message:n};o&&(a.bid=o,a.adId=o.adId),s&&(a.adId=s),(0,i.vV)("Error rendering ad (id: ".concat(s,"): ").concat(n)),r.Ic(y,a)}function O(e){var t=e.doc,n=e.bid,i=e.id;var o={doc:t};n&&(o.bid=n),i&&(o.adId=i),p.Ay.callAdRenderSucceededBidder(n.adapterCode||n.bidder,n),r.Ic(v,o)}function R(e,t){switch(e.event){case o.qY.AD_RENDER_FAILED:B({bid:t,id:t.adId,reason:e.info.reason,message:e.info.message});
break;case o.qY.AD_RENDER_SUCCEEDED:O({doc:null,bid:t,id:t.adId});break;default:(0,i.vV)("Received event request for unsupported event: '".concat(e.event,"' (adId: '").concat(t.adId,"')"))}}function S(e,t,n){var i=n.resizeFn,_n$fireTrackers=n.fireTrackers,r=_n$fireTrackers===void 0?f.vO:_n$fireTrackers;if("resizeNativeHeight"===e.action)i(e.width,e.height);else r(e,t)}var k=_defineProperty({},o.nl.EVENT,R);k[o.nl.NATIVE]=S;var U=(0,l.A_)("sync",function(e,t){var n=e.ad,r=e.adUrl,o=e.cpm,s=e.originalCpm,
a=e.width,d=e.height,c=e.instl,u={AUCTION_PRICE:s||o,CLICKTHROUGH:(t===null||t===void 0?void 0:t.clickUrl)||""};return{ad:(0,i.gM)(n,u),adUrl:(0,i.gM)(r,u),width:a,height:d,instl:c}}),D=(0,l.A_)("sync",function(e){var t=e.renderFn,n=e.resizeFn,r=e.bidResponse,s=e.options,a=e.doc,_e$isMainDocument=e.isMainDocument,c=_e$isMainDocument===void 0?a===document&&!(0,i.al)():_e$isMainDocument;var u=r.mediaType===d.G_;if(c||u)return void B({reason:o.as.PREVENT_WRITING_ON_MAIN_DOCUMENT,message:u?"Cannot render video ad without a renderer":
"renderAd was prevented from writing to the main document.",bid:r,id:r.adId});var l=U(r,s);t(Object.assign({adId:r.adId},l));var f=l.width,g=l.height;null!=(f!==null&&f!==void 0?f:g)&&n(f,g)});function _(e){var t=e.renderFn,n=e.resizeFn,a=e.adId,d=e.options,c=e.bidResponse,u=e.doc;x(c,function(){if(null!=c){var _s$$W$getConfig,_s$$W$getConfig2;if((c.status!==o.tl.RENDERED||((0,i.JE)("Ad id ".concat(a," has been rendered before")),r.Ic(E,c),!((_s$$W$getConfig=s.$W.getConfig("auctionOptions"))!==null&&
_s$$W$getConfig!==void 0&&_s$$W$getConfig.suppressStaleRender)))&&(m.uW.isBidNotExpired(c)||((0,i.JE)("Ad id ".concat(a," has been expired")),r.Ic(w,c),!((_s$$W$getConfig2=s.$W.getConfig("auctionOptions"))!==null&&_s$$W$getConfig2!==void 0&&_s$$W$getConfig2.suppressExpiredRender))))try{D({renderFn:t,resizeFn:n,bidResponse:c,options:d,doc:u})}catch(e){B({reason:o.as.EXCEPTION,message:e.message,id:a,bid:c})}}else B({reason:o.as.CANNOT_FIND_AD,message:"Cannot find ad '".concat(a,"'"),id:a})})}function $(e){var t=
(0,h.BO)(e.metrics);t.checkpoint("bidRender"),t.timeBetween("bidWon","bidRender","render.deferred"),t.timeBetween("auctionEnd","bidRender","render.pending"),t.timeBetween("requestBids","bidRender","render.e2e"),e.status=o.tl.RENDERED}D.before(function(e,t){var n=t.bidResponse,i=t.doc;(0,a.J7)(n.renderer)?((0,a.Pg)(n.renderer,n,i),O({doc:i,bid:n,id:n.adId}),e.bail()):e(t)},100);var j=new WeakMap,q=new WeakSet;function x(e,t){null!=e?(j.set(e,t),e.deferRendering||W(e),N(e)):t()}function N(e){q.has(e)||
(q.add(e),C(e))}function W(e){var t=j.get(e);t&&(t(),$(e),j["delete"](e))}function P(e,t,n){var r;function s(e,n){B(Object.assign({id:t,bid:r},{reason:e,message:n}))}function a(t,n){var _e$defaultView;var i=(_e$defaultView=e.defaultView)===null||_e$defaultView===void 0?void 0:_e$defaultView.frameElement;i&&(t&&(i.width=t,i.style.width&&(i.style.width="".concat(t,"px"))),n&&(i.height=n,i.style.height&&(i.style.height="".concat(n,"px"))))}var d=(c={resizeFn:a},function(e,t,n){k.hasOwnProperty(e)&&k[e](t,
n,c)});var c;function l(t){t.ad?(e.write(t.ad),e.close(),O({doc:e,bid:r,id:r.adId})):(0,u.HH)(r).then(function(n){return n(t,{sendMessage:function sendMessage(e,t){return d(e,t,r)},mkFrame:i.hw},e.defaultView)}).then(function(){return O({doc:e,bid:r,id:r.adId})},function(e){s((e===null||e===void 0?void 0:e.reason)||o.as.EXCEPTION,e===null||e===void 0?void 0:e.message),(e===null||e===void 0?void 0:e.stack)&&(0,i.vV)(e)});var n=document.createComment("Creative ".concat(r.creativeId," served by ").concat(r.bidder,
" Prebid.js Header Bidding"));(0,i._s)(n,e,"html")}try{t&&e?I(t).then(function(i){r=i,_({renderFn:l,resizeFn:a,adId:t,options:{clickUrl:n===null||n===void 0?void 0:n.clickThrough},bidResponse:i,doc:e})}):s(o.as.MISSING_DOC_OR_ADID,"missing "+(t?"doc":"adId"))}catch(e){s(T,e.message)}}function V(){if(!window.frames[o.IY])if(document.body){var _e4=(0,i.CA)();_e4.name=o.IY,document.body.appendChild(_e4)}else window.requestAnimationFrame(V)}},8046:function _(e,t,n){n.d(t,{Ay:function Ay(){return ne},
pX:function pX(){return Y}});var i=n(1069),r=n(2449),o=n(9115),s=n(8044),a=n(3272),d=n(9214);var c={};function u(e,t,n){var i=function(e,t){var n=c[e]=c[e]||{bidders:{}};return t?n.bidders[t]=n.bidders[t]||{}:n}(e,n);return i[t]=(i[t]||0)+1,i[t]}function l(e){return u(e,"auctionsCounter")}function f(e){var _c$e;return(c===null||c===void 0||(_c$e=c[e])===null||_c$e===void 0?void 0:_c$e.requestsCounter)||0}function g(e,t){var _c$e2;return(c===null||c===void 0||(_c$e2=c[e])===null||_c$e2===void 0||(_c$e2=
_c$e2.bidders)===null||_c$e2===void 0||(_c$e2=_c$e2[t])===null||_c$e2===void 0?void 0:_c$e2.requestsCounter)||0}function p(e,t){var _c$e3;return(c===null||c===void 0||(_c$e3=c[e])===null||_c$e3===void 0||(_c$e3=_c$e3.bidders)===null||_c$e3===void 0||(_c$e3=_c$e3[t])===null||_c$e3===void 0?void 0:_c$e3.winsCounter)||0}function h(e){var _c$e4;return(c===null||c===void 0||(_c$e4=c[e])===null||_c$e4===void 0?void 0:_c$e4.auctionsCounter)||0}var m=n(7934),b=n(6916),y=n(5023),v=n(8969),E=n(6894),A=n(6881),
w=n(5569),T=n(5139),I=n(6811),C=n(2604),B=n(433);var O=["data","ext.data","yob","gender","keywords","kwarray","id","buyeruid","customdata"].map(function(e){return"user.".concat(e)}).concat("device.ext.cdep"),R=["user.eids","user.ext.eids"],S=["user.geo.lat","user.geo.lon","device.geo.lat","device.geo.lon"],k=["device.ip"],U=["device.ipv6"];function D(e){return Object.assign({get:function get(){},run:function run(e,t,n,i,r){var o=n&&n[i];if(function(e){return null!=e&&("object"!=_typeof(e)||Object.keys(e).length>
0)}(o)&&r()){var _e5=this.get(o);void 0===_e5?delete n[i]:n[i]=_e5}}},e)}function _(e){return e.forEach(function(e){e.paths=e.paths.map(function(e){var t=e.split("."),n=t.pop();return[t.length>0?t.join("."):null,n]})}),function(t,n){var i=[];for(var r=arguments.length,o=new Array(r>2?r-2:0),s=2;s<r;s++)o[s-2]=arguments[s];var a=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return function(t){return e.hasOwnProperty(t.name)||(e[t.name]=!!t.applies.apply(t,
n)),e[t.name]}}.apply(void 0,[t].concat(o));return e.forEach(function(e){if(!1!==t[e.name]){var _iterator2=_createForOfIteratorHelper(e.paths),_step2;try{for(_iterator2.s();!(_step2=_iterator2.n()).done;){var _step2$value=_slicedToArray(_step2.value,2),_r=_step2$value[0],_o=_step2$value[1];var _s2=null==_r?n:(0,B.A)(n,_r);if(i.push(e.run(n,_r,_s2,_o,a.bind(null,e))),!1===t[e.name])return}}catch(err){_iterator2.e(err)}finally{_iterator2.f()}}}),i.filter(function(e){return null!=e})}}function $(e){var t=
arguments.length>1&&void 0!==arguments[1]?arguments[1]:T.io;return function(n){return!t(e,n)}}function j(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:T.io;return[{name:I.DL,paths:O,applies:$(I.DL,e)},{name:I.hq,paths:R,applies:$(I.hq,e)},{name:I.hE,paths:S,applies:$(I.hE,e),get:function get(e){return Math.round(100*(e+Number.EPSILON))/100}},{name:I.hE,paths:k,applies:$(I.hE,e),get:function get(e){return function(e){if(!e)return null;var t=e.split(".").map(Number);if(4!=t.length)return null;
var n=[];for(var _e6=0;_e6<4;_e6++){var _t3=Math.max(0,Math.min(8,24-8*_e6));n.push(255<<8-_t3&255)}return t.map(function(e,t){return e&n[t]}).join(".")}(e)}},{name:I.hE,paths:U,applies:$(I.hE,e),get:function get(e){return function(e){if(!e)return null;var t=e.split(":").map(function(e){return parseInt(e,16)});for(t=t.map(function(e){return isNaN(e)?0:e});t.length<8;)t.push(0);if(8!=t.length)return null;var n=[];for(var _e7=0;_e7<8;_e7++){var _t4=Math.max(0,Math.min(16,64-16*_e7));n.push(65535<<16-
_t4&65535)}return t.map(function(e,t){return e&n[t]}).map(function(e){return e.toString(16)}).join(":")}(e)}},{name:I.VJ,paths:["source.tid"],applies:$(I.VJ,e)}].map(D)}var q=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:T.io;var t=_(j(e)),n=_(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:T.io;return[{name:I.hq,paths:["userId","userIdAsEids"],applies:$(I.hq,e)},{name:I.VJ,paths:["ortb2Imp.ext.tid"],applies:$(I.VJ,e)}].map(D)}(e));return function(e){var i=
{};return{ortb2:function ortb2(n){return t(i,n,e),n},bidRequest:function bidRequest(t){return n(i,t,e),t}}}}();(0,T.qB)(I.VJ,"enableTIDs config",function(){if(!a.$W.getConfig("enableTIDs"))return{allow:!1,reason:"TIDs are disabled"}});var x=n(3005);var N="pbsBidAdapter",W={CLIENT:"client",SERVER:"server"},P={isAllowed:T.io,redact:q};var V={},M=V.bidderRegistry={},G=V.aliasRegistry={},F=[];a.$W.getConfig("s2sConfig",function(e){e&&e.s2sConfig&&(F=(0,i.cy)(e.s2sConfig)?e.s2sConfig:[e.s2sConfig])});
var H={};var L=(0,C.ZI)(function(e){return V.resolveAlias(e)});function z(e){var _e$configName;return(_e$configName=e.configName)!==null&&_e$configName!==void 0?_e$configName:e.name}var J=(0,d.A_)("sync",function(e){var t=e.bidderCode,n=e.auctionId,r=e.bidderRequestId,o=e.adUnits,s=e.src,a=e.metrics;return o.reduce(function(e,o){var d=o.bids.filter(function(e){return e.bidder===t});return null==t&&0===d.length&&null!=o.s2sBid&&d.push({bidder:null}),e.push(d.reduce(function(e,d){var _c$banner,_c$video;
var c=null==(d=Object.assign({},d,{ortb2Imp:(0,i.D9)({},o.ortb2Imp,d.ortb2Imp)},(0,i.SH)(o,["nativeParams","nativeOrtbRequest","mediaType","renderer"]))).mediaTypes?o.mediaTypes:d.mediaTypes;return(0,i.wD)(c)?d=Object.assign({},d,{mediaTypes:c}):(0,i.vV)("mediaTypes is not correctly configured for adunit ".concat(o.code)),"client"===s&&function(e,t){u(e,"requestsCounter",t)}(o.code,t),e.push(Object.assign({},d,{adUnitCode:o.code,transactionId:o.transactionId,adUnitId:o.adUnitId,sizes:(c===null||c===
void 0||(_c$banner=c.banner)===null||_c$banner===void 0?void 0:_c$banner.sizes)||(c===null||c===void 0||(_c$video=c.video)===null||_c$video===void 0?void 0:_c$video.playerSize)||[],bidId:d.bid_id||(0,i.s0)(),bidderRequestId:r,auctionId:n,src:s,metrics:a,auctionsCount:h(o.code),bidRequestsCount:f(o.code),bidderRequestsCount:g(o.code,d.bidder),bidderWinsCount:p(o.code,d.bidder),deferBilling:!!o.deferBilling})),e},[])),e},[]).reduce(i.Bq,[]).filter(function(e){return""!==e})},"getBids");var Q=(0,d.A_)("sync",
function(e,t){var _ref3=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref3$getS2SBidders=_ref3.getS2SBidders,n=_ref3$getS2SBidders===void 0?Y:_ref3$getS2SBidders;if(null==t)return e;var _i=n(t);return e.filter(function(e){if(!_i.has(e.bidder))return!1;if(null==e.s2sConfigName)return!0;var n=z(t);return(Array.isArray(e.s2sConfigName)?e.s2sConfigName:[e.s2sConfigName]).includes(n)})},"filterBidsForAdUnit");var K=(0,d.A_)("sync",function(e,t){return e},"setupAdUnitMediaTypes");function Y(e){(0,
i.cy)(e)||(e=[e]);var t=new Set([null]);return e.filter(function(e){return e&&e.enabled}).flatMap(function(e){return e.bidders}).forEach(function(e){return t.add(e)}),t}var X=(0,d.A_)("sync",function(e,t){var _ref4=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref4$getS2SBidders=_ref4.getS2SBidders,n=_ref4$getS2SBidders===void 0?Y:_ref4$getS2SBidders;var r=n(t);return(0,i.ZA)(e).reduce(function(e,t){return e[r.has(t)?W.SERVER:W.CLIENT].push(t),e},_defineProperty(_defineProperty({},W.CLIENT,
[]),W.SERVER,[]))},"partitionBidders");function Z(e,t){var n=M[e],i=(n===null||n===void 0?void 0:n.getSpec)&&n.getSpec();if(i&&i[t]&&"function"==typeof i[t])return[i,i[t]]}function ee(e,t,n,r){try{(0,i.fH)("Invoking ".concat(e,".").concat(t));for(var o=arguments.length,s=new Array(o>4?o-4:0),d=4;d<o;d++)s[d-4]=arguments[d];a.$W.runWithBidder(e,r.bind.apply(r,[n].concat(s)))}catch(n){(0,i.JE)("Error calling ".concat(t," of ").concat(e))}}function te(e,t,n){if((n===null||n===void 0?void 0:n.source)!==
v.RW.SRC){var _i2=Z(e,t);null!=_i2&&ee.apply(void 0,[e,t].concat(_toConsumableArray(_i2),[n]))}}V.makeBidRequests=(0,d.A_)("sync",function(e,t,n,o,s){var d=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},c=arguments.length>6?arguments[6]:void 0;c=(0,E.BO)(c),y.Ic(v.qY.BEFORE_REQUEST_BIDS,e),(0,r.nk)(e),e.map(function(e){return e.code}).filter(i.hj).forEach(l),e.forEach(function(e){(0,i.Qd)(e.mediaTypes)||(e.mediaTypes={}),e.bids=e.bids.filter(function(e){return!e.bidder||P.isAllowed(I.uc,
L(w.tW,e.bidder))}),u(e.code,"requestsCounter")}),e=K(e,s);var _X=X(e,F),f=_X[W.CLIENT],g=_X[W.SERVER];a.$W.getConfig("bidderSequence")===a.Ov&&(f=(0,i.k4)(f));var p=(0,m.EN)();var h=[];var A=d.global||{},T=d.bidder||{};function B(e,t){var r=P.redact(null!=t?t:L(w.tW,e.bidderCode)),o=(0,i.D9)({source:{tid:n}},A,T[e.bidderCode]);!function(e,_e$user){var t=(_e$user=e.user)===null||_e$user===void 0?void 0:_e$user.eids;Array.isArray(t)&&t.length&&(e.user.ext=e.user.ext||{},e.user.ext.eids=[].concat(_toConsumableArray(e.user.ext.eids||
[]),_toConsumableArray(t)),delete e.user.eids)}(o);var s=Object.freeze(r.ortb2(o));return e.ortb2=s,e.bids=e.bids.map(function(e){return e.ortb2=s,r.bidRequest(e)}),e}F.forEach(function(r){var o=function(e){return L(w.tp,N,_defineProperty({},C.XG,z(e)))}(r);if(r&&r.enabled&&P.isAllowed(I.uc,o)){var _ref5=function(e,t){var n=(0,i.Go)(e),r=!1;return n.forEach(function(e){var n=e.bids.filter(function(e){var _e$params;return e.module===N&&((_e$params=e.params)===null||_e$params===void 0?void 0:_e$params.configName)===
z(t)});1===n.length?(e.s2sBid=n[0],r=!0,e.ortb2Imp=(0,i.D9)({},e.s2sBid.ortb2Imp,e.ortb2Imp)):n.length>1&&(0,i.JE)('Multiple "module" bids for the same s2s configuration; all will be ignored',n),e.bids=Q(e.bids,t).map(function(e){return e.bid_id=(0,i.s0)(),e})}),n=n.filter(function(e){return 0!==e.bids.length||null!=e.s2sBid}),{adUnits:n,hasModuleBids:r}}(e,r),_s3=_ref5.adUnits,_a=_ref5.hasModuleBids,_d=(0,i.lk)();(0===g.length&&_a?[null]:g).forEach(function(e){var a=(0,i.s0)(),u=c.fork(),l=B({bidderCode:e,
auctionId:n,bidderRequestId:a,uniquePbsTid:_d,bids:J({bidderCode:e,auctionId:n,bidderRequestId:a,adUnits:(0,i.Go)(_s3),src:v.RW.SRC,metrics:u}),auctionStart:t,timeout:r.timeout,src:v.RW.SRC,refererInfo:p,metrics:u},o);0!==l.bids.length&&h.push(l)}),_s3.forEach(function(e){var t=e.bids.filter(function(e){return h.find(function(t){return t.bids.find(function(t){return t.bidId===e.bid_id})})});e.bids=t}),h.forEach(function(e){void 0===e.adUnitsS2SCopy&&(e.adUnitsS2SCopy=_s3.filter(function(e){return e.bids.length>
0||null!=e.s2sBid}))})}});var O=function(e){var t=(0,i.Go)(e);return t.forEach(function(e){e.bids=Q(e.bids,null)}),t=t.filter(function(e){return 0!==e.bids.length}),t}(e);return f.forEach(function(e){var r=(0,i.s0)(),a=c.fork(),d=B({bidderCode:e,auctionId:n,bidderRequestId:r,bids:J({bidderCode:e,auctionId:n,bidderRequestId:r,adUnits:(0,i.Go)(O),labels:s,src:"client",metrics:a}),auctionStart:t,timeout:o,refererInfo:p,metrics:a}),u=M[e];u||(0,i.vV)("Trying to make a request for bidder that does not exist: ".concat(e)),
u&&d.bids&&0!==d.bids.length&&h.push(d)}),h.forEach(function(e){b.mW.getConsentData()&&(e.gdprConsent=b.mW.getConsentData()),b.t6.getConsentData()&&(e.uspConsent=b.t6.getConsentData()),b.ad.getConsentData()&&(e.gppConsent=b.ad.getConsentData())}),h},"makeBidRequests"),V.callBids=function(e,t,n,r,o,d,c){var u=arguments.length>7&&void 0!==arguments[7]?arguments[7]:{};if(!t.length)return void(0,i.JE)("callBids executed with no bidRequests.  Were they filtered by labels or sizing?");var _t$reduce=t.reduce(function(e,
t){return e[Number(void 0!==t.src&&t.src===v.RW.SRC)].push(t),e},[[],[]]),_t$reduce2=_slicedToArray(_t$reduce,2),l=_t$reduce2[0],f=_t$reduce2[1];var g=[];f.forEach(function(e){for(var t=-1,n=0;n<g.length;++n)if(e.uniquePbsTid===g[n].uniquePbsTid){t=n;break}t<=-1&&g.push(e)});var p=0;F.forEach(function(e){if(e&&g[p]&&Y(e).has(g[p].bidderCode)){var _t5=(0,s.g4)(d,o?{request:o.request.bind(null,"s2s"),done:o.done}:void 0);var _a2=e.bidders;var _l=M[e.adapter];var _h=g[p].uniquePbsTid,_m=g[p].adUnitsS2SCopy,
_b=f.filter(function(e){return e.uniquePbsTid===_h});if(_l){var _o2={ad_units:_m,s2sConfig:e,ortb2Fragments:u,requestBidsTimeout:d};if(_o2.ad_units.length){var _e8=_b.map(function(e){return e.start=(0,i.vE)(),function(t){t||c(e.bidderRequestId),r.apply(e,arguments)}});var _s4=(0,i.ZA)(_o2.ad_units).filter(function(e){return _a2.includes(e)});(0,i.OG)("CALLING S2S HEADER BIDDERS \x3d\x3d\x3d\x3d ".concat(_s4.length>0?_s4.join(", "):'No bidder specified, using "ortb2Imp" definition(s) only')),_b.forEach(function(e){y.Ic(v.qY.BID_REQUESTED,
_objectSpread(_objectSpread({},e),{},{tid:e.auctionId}))}),_l.callBids(_o2,f,n,function(t){return _e8.forEach(function(e){return e(t)})},_t5)}}else(0,i.vV)("missing "+e.adapter);p++}}),l.forEach(function(e){e.start=(0,i.vE)();var t=M[e.bidderCode];a.$W.runWithBidder(e.bidderCode,function(){(0,i.OG)("CALLING BIDDER"),y.Ic(v.qY.BID_REQUESTED,e)});var u=(0,s.g4)(d,o?{request:o.request.bind(null,e.bidderCode),done:o.done}:void 0);var l=r.bind(e);try{a.$W.runWithBidder(e.bidderCode,t.callBids.bind(t,e,
n,l,u,function(){return c(e.bidderRequestId)},a.$W.callbackWithBidder(e.bidderCode)))}catch(t){(0,i.vV)("".concat(e.bidderCode," Bid Adapter emitted an uncaught error when parsing their bidRequest"),{e:t,bidRequest:e}),l()}})},V.videoAdapters=[],V.registerBidAdapter=function(e,t){var _e$getSpec;var _ref6=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref6$supportedMediaT=_ref6.supportedMediaTypes,n=_ref6$supportedMediaT===void 0?[]:_ref6$supportedMediaT;e&&t?"function"==typeof e.callBids?
(M[t]=e,b.o2.register(w.tW,t,(_e$getSpec=e.getSpec)===null||_e$getSpec===void 0?void 0:_e$getSpec.call(e).gvlid),n.includes("video")&&V.videoAdapters.push(t),n.includes("native")&&r.mT.push(t)):(0,i.vV)("Bidder adaptor error for bidder code: "+t+"bidder must implement a callBids() function"):(0,i.vV)("bidAdapter or bidderCode not specified")},V.aliasBidAdapter=function(e,t,n){if(void 0===M[t]){var _s5=M[e];if(void 0===_s5){var _n2=[];F.forEach(function(i){if(i.bidders&&i.bidders.length){var _r2=i&&
i.bidders;i&&_r2.includes(t)?G[t]=e:_n2.push(e)}}),_n2.forEach(function(e){(0,i.vV)('bidderCode "'+e+'" is not an existing bidder.',"adapterManager.aliasBidAdapter")})}else try{var _a3,_d2=function(e){var t=[];return V.videoAdapters.includes(e)&&t.push("video"),r.mT.includes(e)&&t.push("native"),t}(e);if(_s5.constructor.prototype!=Object.prototype)_a3=new _s5.constructor,_a3.setBidderCode(t);else{var _ref7=n||{},_ref7$useBaseGvlid=_ref7.useBaseGvlid,_r3=_ref7$useBaseGvlid===void 0?!1:_ref7$useBaseGvlid;
var _d3=_s5.getSpec();var _c2=_r3?_d3.gvlid:n===null||n===void 0?void 0:n.gvlid;null==_c2&&null!=_d3.gvlid&&(0,i.JE)("Alias '".concat(t,"' will NOT re-use the GVL ID of the original adapter ('").concat(_d3.code,"', gvlid: ").concat(_d3.gvlid,"). Functionality that requires TCF consent may not work as expected."));var _u=n&&n.skipPbsAliasing;_a3=(0,o.xb)(Object.assign({},_d3,{code:t,gvlid:_c2,skipPbsAliasing:_u})),G[t]=e}V.registerBidAdapter(_a3,t,{supportedMediaTypes:_d2})}catch(t){(0,i.vV)(e+" bidder does not currently support aliasing.",
"adapterManager.aliasBidAdapter")}}else(0,i.OG)('alias name "'+t+'" has been already specified.')},V.resolveAlias=function(e){var t,n=e;for(;G[n]&&(!t||!t.has(n));)n=G[n],(t=t||new Set).add(n);return n},V.registerAnalyticsAdapter=function(e){var t=e.adapter,n=e.code,r=e.gvlid;t&&n?"function"==typeof t.enableAnalytics?(t.code=n,H[n]={adapter:t,gvlid:r},b.o2.register(w.Tn,n,r)):(0,i.vV)('Prebid Error: Analytics adaptor error for analytics "'.concat(n,'"\n        analytics adapter must implement an enableAnalytics() function')):
(0,i.vV)("Prebid Error: analyticsAdapter or analyticsCode not specified")},V.enableAnalytics=function(e){(0,i.cy)(e)||(e=[e]),e.forEach(function(e){var t=H[e.provider];t&&t.adapter?P.isAllowed(I.mo,L(w.Tn,e.provider,_defineProperty({},C.TQ,e)))&&t.adapter.enableAnalytics(e):(0,i.vV)("Prebid Error: no analytics adapter found in registry for '".concat(e.provider,"'."))})},V.getBidAdapter=function(e){return M[e]},V.getAnalyticsAdapter=function(e){return H[e]},V.callTimedOutBidders=function(e,t,n){t=
t.map(function(t){return t.params=(0,i.SB)(e,t.adUnitCode,t.bidder),t.timeout=n,t}),t=(0,i.$z)(t,"bidder"),Object.keys(t).forEach(function(e){te(e,"onTimeout",t[e])})},V.callBidWonBidder=function(e,t,n){var r,o;t.params=(0,i.SB)(n,t.adUnitCode,t.bidder),r=t.adUnitCode,o=t.bidder,u(r,"winsCounter",o),te(e,"onBidWon",t)},V.triggerBilling=function(){var e=new WeakSet;return function(t){var _x$OA;e.has(t)||(e.add(t),(((_x$OA=(0,x.$T)(t.eventtrackers)[x.OA])===null||_x$OA===void 0?void 0:_x$OA[x.Ni])||
[]).forEach(function(e){return i.mM.triggerPixel(e)}),te(t.bidder,"onBidBillable",t))}}(),V.callSetTargetingBidder=function(e,t){te(e,"onSetTargeting",t)},V.callBidViewableBidder=function(e,t){te(e,"onBidViewable",t)},V.callBidderError=function(e,t,n){te(e,"onBidderError",{error:t,bidderRequest:n})},V.callAdRenderSucceededBidder=function(e,t){te(e,"onAdRenderSucceeded",t)},V.callDataDeletionRequest=(0,d.A_)("sync",function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];
var r="onDataDeletionRequest";Object.keys(M).filter(function(e){return!G.hasOwnProperty(e)}).forEach(function(e){var n=Z(e,r);if(null!=n){var _i3=A.n.getBidsRequested().filter(function(t){return function(e){var t=new Set;for(;G.hasOwnProperty(e)&&!t.has(e);)t.add(e),e=G[e];return e}(t.bidderCode)===e});ee.apply(void 0,[e,r].concat(_toConsumableArray(n),[_i3],t))}}),Object.entries(H).forEach(function(e){var _o$adapter;var _e9=_slicedToArray(e,2),n=_e9[0],o=_e9[1];var s=o===null||o===void 0||(_o$adapter=
o.adapter)===null||_o$adapter===void 0?void 0:_o$adapter[r];if("function"==typeof s)try{s.apply(o.adapter,t)}catch(e){(0,i.vV)("error calling ".concat(r," of ").concat(n),e)}})});var ne=V},9115:function _(e,t,n){function i(e){var t=e;return{callBids:function callBids(){},setBidderCode:function setBidderCode(e){t=e},getBidderCode:function getBidderCode(){return t}}}n.d(t,{xb:function xb(){return C},a$:function a$(){return T}});var r=n(8046),o=n(3272),s=n(3597),a=n(8230),d=n(2449),c=n(3895),u=n(8969),
l=n(5023),f=n(1069),g=n(9214),p=n(6881),h=n(2693),m=n(6894),b=n(5139),y=n(3441),v=n(5569),E=n(6811);var A=["cpm","ttl","creativeId","netRevenue","currency"],w=["auctionId","transactionId"];function T(e){var t=Array.isArray(e.supportedMediaTypes)?{supportedMediaTypes:e.supportedMediaTypes}:void 0;function n(e){var n=C(e);r.Ay.registerBidAdapter(n,e.code,t)}n(e),Array.isArray(e.aliases)&&e.aliases.forEach(function(t){var i,o,s=t;(0,f.Qd)(t)&&(s=t.code,i=t.gvlid,o=t.skipPbsAliasing),r.Ay.aliasRegistry[s]=
e.code,n(Object.assign({},e,{code:s,gvlid:i,skipPbsAliasing:o}))})}var I=(0,f.Bj)(function(e){var t=e.bidderCode;if((0,b.io)(E.VJ,(0,y.s)(v.tW,t)))return{bidRequest:function bidRequest(e){return e},bidderRequest:function bidderRequest(e){return e}};function n(e,t,n){return w.includes(t)?null:Reflect.get(e,t,n)}function i(e,t){var n=new Proxy(e,t);return Object.entries(e).filter(function(e){var _e0=_slicedToArray(e,2),t=_e0[0],n=_e0[1];return"function"==typeof n}).forEach(function(t){var _t6=_slicedToArray(t,
2),i=_t6[0],r=_t6[1];return n[i]=r.bind(e)}),n}var r=(0,f.Bj)(function(e){return i(e,{get:n})},function(e){return e.bidId});return{bidRequest:r,bidderRequest:function bidderRequest(e){return i(e,{get:function get(t,i,o){return"bids"===i?e.bids.map(r):n(t,i,o)}})}}});function C(e){return Object.assign(new i(e.code),{getSpec:function getSpec(){return Object.freeze(Object.assign({},e))},registerSyncs:t,callBids:function callBids(n,i,a,g,b,y){if(!Array.isArray(n.bids))return;var v=I(n),E={};function T(e,
t){var n=(0,m.BO)(t.metrics);n.checkpoint("addBidResponse"),E[e]=!0,n.measureTime("addBidResponse.validate",function(){return function(e,t){var _ref8=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref8$index=_ref8.index,n=_ref8$index===void 0?p.n.index:_ref8$index;function i(){var e=Object.keys(t);return A.every(function(n){return e.includes(n)&&![void 0,null].includes(t[n])})}function r(e){return"Invalid bid from ".concat(t.bidderCode,". Ignoring bid: ").concat(e)}if(!e)return(0,f.JE)("No adUnitCode was supplied to addBidResponse."),
!1;if(!t)return(0,f.JE)("Some adapter tried to add an undefined bid for ".concat(e,".")),!1;if(!i())return(0,f.vV)(r("Bidder ".concat(t.bidderCode," is missing required params. Check http://prebid.org/dev-docs/bidder-adapter-1.html for list of params."))),!1;if("native"===t.mediaType&&!(0,d.Bm)(t,{index:n}))return(0,f.vV)(r("Native bid missing some required properties.")),!1;if("video"===t.mediaType&&!(0,c.vk)(t,{index:n}))return(0,f.vV)(r("Video bid does not have required vastUrl or renderer property")),
!1;if("banner"===t.mediaType&&!function(e,t){var _ref9=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref9$index=_ref9.index,n=_ref9$index===void 0?p.n.index:_ref9$index;if((t.width||0===parseInt(t.width,10))&&(t.height||0===parseInt(t.height,10)))return t.width=parseInt(t.width,10),t.height=parseInt(t.height,10),!0;if(null!=t.wratio&&null!=t.hratio)return t.wratio=parseInt(t.wratio,10),t.hratio=parseInt(t.hratio,10),!0;var i=n.getBidRequest(t),r=n.getMediaTypes(t),o=i&&i.sizes||r&&r.banner&&
r.banner.sizes,s=(0,f.kK)(o);if(1===s.length){var _s$0$split=s[0].split("x"),_s$0$split2=_slicedToArray(_s$0$split,2),_e1=_s$0$split2[0],_n3=_s$0$split2[1];return t.width=parseInt(_e1,10),t.height=parseInt(_n3,10),!0}return!1}(e,t,{index:n}))return(0,f.vV)(r("Banner bids require a width and height")),!1;return!0}(e,t)})?i(e,t):i.reject(e,t,u.Tf.INVALID)}var C=[];function B(){a(),o.$W.runWithBidder(e.code,function(){l.Ic(u.qY.BIDDER_DONE,n),t(C,n.gdprConsent,n.uspConsent,n.gppConsent)})}var R=k(n).measureTime("validate",
function(){return n.bids.filter(function(t){return function(t){if(!e.isBidRequestValid(t))return(0,f.JE)("Invalid bid sent to bidder ".concat(e.code,": ").concat(JSON.stringify(t))),!1;return!0}(v.bidRequest(t))})});if(0===R.length)return void B();var U={};R.forEach(function(e){U[e.bidId]=e,e.adUnitCode||(e.adUnitCode=e.placementCode)}),O(e,R,n,g,y,{onRequest:function onRequest(e){return l.Ic(u.qY.BEFORE_BIDDER_HTTP,n,e)},onResponse:function onResponse(t){b(e.code),C.push(t)},onPaapi:function onPaapi(e){var t=
U[e.bidId];t?S(t,e):(0,f.JE)("Received fledge auction configuration for an unknown bidId",e)},onError:function onError(t,i){i.timedOut||b(e.code),r.Ay.callBidderError(e.code,i,n),l.Ic(u.qY.BIDDER_ERROR,{error:i,bidderRequest:n}),(0,f.vV)("Server call for ".concat(e.code," failed: ").concat(t," ").concat(i.status,". Continuing without bids."),{bidRequests:R})},onBid:function onBid(t){var n=U[t.requestId];if(n){var _t$deferRendering;if(t.adapterCode=n.bidder,function(e,t){var n=h.u.get(t,"allowAlternateBidderCodes")||
!1,i=h.u.get(t,"allowedAlternateBidderCodes");if(e&&t&&t!==e&&(i=(0,f.cy)(i)?i.map(function(e){return e.trim().toLowerCase()}).filter(function(e){return!!e}).filter(f.hj):i,!n||(0,f.cy)(i)&&"*"!==i[0]&&!i.includes(e)))return!0;return!1}(t.bidderCode,n.bidder))return(0,f.JE)("".concat(t.bidderCode," is not a registered partner or known bidder of ").concat(n.bidder,", hence continuing without bid. If you wish to support this bidder, please mark allowAlternateBidderCodes as true in bidderSettings.")),
void i.reject(n.adUnitCode,t,u.Tf.BIDDER_DISALLOWED);t.originalCpm=t.cpm,t.originalCurrency=t.currency,t.meta=t.meta||Object.assign({},t[n.bidder]),t.deferBilling=n.deferBilling,t.deferRendering=t.deferBilling&&((_t$deferRendering=t.deferRendering)!==null&&_t$deferRendering!==void 0?_t$deferRendering:"function"!=typeof e.onBidBillable);var _r4=Object.assign((0,s.O)(u.XQ.GOOD,n),t,(0,f.Up)(n,w));T(n.adUnitCode,_r4)}else(0,f.JE)("Bidder ".concat(e.code," made bid for unknown request ID: ").concat(t.requestId,
". Ignoring.")),i.reject(null,t,u.Tf.INVALID_REQUEST_ID)},onCompletion:B})}});function t(t,n,i,r){R(e,t,n,i,r)}}var B=["bids","paapi"],O=(0,g.A_)("async",function(e,t,n,i,r,o){var s=o.onRequest,a=o.onResponse,d=o.onPaapi,c=o.onError,l=o.onBid,g=o.onCompletion;var p=k(n);g=p.startTiming("total").stopBefore(g);var m=I(n);var A=p.measureTime("buildRequests",function(){return e.buildRequests(t.map(m.bidRequest),m.bidderRequest(n))});if(!A||0===A.length)return void g();Array.isArray(A)||(A=[A]);var w=
(0,f.U6)(g,A.length);A.forEach(function(t){var _t$options;var n=p.fork();function o(e){null!=e&&(e.metrics=n.fork().renameWith()),l(e)}var g=r(function(i,r){A();try{i=JSON.parse(i)}catch(e){}i={body:i,headers:{get:r.getResponseHeader.bind(r)}},a(i);try{i=n.measureTime("interpretResponse",function(){return e.interpretResponse(i,t)})}catch(t){return(0,f.vV)("Bidder ".concat(e.code," failed to interpret the server's response. Continuing without bids"),null,t),void w()}var s,c;i&&!Object.keys(i).some(function(e){return!B.includes(e)})?
(s=i.bids,c=i.paapi):s=i,(0,f.cy)(c)&&c.forEach(d),s&&((0,f.cy)(s)?s.forEach(o):o(s)),w()}),m=r(function(e,t){A(),c(e,t),w()});s(t);var A=n.startTiming("net"),T="TRUE"===(0,f.Ez)(u.M).toUpperCase()||(0,f.dp)();function I(n){var _h$u$get;var i=t.options;return Object.assign(n,i,{browsingTopics:!(i!==null&&i!==void 0&&i.hasOwnProperty("browsingTopics")&&!i.browsingTopics)&&((_h$u$get=h.u.get(e.code,"topicsHeader"))!==null&&_h$u$get!==void 0?_h$u$get:!0)&&(0,b.io)(E.DL,(0,y.s)(v.tW,e.code)),suppressTopicsEnrollmentWarning:i!==
null&&i!==void 0&&i.hasOwnProperty("suppressTopicsEnrollmentWarning")?i.suppressTopicsEnrollmentWarning:!T})}switch(t.method){case "GET":i("".concat(t.url).concat(function(e){if(e)return"?".concat("object"==_typeof(e)?(0,f.bL)(e):e);return""}(t.data)),{success:g,error:m},void 0,I({method:"GET",withCredentials:!0}));break;case "POST":var _n4=(_t$options=t.options)===null||_t$options===void 0?void 0:_t$options.endpointCompression,_r5=function _r5(e){var t=e.url,n=e.payload;i(t,{success:g,error:m},n,
I({method:"POST",contentType:"text/plain",withCredentials:!0}))};_n4&&T&&(0,f.JE)("Skipping GZIP compression for ".concat(e.code," as debug mode is enabled")),_n4&&!T&&(0,f.nT)()?(0,f.ZK)(t.data).then(function(e){var n=new URL(t.url,window.location.origin);n.searchParams.has("gzip")||n.searchParams.set("gzip","1"),_r5({url:n.href,payload:e})}):_r5({url:t.url,payload:"string"==typeof t.data?t.data:JSON.stringify(t.data)});break;default:(0,f.JE)("Skipping invalid request from ".concat(e.code,". Request type ").concat(t.type,
" must be GET or POST")),w()}})},"processBidderRequests"),R=(0,g.A_)("async",function(e,t,n,i,s){var d=o.$W.getConfig("userSync.aliasSyncEnabled");if(e.getUserSyncs&&(d||!r.Ay.aliasRegistry[e.code])){var _r6=e.getUserSyncs({iframeEnabled:a.zt.canBidderRegisterSync("iframe",e.code),pixelEnabled:a.zt.canBidderRegisterSync("image",e.code)},t,n,i,s);_r6&&(Array.isArray(_r6)||(_r6=[_r6]),_r6.forEach(function(t){a.zt.registerSync(t.type,e.code,t.url)}),a.zt.bidderDone(e.code))}},"registerSyncs"),S=(0,g.A_)("sync",
function(e,t){},"addPaapiConfig");function k(e){return(0,m.BO)(e.metrics).renameWith(function(t){return["adapter.client.".concat(t),"adapters.client.".concat(e.bidderCode,".").concat(t)]})}},1580:function _(e,t,n){n.d(t,{R:function R(){return c}});var i=n(6811),r=n(3441),o=n(5139),s=n(1069);var a=new WeakMap,d=["debugging","outstream","aaxBlockmeter","adagio","adloox","akamaidap","arcspan","airgrid","browsi","brandmetrics","clean.io","humansecurity","confiant","contxtful","hadron","mediafilter","medianet",
"azerionedge","a1Media","geoedge","qortex","dynamicAdBoost","51Degrees","symitridap","wurfl","nodalsAi","anonymised","optable","justtag","tncId","ftrackId","id5"];function c(e,t,n,c,u,l){if(!(0,o.io)(i.pY,(0,r.s)(t,n)))return;if(!n||!e)return void(0,s.vV)("cannot load external script without url and moduleCode");if(!d.includes(n))return void(0,s.vV)("".concat(n," not whitelisted for loading external JavaScript"));u||(u=document);var f=h(u,e);if(f)return c&&"function"==typeof c&&(f.loaded?c():f.callbacks.push(c)),
f.tag;var g=a.get(u)||{},p={loaded:!1,tag:null,callbacks:[]};return g[e]=p,a.set(u,g),c&&"function"==typeof c&&p.callbacks.push(c),(0,s.JE)("module ".concat(n," is loading external JavaScript")),function(t,n,i,r){i||(i=document);var o=i.createElement("script");o.type="text/javascript",o.async=!0;var a=h(i,e);a&&(a.tag=o);o.readyState?o.onreadystatechange=function(){"loaded"!==o.readyState&&"complete"!==o.readyState||(o.onreadystatechange=null,n())}:o.onload=function(){n()};o.src=t,r&&(0,s.Bg)(o,r);
return(0,s._s)(o,i),o}(e,function(){p.loaded=!0;try{for(var _e10=0;_e10<p.callbacks.length;_e10++)p.callbacks[_e10]()}catch(e){(0,s.vV)("Error executing callback","adloader.js:loadExternalScript",e)}},u,l);function h(e,t){var n=a.get(e);return n&&n[t]?n[t]:null}}},8044:function _(e,t,n){n.d(t,{RD:function RD(){return f},g4:function g4(){return l}});var i=n(3272),r=n(1069);var o={fetch:window.fetch.bind(window),makeRequest:function makeRequest(e,t){return new Request(e,t)},timeout:function timeout(e,
t){var n=new AbortController;var i=setTimeout(function(){n.abort(),(0,r.vV)("Request timeout after ".concat(e,"ms"),t),i=null},e);return{signal:n.signal,done:function done(){i&&clearTimeout(i)}}}},s="GET",a="POST",d="Content-Type";function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3E3,_ref0=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=_ref0.request,n=_ref0.done,r=function r(t,n){var _n5,_r7;var r;null==e||null!=((_n5=n)===null||_n5===void 0?void 0:_n5.signal)||
i.$W.getConfig("disableAjaxTimeout")||(r=o.timeout(e,t),n=Object.assign({signal:r.signal},n));var s=o.fetch(t,n);return null!=((_r7=r)===null||_r7===void 0?void 0:_r7.done)&&(s=s["finally"](r.done)),s};return null==t&&null==n||(r=function(e){return function(i,r){var o=(new URL(null==(i===null||i===void 0?void 0:i.url)?i:i.url,document.location)).origin;var s=e(i,r);return t&&t(o),n&&(s=s["finally"](function(){return n(o)})),s}}(r)),r}function u(e,t){var n=e.status,_e$statusText=e.statusText,i=_e$statusText===
void 0?"":_e$statusText,o=e.headers,s=e.url,a=0;function c(e){if(0===a)try{var _o$get;a=(new DOMParser).parseFromString(t,o===null||o===void 0||(_o$get=o.get(d))===null||_o$get===void 0||(_o$get=_o$get.split(";"))===null||_o$get===void 0?void 0:_o$get[0])}catch(t){a=null,e&&e(t)}return a}return{readyState:XMLHttpRequest.DONE,status:n,statusText:i,responseText:t,response:t,responseType:"",responseURL:s,get responseXML(){return c(r.vV)},getResponseHeader:function getResponseHeader(e){return o!==null&&
o!==void 0&&o.has(e)?o.get(e):null},toJSON:function toJSON(){return Object.assign({responseXML:c()},this)},timedOut:!1}}function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3E3,_ref1=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=_ref1.request,n=_ref1.done;var i=c(e,{request:t,done:n});return function(e,t,n){!function(e,t){var _ref10="object"==_typeof(t)&&null!=t?t:{success:"function"==typeof t?t:function(){return null},error:function error(e,t){return(0,r.vV)("Network error",
e,t)}},n=_ref10.success,i=_ref10.error;e.then(function(e){return e.text().then(function(t){return[e,t]})}).then(function(e){var _e11=_slicedToArray(e,2),t=_e11[0],r=_e11[1];var o=u(t,r);t.ok||304===t.status?n(r,o):i(t.statusText,o)},function(e){return i("",Object.assign(u({status:0},""),{reason:e,timedOut:"AbortError"===(e===null||e===void 0?void 0:e.name)}))})}(i(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var i=n.method||(t?a:s);if(i===s&&t){var _i4=(0,r.Dl)(e,
n);Object.assign(_i4.search,t),e=(0,r.c$)(_i4)}var c=new Headers(n.customHeaders);c.set(d,n.contentType||"text/plain");var u={method:i,headers:c};return i!==s&&t&&(u.body=t),n.withCredentials&&(u.credentials="include"),isSecureContext&&(["browsingTopics","adAuctionHeaders"].forEach(function(e){n[e]&&(u[e]=!0)}),null!=n.suppressTopicsEnrollmentWarning&&(u.suppressTopicsEnrollmentWarning=n.suppressTopicsEnrollmentWarning)),n.keepalive&&(u.keepalive=!0),o.makeRequest(e,u)}(e,n,arguments.length>3&&void 0!==
arguments[3]?arguments[3]:{})),t)}}var f=l();c()},6881:function _(e,t,n){n.d(t,{n:function n(){return ae}});var i=n(1069),r=n(6833),o=n(2449),s=n(8044),a=n(3272);var d=15,c=new Map;function u(e){var _ref11=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref11$index=_ref11.index,t=_ref11$index===void 0?ae.index:_ref11$index;var n=l(e),i=t.getAuction(e);var r={type:"xml",value:n,ttlseconds:Number(e.ttl)+d};return a.$W.getConfig("cache.vasttrack")&&(r.bidder=e.bidder,r.bidid=e.requestId,r.aid=
e.auctionId),null!=i&&(r.timestamp=i.getAuctionStart()),"string"==typeof e.customCacheKey&&""!==e.customCacheKey&&(r.key=e.customCacheKey),r}function l(e){return e.vastXml?e.vastXml:(t=e.vastUrl,n=e.vastImpUrl,'\x3cVAST version\x3d"3.0"\x3e\n    \x3cAd\x3e\n      \x3cWrapper\x3e\n        \x3cAdSystem\x3eprebid.org wrapper\x3c/AdSystem\x3e\n        \x3cVASTAdTagURI\x3e\x3c![CDATA['.concat(t,"]]\x3e\x3c/VASTAdTagURI\x3e\n        ").concat((n=n&&(Array.isArray(n)?n:[n]))?n.map(function(e){return"\x3cImpression\x3e\x3c![CDATA[".concat(e,
"]]\x3e\x3c/Impression\x3e")}).join(""):"","\n        \x3cCreatives\x3e\x3c/Creatives\x3e\n      \x3c/Wrapper\x3e\n    \x3c/Ad\x3e\n  \x3c/VAST\x3e"));var t,n}var f=function f(e){var t=l(e),n=URL.createObjectURL(new Blob([t],{type:"text/xml"}));g(e,n),c.set(e.videoCacheKey,n)},g=function g(e,t,n){e.videoCacheKey=n||(0,i.lk)(),e.vastUrl||(e.vastUrl=t)},p={store:function store(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.g4;var i={puts:e.map(u)};n(a.$W.getConfig("cache.timeout"))(a.$W.getConfig("cache.url"),
function(e){return{success:function success(t){var n;try{n=JSON.parse(t).responses}catch(t){return void e(t,[])}n?e(null,n):e(new Error("The cache server didn't respond with a responses property."),[])},error:function error(t,n){e(new Error("Error storing video ad in the cache: ".concat(t,": ").concat(JSON.stringify(n))),[])}}}(t),JSON.stringify(i),{contentType:"text/plain",withCredentials:!0})}};function h(e){var t=e.map(function(e){return e.bidResponse});p.store(t,function(n,r){var o;n?(o=n,(0,
i.vV)("Failed to save to the video cache: ".concat(o,". Video bids will be discarded:"),t)):e.length!==r.length?(0,i.vV)("expected ".concat(e.length," cache IDs, got ").concat(r.length," instead")):r.forEach(function(t,n){var _e$n=e[n],r=_e$n.auctionInstance,o=_e$n.bidResponse,s=_e$n.afterBidAdded;var d;""===t.uuid?(0,i.JE)("Supplied video cache key was already in use by Prebid Cache; caching attempt was rejected. Video bid must be discarded."):(g(o,(d=t.uuid,"".concat(a.$W.getConfig("cache.url"),
"?uuid\x3d").concat(d)),t.uuid),J(r,o),s())})})}var m,b,y;a.$W.getConfig("cache",function(e){var t=e.cache;m="number"==typeof t.batchSize&&t.batchSize>0?t.batchSize:1,b="number"==typeof t.batchTimeout&&t.batchTimeout>0?t.batchTimeout:0,t.useLocal&&!y&&(y=ae.onExpiry(function(e){e.getBidsReceived().forEach(function(e){var t=c.get(e.videoCacheKey);t&&t.startsWith("blob")&&URL.revokeObjectURL(t),c["delete"](e.videoCacheKey)})}))});var v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:
setTimeout,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:h,n=[[]],i=!1;var r=function r(e){return e()};return function(o,s,a){var d=b>0?e:r;n[n.length-1].length>=m&&n.push([]),n[n.length-1].push({auctionInstance:o,bidResponse:s,afterBidAdded:a}),i||(i=!0,d(function(){n.forEach(t),n=[[]],i=!1},b))}}();var E=n(5789),A=n(8230),w=n(9214),T=n(3895),I=n(1371),C=n(2693),B=n(5023),O=n(8046),R=n(8969),S=n(5555),k=n(6894);var U=n(7873),D=n(6853),_=n(7863);var $=A.zt.syncUsers,j="started",q="inProgress",
x="completed";B.on(R.qY.BID_ADJUSTMENT,function(e){!function(e){var t=function(e,t,n,_n6){var _ref12=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},_ref12$index=_ref12.index,r=_ref12$index===void 0?ae.index:_ref12$index,_ref12$bs=_ref12.bs,o=_ref12$bs===void 0?C.u:_ref12$bs;n=n||r.getBidRequest(t);var s=t===null||t===void 0?void 0:t.adapterCode,a=(t===null||t===void 0?void 0:t.bidderCode)||((_n6=n)===null||_n6===void 0?void 0:_n6.bidder),d=o.get(t===null||t===void 0?void 0:t.adapterCode,
"adjustAlternateBids"),c=o.getOwn(a,"bidCpmAdjustment")||o.get(d?s:a,"bidCpmAdjustment");if(c&&"function"==typeof c)try{return c(e,Object.assign({},t),n)}catch(e){(0,i.vV)("Error during bid adjustment",e)}return e}(e.cpm,e);t>=0&&(e.cpm=t)}(e)});var N=4,W={},P={},V=[],M=(0,U.m)();function G(e){var t=e.adUnits,n=e.adUnitCodes,s=e.callback,d=e.cbTimeout,c=e.labels,u=e.auctionId,l=e.ortb2Fragments,g=e.metrics;g=(0,k.BO)(g);var p=t,h=c,m=n,b=u||(0,i.lk)(),y=d,v=new Set,A=(0,S.v6)(),w=(0,S.v6)();var C,
U,G,Y,X=[],Z=s,ee=[],te=(0,D.H)({startTime:function startTime(e){return e.responseTimestamp},ttl:function ttl(e){return null==(0,_.S9)()?null:1E3*Math.max((0,_.S9)(),e.ttl)}}),ne=[],ie=[],re=[];function se(){return{auctionId:b,timestamp:C,auctionEnd:U,auctionStatus:Y,adUnits:p,adUnitCodes:m,labels:h,bidderRequests:ee,noBids:ne,bidsReceived:te.toArray(),bidsRejected:X,winningBids:ie,timeout:y,metrics:g,seatNonBids:re}}function de(e){if(e?B.Ic(R.qY.AUCTION_TIMEOUT,se()):clearTimeout(G),void 0===U){var _n7=
[];e&&((0,i.OG)("Auction ".concat(b," timedOut")),_n7=ee.filter(function(e){return!v.has(e.bidderRequestId)}).flatMap(function(e){return e.bids}),_n7.length&&B.Ic(R.qY.BID_TIMEOUT,_n7)),Y=x,U=Date.now(),g.checkpoint("auctionEnd"),g.timeBetween("requestBids","auctionEnd","requestBids.total"),g.timeBetween("callBids","auctionEnd","requestBids.callBids"),A.resolve(),B.Ic(R.qY.AUCTION_END,se()),z(p,function(){try{if(null!=Z){var _t7=te.toArray().filter(function(e){return m.includes(e.adUnitCode)}).reduce(oe,
{});Z.apply(M,[_t7,e,b]),Z=null}}catch(e){(0,i.vV)("Error executing bidsBackHandler",null,e)}finally{_n7.length&&O.Ay.callTimedOutBidders(t,_n7,y);var _e12=a.$W.getConfig("userSync")||{};_e12.enableOverride||$(_e12.syncDelay)}})}}function ce(){a.$W.resetBidder(),(0,i.fH)("Bids Received for Auction with id: ".concat(b),te.toArray()),Y=x,de(!1)}function ue(e){v.add(e)}function le(e){var _this3=this;e.forEach(function(e){var t;t=e,ee=ee.concat(t)});var t={},n={bidRequests:e,run:function run(){G=setTimeout(function(){return de(!0)},
y),Y=q,B.Ic(R.qY.AUCTION_INIT,se());var n=function(e,t){var _ref13=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref13$index=_ref13.index,n=_ref13$index===void 0?ae.index:_ref13$index,s=0,d=!1,c=new Set,u={};function l(){s--,d&&0===s&&e()}function g(e,t,n){return u[t.requestId]=!0,function(e,t){var _ref14=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref14$index=_ref14.index,n=_ref14$index===void 0?ae.index:_ref14$index;var r=n.getBidderRequest(e),o=n.getAdUnit(e),s=r&&r.start||
e.requestTimestamp;Object.assign(e,{responseTimestamp:e.responseTimestamp||(0,i.vE)(),requestTimestamp:e.requestTimestamp||s,cpm:parseFloat(e.cpm)||0,bidder:e.bidder||e.bidderCode,adUnitCode:t}),null!=(o===null||o===void 0?void 0:o.ttlBuffer)&&(e.ttlBuffer=o.ttlBuffer);e.timeToRespond=e.responseTimestamp-e.requestTimestamp}(t,e),s++,n(l)}function p(e,s){g(e,s,function(e){var d=function(e,_n$ortb2Imp,_t$getBidRequest){var _ref15=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref15$index=
_ref15.index,t=_ref15$index===void 0?ae.index:_ref15$index;B.Ic(R.qY.BID_ADJUSTMENT,e);var n=t.getAdUnit(e);e.instl=1===(n===null||n===void 0||(_n$ortb2Imp=n.ortb2Imp)===null||_n$ortb2Imp===void 0?void 0:_n$ortb2Imp.instl);var i=((_t$getBidRequest=t.getBidRequest(e))===null||_t$getBidRequest===void 0?void 0:_t$getBidRequest.renderer)||n.renderer,o=e.mediaType,s=t.getMediaTypes(e),d=s&&s[o];var c=d&&d.renderer,u=null;!c||!c.render||!0===c.backupOnly&&e.renderer?!i||!i.render||!0===i.backupOnly&&e.renderer||
(u=i):u=c;u&&(e.renderer=E.A4.install({url:u.url,config:u.options,renderNow:null==u.url}),e.renderer.setRender(u.render));var l=K(e.mediaType,s,a.$W.getConfig("mediaTypePriceGranularity")),f=(0,r.j)(e.cpm,"object"==_typeof(l)?l:a.$W.getConfig("customPriceBucket"),a.$W.getConfig("currency.granularityMultiplier"));return e.pbLg=f.low,e.pbMg=f.med,e.pbHg=f.high,e.pbAg=f.auto,e.pbDg=f.dense,e.pbCg=f.custom,e}(s);B.Ic(R.qY.BID_ACCEPTED,d),d.mediaType===I.G_?function(e,t,n,_r$getMediaTypes){var _ref16=
arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},_ref16$index=_ref16.index,r=_ref16$index===void 0?ae.index:_ref16$index,o=!0;var s=(_r$getMediaTypes=r.getMediaTypes({requestId:t.originalRequestId||t.requestId,adUnitId:t.adUnitId}))===null||_r$getMediaTypes===void 0?void 0:_r$getMediaTypes.video,d=s&&(s===null||s===void 0?void 0:s.context),c=s&&(s===null||s===void 0?void 0:s.useCacheKey),_ref17=a.$W.getConfig("cache")||{},u=_ref17.useLocal,l=_ref17.url,g=_ref17.ignoreBidderCacheKey;u?f(t):
l&&(c||d!==T.H6)&&(!t.videoCacheKey||g?(o=!1,Q(e,t,n,s)):t.vastUrl||((0,i.vV)("videoCacheKey specified but not required vastUrl for video bid"),o=!1));o&&(J(e,t),n())}(t,d,e):((0,o.l6)(d)&&(0,o.gs)(d,n.getAdUnit(d)),J(t,d),e())})}function h(e,n,r){return g(e,n,function(e){n.rejectionReason=r,(0,i.JE)("Bid from ".concat(n.bidder||"unknown bidder"," was rejected: ").concat(r),n),B.Ic(R.qY.BID_REJECTED,n),t.addBidRejected(n),e()})}function m(){var n=this,r=t.getBidRequests();var o=a.$W.getConfig("auctionOptions");
if(c.add(n),o&&!(0,i.Im)(o)){var _e13=o.secondaryBidders;_e13&&!r.every(function(t){return _e13.includes(t.bidderCode)})&&(r=r.filter(function(t){return!_e13.includes(t.bidderCode)}))}d=r.every(function(e){return c.has(e)}),n.bids.forEach(function(e){u[e.bidId]||(t.addNoBid(e),B.Ic(R.qY.NO_BID,e))}),d&&0===s&&e()}return{addBidResponse:function(){function e(e,t){F.call({dispatch:p},e,t,function(){var n=!1;return function(i){n||(h(e,t,i),n=!0)}}())}return e.reject=h,e}(),adapterDone:function adapterDone(){var _this4=
this;H(S.U9.resolve())["finally"](function(){return m.call(_this4)})}}}(ce,_this3);O.Ay.callBids(p,e,n.addBidResponse,n.adapterDone,{request:function request(e,n){d(W,n),d(t,e),P[e]||(P[e]={SRA:!0,origin:n}),t[e]>1&&(P[e].SRA=!1)},done:function done(e){W[e]--,V[0]&&s(V[0])&&V.shift()}},y,ue,l),w.resolve()}};function s(e){var t=!0,n=a.$W.getConfig("maxRequestsPerOrigin")||N;return e.bidRequests.some(function(e){var i=1,r=void 0!==e.src&&e.src===R.RW.SRC?"s2s":e.bidderCode;return P[r]&&(!1===P[r].SRA&&
(i=Math.min(e.bids.length,n)),W[P[r].origin]+i>n&&(t=!1)),!t}),t&&e.run(),t}function d(e,t){void 0===e[t]?e[t]=1:e[t]++}s(n)||((0,i.JE)("queueing auction due to limited endpoint capacity"),V.push(n))}return(0,_.lc)(function(){return te.refresh()}),B.on(R.qY.SEAT_NON_BID,function(e){var t;e.auctionId===b&&(t=e.seatnonbid,re=re.concat(t))}),{addBidReceived:function addBidReceived(e){te.add(e)},addBidRejected:function addBidRejected(e){X=X.concat(e)},addNoBid:function addNoBid(e){ne=ne.concat(e)},callBids:function callBids(){Y=
j,C=Date.now();var e=g.measureTime("requestBids.makeRequests",function(){return O.Ay.makeBidRequests(p,C,b,y,h,l,g)});(0,i.fH)("Bids Requested for Auction with id: ".concat(b),e),g.checkpoint("callBids"),e.length<1?((0,i.JE)("No valid bid requests returned for auction"),ce()):L.call({dispatch:le,context:this},e)},addWinningBid:function addWinningBid(e){ie=ie.concat(e),O.Ay.callBidWonBidder(e.adapterCode||e.bidder,e,t),e.deferBilling||O.Ay.triggerBilling(e)},setBidTargeting:function setBidTargeting(e){O.Ay.callSetTargetingBidder(e.adapterCode||
e.bidder,e)},getWinningBids:function getWinningBids(){return ie},getAuctionStart:function getAuctionStart(){return C},getAuctionEnd:function getAuctionEnd(){return U},getTimeout:function getTimeout(){return y},getAuctionId:function getAuctionId(){return b},getAuctionStatus:function getAuctionStatus(){return Y},getAdUnits:function getAdUnits(){return p},getAdUnitCodes:function getAdUnitCodes(){return m},getBidRequests:function getBidRequests(){return ee},getBidsReceived:function getBidsReceived(){return te.toArray()},
getNoBids:function getNoBids(){return ne},getNonBids:function getNonBids(){return re},getFPD:function getFPD(){return l},getMetrics:function getMetrics(){return g},end:A.promise,requestsDone:w.promise,getProperties:se}}var F=(0,w.u2)((0,w.A_)("async",function(e,t,n){!function(e){var t=a.$W.getConfig("maxBid");return!t||!e.cpm||t>=Number(e.cpm)}(t)?n(R.Tf.PRICE_TOO_HIGH):this.dispatch.call(null,e,t)},"addBidResponse")),H=(0,w.A_)("sync",function(e){return e},"responsesReady"),L=(0,w.A_)("sync",function(e){this.dispatch.call(this.context,
e)},"addBidderRequests"),z=(0,w.A_)("async",function(e,t){t&&t()},"bidsBackCallback");function J(e,t){!function(e){var t;var n=!0===C.u.get(e.bidderCode,"allowZeroCpmBids")?e.cpm>=0:e.cpm>0;e.bidderCode&&(n||e.dealId)&&(t=function(e,t){var _ref18=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref18$index=_ref18.index,n=_ref18$index===void 0?ae.index:_ref18$index;if(!t)return{};var i=n.getBidRequest(t);var r={};var s=ie(t.mediaType,e);re(r,s,t,i),e&&C.u.getOwn(e,R.iD.ADSERVER_TARGETING)&&
(re(r,C.u.ownSettingsFor(e),t,i),t.sendStandardTargeting=C.u.get(e,"sendStandardTargeting"));t["native"]&&(r=Object.assign({},r,(0,o.Zj)(t)));return r}(e.bidderCode,e));e.adserverTargeting=Object.assign(e.adserverTargeting||{},t)}(t),(0,k.BO)(t.metrics).timeSince("addBidResponse","addBidResponse.total"),e.addBidReceived(t),B.Ic(R.qY.BID_RESPONSE,t)}var Q=(0,w.A_)("async",function(e,t,n,i){v(e,t,n)},"callPrebidCache");function K(e,t,n){if(e&&n){if(e===I.G_){var _t$I$G_$context,_t$I$G_;var _e14=(_t$I$G_$context=
t===null||t===void 0||(_t$I$G_=t[I.G_])===null||_t$I$G_===void 0?void 0:_t$I$G_.context)!==null&&_t$I$G_$context!==void 0?_t$I$G_$context:"instream";if(n["".concat(I.G_,"-").concat(_e14)])return n["".concat(I.G_,"-").concat(_e14)]}return n[e]}}var Y=function Y(e){return function(t){var n=e||function(e){var _ref19=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref19$index=_ref19.index,t=_ref19$index===void 0?ae.index:_ref19$index;var n=K(e.mediaType,t.getMediaTypes(e),a.$W.getConfig("mediaTypePriceGranularity"));
return"string"==typeof e.mediaType&&n?"string"==typeof n?n:"custom":a.$W.getConfig("priceGranularity")}(t);return n===R.UE.AUTO?t.pbAg:n===R.UE.DENSE?t.pbDg:n===R.UE.LOW?t.pbLg:n===R.UE.MEDIUM?t.pbMg:n===R.UE.HIGH?t.pbHg:n===R.UE.CUSTOM?t.pbCg:void 0}},X=function X(){return function(e){return e.creativeId?e.creativeId:""}},Z=function Z(){return function(e){return e.meta&&e.meta.advertiserDomains&&e.meta.advertiserDomains.length>0?[e.meta.advertiserDomains].flat()[0]:""}},ee=function ee(){return function(e){var _e$meta,
_e$meta2;return e.meta&&(e.meta.networkId||e.meta.networkName)?(e===null||e===void 0||(_e$meta=e.meta)===null||_e$meta===void 0?void 0:_e$meta.networkName)||(e===null||e===void 0||(_e$meta2=e.meta)===null||_e$meta2===void 0?void 0:_e$meta2.networkId):""}},te=function te(){return function(e){var _e$meta3;var t=e===null||e===void 0||(_e$meta3=e.meta)===null||_e$meta3===void 0?void 0:_e$meta3.primaryCatId;return Array.isArray(t)?t[0]||"":t||""}};function ne(e,t){return{key:e,val:"function"==typeof t?
function(e,n){return t(e,n)}:function(e){return e[t]}}}function ie(e,t){var n=Object.assign({},C.u.settingsFor(null));if(n[R.iD.ADSERVER_TARGETING]||(n[R.iD.ADSERVER_TARGETING]=[ne(R.xS.BIDDER,"bidderCode"),ne(R.xS.AD_ID,"adId"),ne(R.xS.PRICE_BUCKET,Y()),ne(R.xS.SIZE,"size"),ne(R.xS.DEAL,"dealId"),ne(R.xS.SOURCE,"source"),ne(R.xS.FORMAT,"mediaType"),ne(R.xS.ADOMAIN,Z()),ne(R.xS.ACAT,te()),ne(R.xS.DSP,ee()),ne(R.xS.CRID,X())]),"video"===e){var _e15=n[R.iD.ADSERVER_TARGETING].slice();if(n[R.iD.ADSERVER_TARGETING]=
_e15,[R.xS.UUID,R.xS.CACHE_ID].forEach(function(t){void 0===_e15.find(function(e){return e.key===t})&&_e15.push(ne(t,"videoCacheKey"))}),a.$W.getConfig("cache.url")&&(!t||!1!==C.u.get(t,"sendStandardTargeting"))){var _t8=(0,i.Dl)(a.$W.getConfig("cache.url"));void 0===_e15.find(function(e){return e.key===R.xS.CACHE_HOST})&&_e15.push(ne(R.xS.CACHE_HOST,function(e){var _e$adserverTargeting;return(e===null||e===void 0||(_e$adserverTargeting=e.adserverTargeting)===null||_e$adserverTargeting===void 0?void 0:
_e$adserverTargeting[R.xS.CACHE_HOST])||_t8.hostname}))}}return n}function re(e,t,n,r){var o=t[R.iD.ADSERVER_TARGETING];return n.size=n.getSize(),(o||[]).forEach(function(o){var s=o.key,a=o.val;if(e[s]&&(0,i.JE)("The key: "+s+" is being overwritten"),(0,i.fp)(a))try{a=a(n,r)}catch(e){(0,i.vV)("bidmanager","ERROR",e)}(void 0===t.suppressEmptyKeys||!0!==t.suppressEmptyKeys)&&s!==R.xS.DEAL&&s!==R.xS.ACAT&&s!==R.xS.DSP&&s!==R.xS.CRID||!(0,i.xQ)(a)&&null!=a?e[s]=a:(0,i.fH)("suppressing empty key '"+s+
"' from adserver targeting")}),e}function oe(e,t){return e[t.adUnitCode]||(e[t.adUnitCode]={bids:[]}),e[t.adUnitCode].bids.push(t),e}function se(e){Object.assign(this,{getAuction:function getAuction(t){var n=t.auctionId;if(null!=n)return e().find(function(e){return e.getAuctionId()===n})},getAdUnit:function getAdUnit(t){var n=t.adUnitId;if(null!=n)return e().flatMap(function(e){return e.getAdUnits()}).find(function(e){return e.adUnitId===n})},getMediaTypes:function getMediaTypes(e){var t=e.adUnitId,
n=e.requestId;if(null!=n){var _e16=this.getBidRequest({requestId:n});if(null!=_e16&&(null==t||_e16.adUnitId===t))return _e16.mediaTypes}else if(null!=t){var _e17=this.getAdUnit({adUnitId:t});if(null!=_e17)return _e17.mediaTypes}},getBidderRequest:function getBidderRequest(t){var n=t.requestId,i=t.bidderRequestId;if(null!=n||null!=i){var _t9=e().flatMap(function(e){return e.getBidRequests()});return null!=i&&(_t9=_t9.filter(function(e){return e.bidderRequestId===i})),null==n?_t9[0]:_t9.find(function(e){return e.bids&&
null!=e.bids.find(function(e){return e.bidId===n})})}},getBidRequest:function getBidRequest(t){var n=t.requestId;if(null!=n)return e().flatMap(function(e){return e.getBidRequests()}).flatMap(function(e){return e.bids}).find(function(e){return e&&e.bidId===n})},getOrtb2:function getOrtb2(e){var _this$getBidderReques,_this$getAuction;return((_this$getBidderReques=this.getBidderRequest(e))===null||_this$getBidderReques===void 0?void 0:_this$getBidderReques.ortb2)||((_this$getAuction=this.getAuction(e))===
null||_this$getAuction===void 0||(_this$getAuction=_this$getAuction.getFPD())===null||_this$getAuction===void 0||(_this$getAuction=_this$getAuction.global)===null||_this$getAuction===void 0?void 0:_this$getAuction.ortb2)}})}var ae=function(){var e=(0,D.H)({startTime:function startTime(e){return e.end.then(function(){return e.getAuctionEnd()})},ttl:function ttl(e){return null==(0,_.S9)()?null:e.end.then(function(){return 1E3*Math.max.apply(Math,[(0,_.S9)()].concat(_toConsumableArray(e.getBidsReceived().map(function(e){return e.ttl}))))})}});
(0,_.lc)(function(){return e.refresh()});var t={onExpiry:e.onExpiry};function n(t){var _iterator3=_createForOfIteratorHelper(e),_step3;try{for(_iterator3.s();!(_step3=_iterator3.n()).done;){var _n8=_step3.value;if(_n8.getAuctionId()===t)return _n8}}catch(err){_iterator3.e(err)}finally{_iterator3.f()}}function r(){return e.toArray().flatMap(function(e){return e.getBidsReceived()})}return t.addWinningBid=function(e){var t=(0,k.BO)(e.metrics);t.checkpoint("bidWon"),t.timeBetween("auctionEnd","bidWon",
"adserver.pending"),t.timeBetween("requestBids","bidWon","adserver.e2e");var r=n(e.auctionId);r?r.addWinningBid(e):(0,i.JE)("Auction not found when adding winning bid")},Object.entries({getAllWinningBids:{name:"getWinningBids"},getBidsRequested:{name:"getBidRequests"},getNoBids:{},getAdUnits:{},getBidsReceived:{pre:function pre(e){return e.getAuctionStatus()===x}},getAdUnitCodes:{post:i.hj}}).forEach(function(n){var _n9=_slicedToArray(n,2),i=_n9[0],_n9$=_n9[1],_n9$$name=_n9$.name,r=_n9$$name===void 0?
i:_n9$$name,o=_n9$.pre,s=_n9$.post;var a=null==o?function(e){return e[r]()}:function(e){return o(e)?e[r]():[]},d=null==s?function(e){return e}:function(e){return e.filter(s)};t[i]=function(){return d(e.toArray().flatMap(a))}}),t.getAllBidsForAdUnitCode=function(e){return r().filter(function(t){return t&&t.adUnitCode===e})},t.createAuction=function(t){var n=G(t);return function(t){e.add(t)}(n),n},t.findBidByAdId=function(e){return r().find(function(t){return t.adId===e})},t.getStandardBidderAdServerTargeting=
function(){return ie()[R.iD.ADSERVER_TARGETING]},t.setStatusForBids=function(e,i){var r=t.findBidByAdId(e);if(r&&(r.status=i),r&&i===R.tl.BID_TARGETING_SET){var _e18=n(r.auctionId);_e18&&_e18.setBidTargeting(r)}},t.getLastAuctionId=function(){var t=e.toArray();return t.length&&t[t.length-1].getAuctionId()},t.clearAllAuctions=function(){e.clear()},t.index=new se(function(){return e.toArray()}),t}()},7863:function _(e,t,n){n.d(t,{S9:function S9(){return u},cT:function cT(){return c},lc:function lc(){return l}});
var i=n(3272),r=n(1069);var o="minBidCacheTTL";var s=1,a=null;var d=[];function c(e){return e.ttl-(e.hasOwnProperty("ttlBuffer")?e.ttlBuffer:s)}function u(){return a}function l(e){d.push(e)}i.$W.getConfig("ttlBuffer",function(e){"number"==typeof e.ttlBuffer?s=e.ttlBuffer:(0,r.vV)("Invalid value for ttlBuffer",e.ttlBuffer)}),i.$W.getConfig(o,function(e){var t=a;a=e===null||e===void 0?void 0:e[o],a="number"==typeof a?a:null,t!==a&&d.forEach(function(e){return e(a)})})},2693:function _(e,t,n){var _Class_brand;
n.d(t,{u:function u(){return a}});var i=n(433),r=n(1069),o=n(7873),s=n(8969);var a=new (_Class_brand=new WeakSet,function(){function _class(_e20,t){_classCallCheck(this,_class);_classPrivateMethodInitSpec(this,_Class_brand);this.getSettings=_e20,this.defaultScope=t}return _createClass(_class,[{key:"get",value:function get(e,t){var n=this.getOwn(e,t);return void 0===n&&(n=this.getOwn(null,t)),n}},{key:"getOwn",value:function getOwn(e,t){return e=_assertClassBrand(_Class_brand,this,_e19).call(this,
e),(0,i.A)(this.getSettings(),"".concat(e,".").concat(t))}},{key:"getScopes",value:function getScopes(){var _this5=this;return Object.keys(this.getSettings()).filter(function(e){return e!==_this5.defaultScope})}},{key:"settingsFor",value:function settingsFor(e){return(0,r.D9)({},this.ownSettingsFor(null),this.ownSettingsFor(e))}},{key:"ownSettingsFor",value:function ownSettingsFor(e){return e=_assertClassBrand(_Class_brand,this,_e19).call(this,e),this.getSettings()[e]||{}}}])}())(function(){return(0,
o.m)().bidderSettings||{}},s.iD.BD_SETTING_STANDARD);function _e19(e){return null==e?this.defaultScope:e}},3597:function _(e,t,n){n.d(t,{O:function O(){return o}});var i=n(1069);function r(e){var _ref20=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref20$src=_ref20.src,t=_ref20$src===void 0?"client":_ref20$src,_ref20$bidder=_ref20.bidder,n=_ref20$bidder===void 0?"":_ref20$bidder,r=_ref20.bidId,o=_ref20.transactionId,s=_ref20.adUnitId,a=_ref20.auctionId;var d=t,c=e||0;Object.assign(this,
{bidderCode:n,width:0,height:0,statusMessage:function(){switch(c){case 0:return"Pending";case 1:return"Bid available";case 2:return"Bid returned empty or error response";case 3:return"Bid timed out"}}(),adId:(0,i.s0)(),requestId:r,transactionId:o,adUnitId:s,auctionId:a,mediaType:"banner",source:d}),this.getStatusCode=function(){return c},this.getSize=function(){return this.width+"x"+this.height},this.getIdentifiers=function(){return{src:this.source,bidder:this.bidderCode,bidId:this.requestId,transactionId:this.transactionId,
adUnitId:this.adUnitId,auctionId:this.auctionId}}}function o(e,t){return new r(e,t)}},3272:function _(e,t,n){n.d(t,{$W:function $W(){return p},Ov:function Ov(){return c}});var i=n(6833),r=n(1069),o=n(433),s=n(8969);var a="TRUE"===(0,r.Ez)(s.M).toUpperCase(),d={},c="random",u={};u[c]=!0,u.fixed=!0;var l=c,f={LOW:"low",MEDIUM:"medium",HIGH:"high",AUTO:"auto",DENSE:"dense",CUSTOM:"custom"};function g(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?{priceGranularity:f.MEDIUM,customPriceBucket:{},
mediaTypePriceGranularity:{},bidderSequence:l,auctionOptions:{}}:{};function n(e){return t[e]}function o(n,i){t.hasOwnProperty(n)||Object.defineProperty(e,n,{enumerable:!0}),t[n]=i}var s={publisherDomain:{set:function set(e){null!=e&&(0,r.JE)("publisherDomain is deprecated and has no effect since v7 - use pageUrl instead"),o("publisherDomain",e)}},priceGranularity:{set:function set(e){d(e)&&("string"==typeof e?o("priceGranularity",a(e)?e:f.MEDIUM):(0,r.Qd)(e)&&(o("customPriceBucket",e),o("priceGranularity",
f.CUSTOM),(0,r.OG)("Using custom price granularity")))}},customPriceBucket:{},mediaTypePriceGranularity:{set:function set(e){null!=e&&o("mediaTypePriceGranularity",Object.keys(e).reduce(function(t,i){return d(e[i])?"string"==typeof e?t[i]=a(e[i])?e[i]:n("priceGranularity"):(0,r.Qd)(e)&&(t[i]=e[i],(0,r.OG)("Using custom price granularity for ".concat(i))):(0,r.JE)("Invalid price granularity for media type: ".concat(i)),t},{}))}},bidderSequence:{set:function set(e){u[e]?o("bidderSequence",e):(0,r.JE)("Invalid order: ".concat(e,
". Bidder Sequence was not set."))}},auctionOptions:{set:function set(e){(function(e){if(!(0,r.Qd)(e))return(0,r.JE)("Auction Options must be an object"),!1;for(var _i5=0,_Object$keys=Object.keys(e);_i5<_Object$keys.length;_i5++){var _t0=_Object$keys[_i5];if("secondaryBidders"!==_t0&&"suppressStaleRender"!==_t0&&"suppressExpiredRender"!==_t0)return(0,r.JE)("Auction Options given an incorrect param: ".concat(_t0)),!1;if("secondaryBidders"===_t0){if(!(0,r.cy)(e[_t0]))return(0,r.JE)("Auction Options ".concat(_t0,
" must be of type Array")),!1;if(!e[_t0].every(r.O8))return(0,r.JE)("Auction Options ".concat(_t0," must be only string")),!1}else if(("suppressStaleRender"===_t0||"suppressExpiredRender"===_t0)&&!(0,r.Lm)(e[_t0]))return(0,r.JE)("Auction Options ".concat(_t0," must be of type boolean")),!1}return!0})(e)&&o("auctionOptions",e)}}};return Object.defineProperties(e,Object.fromEntries(Object.entries(s).map(function(e){var _e21=_slicedToArray(e,2),i=_e21[0],r=_e21[1];return[i,Object.assign({get:n.bind(null,
i),set:o.bind(null,i),enumerable:t.hasOwnProperty(i),configurable:!t.hasOwnProperty(i)},r)]}))),e;function a(e){return Object.keys(f).find(function(t){return e===f[t]})}function d(e){if(!e)return(0,r.vV)("Prebid Error: no value passed to `setPriceGranularity()`"),!1;if("string"==typeof e)a(e)||(0,r.JE)("Prebid Warning: setPriceGranularity was called with invalid setting, using `medium` as default.");else if((0,r.Qd)(e)&&!(0,i.q)(e))return(0,r.vV)("Invalid custom price value passed to `setPriceGranularity()`"),
!1;return!0}}var p=function(){var e,t,n,i=[],s=null;function c(){e={};var i=g({debug:a,bidderTimeout:3E3,enableSendAllBids:true,useBidCache:false,deviceAccess:true,disableAjaxTimeout:false,maxNestedIframes:10,maxBid:5E3,userSync:{topics:d}});t&&y(Object.keys(t).reduce(function(e,n){return t[n]!==i[n]&&(e[n]=i[n]||{}),e},{})),t=i,n={}}function u(){if(s&&n&&(0,r.Qd)(n[s])){var _e22=n[s],_i6=new Set([].concat(_toConsumableArray(Object.keys(t)),_toConsumableArray(Object.keys(_e22)))),_o3={};var _iterator4=
_createForOfIteratorHelper(_i6),_step4;try{for(_iterator4.s();!(_step4=_iterator4.n()).done;){var _n0=_step4.value;var _i7=t[_n0],_s6=_e22[_n0];_o3[_n0]=void 0===_s6?_i7:void 0===_i7?_s6:(0,r.Qd)(_s6)?(0,r.D9)({},_i7,_s6):_s6}}catch(err){_iterator4.e(err)}finally{_iterator4.f()}return _o3}return _objectSpread({},t)}var _map=[u,function(){var e=u();return Object.defineProperty(e,"ortb2",{get:function get(){throw new Error("invalid access to 'orbt2' config - use request parameters instead");}}),e}].map(function(e){return function(){if(arguments.length<=
1&&"function"!=typeof(arguments.length<=0?void 0:arguments[0])){var _t1=arguments.length<=0?void 0:arguments[0];return _t1?(0,o.A)(e(),_t1):u()}return b.apply(void 0,arguments)}}),_map2=_slicedToArray(_map,2),l=_map2[0],f=_map2[1],_map3=[f,l].map(function(e){return function(){var t=e.apply(void 0,arguments);return t&&"object"==_typeof(t)&&(t=(0,r.Go)(t)),t}}),_map4=_slicedToArray(_map3,2),p=_map4[0],h=_map4[1];function m(n){if(!(0,r.Qd)(n))return void(0,r.vV)("setConfig options must be an object");
var i=Object.keys(n),o={};i.forEach(function(i){var s=n[i];(0,r.Qd)(e[i])&&(0,r.Qd)(s)&&(s=Object.assign({},e[i],s));try{o[i]=t[i]=s}catch(e){(0,r.JE)("Cannot set config for property ".concat(i," : "),e)}}),y(o)}function b(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=t;if("string"!=typeof e&&(o=e,e="*",n=t||{}),"function"!=typeof o)return void(0,r.vV)("listener must be a function");var s={topic:e,callback:o};return i.push(s),n.init&&o("*"===e?f():_defineProperty({},e,f(e))),
function(){i.splice(i.indexOf(s),1)}}function y(e){var t=Object.keys(e);i.filter(function(e){return t.includes(e.topic)}).forEach(function(t){t.callback(_defineProperty({},t.topic,e[t.topic]))}),i.filter(function(e){return"*"===e.topic}).forEach(function(t){return t.callback(e)})}function v(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{!function(e){if(!(0,r.Qd)(e))throw"setBidderConfig bidder options must be an object";if(!Array.isArray(e.bidders)||!e.bidders.length)throw"setBidderConfig bidder options must contain a bidders list with at least 1 bidder";
if(!(0,r.Qd)(e.config))throw"setBidderConfig bidder options must contain a config object";}(e),e.bidders.forEach(function(i){n[i]||(n[i]=g({},!1)),Object.keys(e.config).forEach(function(o){var s=e.config[o];var a=n[i][o];if((0,r.Qd)(s)&&(null==a||(0,r.Qd)(a))){var _e23=t?r.D9:Object.assign;n[i][o]=_e23({},a||{},s)}else n[i][o]=s})})}catch(e){(0,r.vV)(e)}}function E(e,t){s=e;try{return t()}finally{A()}}function A(){s=null}return c(),{getCurrentBidder:function getCurrentBidder(){return s},resetBidder:A,
getConfig:f,getAnyConfig:l,readConfig:p,readAnyConfig:h,setConfig:m,mergeConfig:function mergeConfig(e){if(!(0,r.Qd)(e))return void(0,r.vV)("mergeConfig input must be an object");var t=(0,r.D9)(u(),e);return m(_objectSpread({},t)),t},setDefaults:function setDefaults(n){(0,r.Qd)(e)?(Object.assign(e,n),Object.assign(t,n)):(0,r.vV)("defaults must be an object")},resetConfig:c,runWithBidder:E,callbackWithBidder:function callbackWithBidder(e){return function(t){return function(){if("function"==typeof t){for(var n=
arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return E(e,t.bind.apply(t,[this].concat(i)))}(0,r.JE)("config.callbackWithBidder callback is not a function")}}},setBidderConfig:v,getBidderConfig:function getBidderConfig(){return n},mergeBidderConfig:function mergeBidderConfig(e){return v(e,!0)}}}()},6916:function _(e,t,n){n.d(t,{B1:function B1(){return s},SL:function SL(){return p},ad:function ad(){return u},mW:function mW(){return d},o2:function o2(){return f},t6:function t6(){return c}});
var i=n(1069),r=n(5555),o=n(3272);var s=Object.freeze({});var _t10=new WeakMap;var _n1=new WeakMap;var _i8=new WeakMap;var _r8=new WeakMap;var _o4=new WeakMap;var _s7=new WeakMap;var _a_brand=new WeakSet;var a=function(){function a(){_classCallCheck(this,a);_classPrivateMethodInitSpec(this,_a_brand);_classPrivateFieldInitSpec(this,_t10,void 0);_classPrivateFieldInitSpec(this,_n1,void 0);_classPrivateFieldInitSpec(this,_i8,void 0);_classPrivateFieldInitSpec(this,_r8,void 0);_classPrivateFieldInitSpec(this,
_o4,!0);_classPrivateFieldInitSpec(this,_s7,void 0);_defineProperty(this,"generatedTime",void 0);_defineProperty(this,"hashFields",void 0);this.reset()}return _createClass(a,[{key:"reset",value:function reset(){_classPrivateFieldSet(_i8,this,(0,r.v6)()),_classPrivateFieldSet(_t10,this,!1),_classPrivateFieldSet(_n1,this,null),_classPrivateFieldSet(_r8,this,!1),this.generatedTime=null}},{key:"enable",value:function enable(){_classPrivateFieldSet(_t10,this,!0)}},{key:"enabled",get:function get(){return _classPrivateFieldGet(_t10,
this)}},{key:"ready",get:function get(){return _classPrivateFieldGet(_r8,this)}},{key:"promise",get:function get(){return _classPrivateFieldGet(_r8,this)?r.U9.resolve(_classPrivateFieldGet(_n1,this)):(_classPrivateFieldGet(_t10,this)||_assertClassBrand(_a_brand,this,_a4).call(this,null),_classPrivateFieldGet(_i8,this).promise)}},{key:"setConsentData",value:function setConsentData(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(0,i.vE)();this.generatedTime=t,_classPrivateFieldSet(_o4,
this,!0),_assertClassBrand(_a_brand,this,_a4).call(this,e)}},{key:"getConsentData",value:function getConsentData(){return _classPrivateFieldGet(_n1,this)}},{key:"hash",get:function get(){var _this6=this;return _classPrivateFieldGet(_o4,this)&&(_classPrivateFieldSet(_s7,this,(0,i.PB)(JSON.stringify(_classPrivateFieldGet(_n1,this)&&this.hashFields?this.hashFields.map(function(e){return _classPrivateFieldGet(_n1,_this6)[e]}):_classPrivateFieldGet(_n1,this)))),_classPrivateFieldSet(_o4,this,!1)),_classPrivateFieldGet(_s7,
this)}}])}();function _a4(e){_classPrivateFieldSet(_r8,this,!0),_classPrivateFieldSet(_n1,this,e),_classPrivateFieldGet(_i8,this).resolve(e)}var d=new (function(_a6){function _class2(){var _this7;_classCallCheck(this,_class2);for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];_this7=_callSuper(this,_class2,[].concat(args));_defineProperty(_this7,"hashFields",["gdprApplies","consentString"]);return _this7}_inherits(_class2,_a6);return _createClass(_class2,
[{key:"getConsentMeta",value:function getConsentMeta(){var e=this.getConsentData();if(e&&e.vendorData&&this.generatedTime)return{gdprApplies:e.gdprApplies,consentStringSize:(0,i.O8)(e.vendorData.tcString)?e.vendorData.tcString.length:0,generatedAt:this.generatedTime,apiVersion:e.apiVersion}}}])}(a)),c=new (function(_a7){function _class3(){_classCallCheck(this,_class3);return _callSuper(this,_class3,arguments)}_inherits(_class3,_a7);return _createClass(_class3,[{key:"getConsentMeta",value:function getConsentMeta(){if(this.getConsentData()&&
this.generatedTime)return{generatedAt:this.generatedTime}}}])}(a)),u=new (function(_a8){function _class4(){var _this8;_classCallCheck(this,_class4);for(var _len2=arguments.length,args=new Array(_len2),_key2=0;_key2<_len2;_key2++)args[_key2]=arguments[_key2];_this8=_callSuper(this,_class4,[].concat(args));_defineProperty(_this8,"hashFields",["applicableSections","gppString"]);return _this8}_inherits(_class4,_a8);return _createClass(_class4,[{key:"getConsentMeta",value:function getConsentMeta(){if(this.getConsentData()&&
this.generatedTime)return{generatedAt:this.generatedTime}}}])}(a)),l=function(){function e(){return!!o.$W.getConfig("coppa")}return{getCoppa:e,getConsentData:e,getConsentMeta:e,reset:function reset(){},get promise(){return r.U9.resolve(e())},get hash(){return e()?"1":"0"}}}(),f=function(){var e={},t={},n={};return{register:function register(i,r,o){o&&((e[r]=e[r]||{})[i]=o,t.hasOwnProperty(r)?t[r]!==o&&(t[r]=n):t[r]=o)},get:function get(i){var r={modules:e[i]||{}};return t.hasOwnProperty(i)&&t[i]!==
n&&(r.gvlid=t[i]),r}}}(),g={gdpr:d,usp:c,gpp:u,coppa:l};var p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;return e=Object.entries(e),Object.assign({get promise(){return r.U9.all(e.map(function(e){var _e24=_slicedToArray(e,2),t=_e24[0],n=_e24[1];return n.promise.then(function(e){return[t,e]})})).then(function(e){return Object.fromEntries(e)})},get hash(){return(0,i.PB)(e.map(function(e){var _e25=_slicedToArray(e,2),t=_e25[0],n=_e25[1];return n.hash}).join(":"))}},Object.fromEntries(["getConsentData",
"getConsentMeta","reset"].map(function(t){return[t,(n=t,function(){return Object.fromEntries(e.map(function(e){var _e26=_slicedToArray(e,2),t=_e26[0],i=_e26[1];return[t,i[n]()]}))})];var n})))}()},8969:function _(e,t,n){n.d(t,{IY:function IY(){return A},M:function M(){return r},RW:function RW(){return g},Tf:function Tf(){return h},UE:function UE(){return c},XQ:function XQ(){return o},Zh:function Zh(){return l},_B:function _B(){return v},as:function as(){return a},cA:function cA(){return d},h0:function h0(){return m},
iD:function iD(){return i},jO:function jO(){return b},nl:function nl(){return E},oA:function oA(){return y},qY:function qY(){return s},tl:function tl(){return p},x5:function x5(){return f},xS:function xS(){return u}});var i={PL_CODE:"code",PL_SIZE:"sizes",PL_BIDS:"bids",BD_BIDDER:"bidder",BD_ID:"paramsd",BD_PL_ID:"placementId",ADSERVER_TARGETING:"adserverTargeting",BD_SETTING_STANDARD:"standard"},r="pbjs_debug",o={GOOD:1},s={AUCTION_INIT:"auctionInit",AUCTION_TIMEOUT:"auctionTimeout",AUCTION_END:"auctionEnd",
BID_ADJUSTMENT:"bidAdjustment",BID_TIMEOUT:"bidTimeout",BID_REQUESTED:"bidRequested",BID_RESPONSE:"bidResponse",BID_REJECTED:"bidRejected",NO_BID:"noBid",SEAT_NON_BID:"seatNonBid",BID_WON:"bidWon",BIDDER_DONE:"bidderDone",BIDDER_ERROR:"bidderError",SET_TARGETING:"setTargeting",BEFORE_REQUEST_BIDS:"beforeRequestBids",BEFORE_BIDDER_HTTP:"beforeBidderHttp",REQUEST_BIDS:"requestBids",ADD_AD_UNITS:"addAdUnits",AD_RENDER_FAILED:"adRenderFailed",AD_RENDER_SUCCEEDED:"adRenderSucceeded",TCF2_ENFORCEMENT:"tcf2Enforcement",
AUCTION_DEBUG:"auctionDebug",BID_VIEWABLE:"bidViewable",STALE_RENDER:"staleRender",EXPIRED_RENDER:"expiredRender",BILLABLE_EVENT:"billableEvent",BID_ACCEPTED:"bidAccepted",RUN_PAAPI_AUCTION:"paapiRunAuction",PBS_ANALYTICS:"pbsAnalytics",PAAPI_BID:"paapiBid",PAAPI_NO_BID:"paapiNoBid",PAAPI_ERROR:"paapiError",BEFORE_PBS_HTTP:"beforePBSHttp",BROWSI_INIT:"browsiInit",BROWSI_DATA:"browsiData"},a={PREVENT_WRITING_ON_MAIN_DOCUMENT:"preventWritingOnMainDocument",NO_AD:"noAd",EXCEPTION:"exception",CANNOT_FIND_AD:"cannotFindAd",
MISSING_DOC_OR_ADID:"missingDocOrAdid"},d={bidWon:"adUnitCode"},c={LOW:"low",MEDIUM:"medium",HIGH:"high",AUTO:"auto",DENSE:"dense",CUSTOM:"custom"},u={BIDDER:"hb_bidder",AD_ID:"hb_adid",PRICE_BUCKET:"hb_pb",SIZE:"hb_size",DEAL:"hb_deal",SOURCE:"hb_source",FORMAT:"hb_format",UUID:"hb_uuid",CACHE_ID:"hb_cache_id",CACHE_HOST:"hb_cache_host",ADOMAIN:"hb_adomain",ACAT:"hb_acat",CRID:"hb_crid",DSP:"hb_dsp"},l={BIDDER:"hb_bidder",AD_ID:"hb_adid",PRICE_BUCKET:"hb_pb",SIZE:"hb_size",DEAL:"hb_deal",FORMAT:"hb_format",
UUID:"hb_uuid",CACHE_HOST:"hb_cache_host"},f={title:"hb_native_title",body:"hb_native_body",body2:"hb_native_body2",privacyLink:"hb_native_privacy",privacyIcon:"hb_native_privicon",sponsoredBy:"hb_native_brand",image:"hb_native_image",icon:"hb_native_icon",clickUrl:"hb_native_linkurl",displayUrl:"hb_native_displayurl",cta:"hb_native_cta",rating:"hb_native_rating",address:"hb_native_address",downloads:"hb_native_downloads",likes:"hb_native_likes",phone:"hb_native_phone",price:"hb_native_price",salePrice:"hb_native_saleprice",
rendererUrl:"hb_renderer_url",adTemplate:"hb_adTemplate"},g={SRC:"s2s",DEFAULT_ENDPOINT:"https://prebid.adnxs.com/pbs/v1/openrtb2/auction",SYNCED_BIDDERS_KEY:"pbjsSyncs"},p={BID_TARGETING_SET:"targetingSet",RENDERED:"rendered",BID_REJECTED:"bidRejected"},h={INVALID:"Bid has missing or invalid properties",INVALID_REQUEST_ID:"Invalid request ID",BIDDER_DISALLOWED:"Bidder code is not allowed by allowedAlternateBidderCodes / allowUnknownBidderCodes",FLOOR_NOT_MET:"Bid does not meet price floor",CANNOT_CONVERT_CURRENCY:"Unable to convert currency",
DSA_REQUIRED:"Bid does not provide required DSA transparency info",DSA_MISMATCH:"Bid indicates inappropriate DSA rendering method",PRICE_TOO_HIGH:"Bid price exceeds maximum value"},m={body:"desc",body2:"desc2",sponsoredBy:"sponsored",cta:"ctatext",rating:"rating",address:"address",downloads:"downloads",likes:"likes",phone:"phone",price:"price",salePrice:"saleprice",displayUrl:"displayurl"},b={sponsored:1,desc:2,rating:3,likes:4,downloads:5,price:6,saleprice:7,phone:8,address:9,desc2:10,displayurl:11,
ctatext:12},y={ICON:1,MAIN:3},v=["privacyIcon","clickUrl","sendTargetingKeys","adTemplate","rendererUrl","type"],E={REQUEST:"Prebid Request",RESPONSE:"Prebid Response",NATIVE:"Prebid Native",EVENT:"Prebid Event"},A="__pb_locator__"},6833:function _(e,t,n){n.d(t,{j:function j(){return l},q:function q(){return g}});var i=n(1069),r=n(3272);var o=2,s={buckets:[{max:5,increment:.5}]},a={buckets:[{max:20,increment:.1}]},d={buckets:[{max:20,increment:.01}]},c={buckets:[{max:3,increment:.01},{max:8,increment:.05},
{max:20,increment:.5}]},u={buckets:[{max:5,increment:.05},{max:10,increment:.1},{max:20,increment:.5}]};function l(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=parseFloat(e);return isNaN(i)&&(i=""),{low:""===i?"":f(e,s,n),med:""===i?"":f(e,a,n),high:""===i?"":f(e,d,n),auto:""===i?"":f(e,u,n),dense:""===i?"":f(e,c,n),custom:""===i?"":f(e,t,n)}}function f(e,t,n){var s="";if(!g(t))return s;var a=t.buckets.reduce(function(e,t){return e.max>t.max?e:t},{max:0});var d=0,c=t.buckets.find(function(t){if(e>
a.max*n){var _e27=t.precision;void 0===_e27&&(_e27=o),s=(t.max*n).toFixed(_e27)}else{if(e<=t.max*n&&e>=d*n)return t.min=d,t;d=t.max}});return c&&(s=function(e,t,n){var s=void 0!==t.precision?t.precision:o,a=t.increment*n,d=t.min*n;var c=Math.floor,u=r.$W.getConfig("cpmRoundingFunction");"function"==typeof u&&(c=u);var l,f,g=Math.pow(10,s+2),p=(e*g-d*g)/(a*g);try{l=c(p)*a+d}catch(e){f=!0}(f||"number"!=typeof l)&&((0,i.JE)("Invalid rounding function passed in config"),l=Math.floor(p)*a+d);return l=
Number(l.toFixed(10)),l.toFixed(s)}(e,c,n)),s}function g(e){if((0,i.Im)(e)||!e.buckets||!Array.isArray(e.buckets))return!1;var t=!0;return e.buckets.forEach(function(e){e.max&&e.increment||(t=!1)}),t}},6031:function _(e,t,n){n.d(t,{HH:function HH(){return c},kj:function kj(){return d},xh:function xh(){return a}});var i=n(5555),r=n(1069),o=n(4595),s=n(9214);var a=3,d=(0,s.A_)("sync",function(e){return o.G}),c=function(){var e={};return function(t){var n=d(t);return e.hasOwnProperty(n)||(e[n]=new i.U9(function(e){var t=
(0,r.CA)();t.srcdoc="\x3cscript\x3e".concat(n,"\x3c/script\x3e"),t.onload=function(){return e(t.contentWindow.render)},document.body.appendChild(t)})),e[n]}}()},3005:function _(e,t,n){n.d(t,{$T:function $T(){return a},Ni:function Ni(){return i},OA:function OA(){return o},RO:function RO(){return s},fR:function fR(){return r}});var i=1,r=2,o=1,s=500;function a(e){return(e!==null&&e!==void 0?e:[]).reduce(function(e,t){var _e$n2,_o$i;var n=t.event,i=t.method,r=t.url;var o=e[n]=(_e$n2=e[n])!==null&&_e$n2!==
void 0?_e$n2:{};return(o[i]=(_o$i=o[i])!==null&&_o$i!==void 0?_o$i:[]).push(r),e},{})}},5023:function _(e,t,n){n.d(t,{AU:function AU(){return m},Ic:function Ic(){return v},kQ:function kQ(){return y},on:function on(){return h}});var i=n(1069),r=n(8969),o=n(6853),s=n(3272);var a="eventHistoryTTL";var d=null;var c=(0,o.H)({monotonic:!0,ttl:function ttl(){return d}});s.$W.getConfig(a,function(e){var _e28;var t=d;e=(_e28=e)===null||_e28===void 0?void 0:_e28[a],d="number"==typeof e?1E3*e:null,t!==d&&c.refresh()});
var u=Array.prototype.slice,l=Array.prototype.push,f=Object.values(r.qY);var g=r.cA,p=function(){var e={},t={};function n(e){return f.includes(e)}return t.has=n,t.on=function(t,r,o){if(n(t)){var _n10=e[t]||{que:[]};o?(_n10[o]=_n10[o]||{que:[]},_n10[o].que.push(r)):_n10.que.push(r),e[t]=_n10}else i.vV("Wrong event name : "+t+" Valid event names :"+f)},t.emit=function(t){!function(t,n){i.OG("Emitting event for: "+t);var r=n[0]||{},o=r[g[t]],s=e[t]||{que:[]};var a=Object.keys(s);var d=[];c.add({eventType:t,
args:r,id:o,elapsedTime:i.V()}),o&&a.includes(o)&&l.apply(d,s[o].que),l.apply(d,s.que),(d||[]).forEach(function(e){if(e)try{e.apply(null,n)}catch(e){i.vV("Error executing handler:","events.js",e,t)}})}(t,u.call(arguments,1))},t.off=function(t,n,r){var o=e[t];i.Im(o)||i.Im(o.que)&&i.Im(o[r])||r&&(i.Im(o[r])||i.Im(o[r].que))||(r?(o[r].que||[]).forEach(function(e){var t=o[r].que;e===n&&t.splice(t.indexOf(e),1)}):(o.que||[]).forEach(function(e){var t=o.que;e===n&&t.splice(t.indexOf(e),1)}),e[t]=o)},t.get=
function(){return e},t.addEvents=function(e){f=f.concat(e)},t.getEvents=function(){return c.toArray().map(function(e){return Object.assign({},e)})},t}();i.cD(p.emit.bind(p));var h=p.on,m=p.off,b=p.get,y=p.getEvents,v=p.emit,E=p.addEvents,A=p.has},1970:function _(e,t,n){n.d(t,{w:function w(){return C}});var i=n(9214),r=n(7934),o=n(1069);var s=(0,n(2938).CK)("fpdEnrichment"),a=(0,o.Bj)(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.host;if(!s.cookiesAreEnabled())return e;
var t=e.split(".");if(2===t.length)return e;var n,i,r=-2;var a="_rdc".concat(Date.now()),d="writeable";do{n=t.slice(r).join(".");var _e29=(new Date((0,o.vE)()+1E4)).toUTCString();s.setCookie(a,d,_e29,"Lax",n,void 0);s.getCookie(a,void 0)===d?(i=!1,s.setCookie(a,"","Thu, 01 Jan 1970 00:00:01 GMT",void 0,n,void 0)):(r+=-1,i=Math.abs(r)<=t.length)}while(i);return n});var d=n(3172),c=n(433),u=n(3272),l=n(5555);var f=["architecture","bitness","model","platformVersion","fullVersionList"],g=["brands","mobile",
"platform"],p=function(_window$navigator){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:(_window$navigator=window.navigator)===null||_window$navigator===void 0?void 0:_window$navigator.userAgentData;var t=e&&g.some(function(t){return void 0!==e[t]})?Object.freeze(m(1,e)):null;return function(){return t}}(),h=function(_window$navigator2){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:(_window$navigator2=window.navigator)===null||_window$navigator2===void 0?void 0:_window$navigator2.userAgentData;
var t={},n=new WeakMap;return function(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;if(!n.has(i)){var _e30=Array.from(i);_e30.sort(),n.set(i,_e30.join("|"))}var r=n.get(i);if(!t.hasOwnProperty(r))try{t[r]=e.getHighEntropyValues(i).then(function(e){return(0,o.Im)(e)?null:Object.freeze(m(2,e))})["catch"](function(){return null})}catch(e){t[r]=l.U9.resolve(null)}return t[r]}}();function m(e,t){function n(e,t){var n={brand:e};return(0,o.O8)(t)&&!(0,o.xQ)(t)&&(n.version=t.split(".")),
n}var i={source:e};return t.platform&&(i.platform=n(t.platform,t.platformVersion)),(t.fullVersionList||t.brands)&&(i.browsers=(t.fullVersionList||t.brands).map(function(e){var t=e.brand,i=e.version;return n(t,i)})),void 0!==t.mobile&&(i.mobile=t.mobile?1:0),["model","bitness","architecture"].forEach(function(e){var n=t[e];(0,o.O8)(n)&&(i[e]=n)}),i}var b=n(3858),y=n(5139),v=n(3441),E=n(6811),A=n(5569),w=n(8944);var T={getRefererInfo:r.EN,findRootDomain:a,getWindowTop:o.mb,getWindowSelf:o.l4,getHighEntropySUA:h,
getLowEntropySUA:p,getDocument:o.YE},I=(0,b.i8)("FPD"),C=(0,i.A_)("sync",function(e){var t=[e,O()["catch"](function(){return null}),l.U9.resolve("cookieDeprecationLabel"in navigator&&(0,y.io)(E.Ue,(0,v.s)(A.tp,"cdep"))&&navigator.cookieDeprecationLabel.getValue())["catch"](function(){return null})];return l.U9.all(t).then(function(e){var _e31=_slicedToArray(e,3),t=_e31[0],n=_e31[1],i=_e31[2];var r=T.getRefererInfo();if(Object.entries(S).forEach(function(e){var _e32=_slicedToArray(e,2),n=_e32[0],i=
_e32[1];var s=i(t,r);s&&Object.keys(s).length>0&&(t[n]=(0,o.D9)({},s,t[n]))}),n&&(0,d.J)(t,"device.sua",Object.assign({},n,t.device.sua)),i){var _e33={cdep:i};(0,d.J)(t,"device.ext",Object.assign({},_e33,t.device.ext))}var s=T.getDocument().documentElement.lang;if(s&&((0,d.J)(t,"site.ext.data.documentLang",s),!(0,c.A)(t,"site.content.language"))){var _e34=s.split("-")[0];(0,d.J)(t,"site.content.language",_e34)}t=I(t);var _iterator5=_createForOfIteratorHelper(b.Dy),_step5;try{for(_iterator5.s();!(_step5=
_iterator5.n()).done;){var _e35=_step5.value;if((0,b.O$)(t,_e35)){t[_e35]=(0,o.D9)({},k(t,r),t[_e35]);break}}}catch(err){_iterator5.e(err)}finally{_iterator5.f()}return t})});function B(e){try{return e(T.getWindowTop())}catch(t){return e(T.getWindowSelf())}}function O(){var e=u.$W.getConfig("firstPartyData.uaHints");return Array.isArray(e)&&0!==e.length?T.getHighEntropySUA(e):l.U9.resolve(T.getLowEntropySUA())}function R(e){return(0,o.SH)(e,Object.keys(e))}var S={site:function site(e,t){if(!b.Dy.filter(function(e){return"site"!==
e}).some(b.O$.bind(null,e)))return R({page:t.page,ref:t.ref})},device:function device(){return B(function(e){var _e$navigator;var t=(0,o.Ot)().screen.width,n=(0,o.Ot)().screen.height,_ref22=(0,w.M)(),i=_ref22.width,r=_ref22.height,s={w:t,h:n,dnt:(0,o.l9)()?1:0,ua:e.navigator.userAgent,language:e.navigator.language.split("-").shift(),ext:{vpw:i,vph:r}};return(_e$navigator=e.navigator)!==null&&_e$navigator!==void 0&&_e$navigator.webdriver&&(0,d.J)(s,"ext.webdriver",!0),s})},regs:function regs(){var e=
{};B(function(e){return e.navigator.globalPrivacyControl})&&(0,d.J)(e,"ext.gpc","1");var t=u.$W.getConfig("coppa");return"boolean"==typeof t&&(e.coppa=t?1:0),e}};function k(e,t){var _B2,_B2$replace;var n=(0,r.gR)(t.page,{noLeadingWww:!0}),i=(_B2=B(function(e){return e.document.querySelector("meta[name\x3d'keywords']")}))===null||_B2===void 0||(_B2=_B2.content)===null||_B2===void 0||(_B2$replace=_B2.replace)===null||_B2$replace===void 0?void 0:_B2$replace.call(_B2,/\s/g,"");return R({domain:n,keywords:i,
publisher:R({domain:T.findRootDomain(n)})})}},3858:function _(e,t,n){n.d(t,{Dy:function Dy(){return r},O$:function O$(){return s},i8:function i8(){return o}});var i=n(1069);var r=["dooh","app","site"];function o(e){return function(t){return r.reduce(function(n,r){return s(t,r)&&(null!=n?((0,i.JE)("".concat(e," specifies both '").concat(n,"' and '").concat(r,"'; dropping the latter.")),delete t[r]):n=r),n},null),t}}function s(e,t){return null!=e[t]&&Object.keys(e[t]).length>0}},9214:function _(e,t,
n){n.d(t,{A_:function A_(){return s},Y6:function Y6(){return c},Yn:function Yn(){return d},u2:function u2(){return u}});var i=n(8128),r=n.n(i),o=n(5555);var s=r()({ready:r().SYNC|r().ASYNC|r().QUEUE});var a=(0,o.v6)();s.ready=function(){var e=s.ready;return function(){try{return e.apply(s,arguments)}finally{a.resolve()}}}();a.promise;var d=s.get;function c(e,t){return Object.defineProperties(t,Object.fromEntries(["before","after","getHooks","removeAll"].map(function(t){return[t,{get:function get(){return e[t]}}]}))),
t}function u(e){return c(e,function(){for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return n.push(function(){}),e.apply(this,n)})}},1371:function _(e,t,n){n.d(t,{D4:function D4(){return o},G_:function G_(){return r},LM:function LM(){return s},s6:function s6(){return i}});var i="native",r="video",o="banner",s="adpod"},2449:function _(e,t,n){n.d(t,{Bm:function Bm(){return v},IX:function IX(){return B},Nh:function Nh(){return l},Xj:function Xj(){return U},Zj:function Zj(){return w},
gs:function gs(){return A},l6:function l6(){return h},mT:function mT(){return u},nk:function nk(){return b},vO:function vO(){return E},yl:function yl(){return O}});var i=n(1069),r=n(6881),o=n(8969),s=n(1371),a=n(9075),d=n(6031),c=n(3005);var u=[],l=Object.keys(o.x5).map(function(e){return o.x5[e]}),f={image:{ortb:{ver:"1.2",assets:[{required:1,id:1,img:{type:3,wmin:100,hmin:100}},{required:1,id:2,title:{len:140}},{required:1,id:3,data:{type:1}},{required:0,id:4,data:{type:2}},{required:0,id:5,img:{type:1,
wmin:20,hmin:20}}]},image:{required:!0},title:{required:!0},sponsoredBy:{required:!0},clickUrl:{required:!0},body:{required:!1},icon:{required:!1}}},g=$(o.h0),p=$(o.jO);function h(e){return e["native"]&&"object"==_typeof(e["native"])}function m(e){if(e&&e.type&&function(e){if(!e||!Object.keys(f).includes(e))return(0,i.vV)("".concat(e," nativeParam is not supported")),!1;return!0}(e.type)&&(e=f[e.type]),!e||!e.ortb||y(e.ortb))return e}function b(e){e.forEach(function(e){var _e$mediaTypes;var t=e.nativeParams||
(e===null||e===void 0||(_e$mediaTypes=e.mediaTypes)===null||_e$mediaTypes===void 0?void 0:_e$mediaTypes["native"]);t&&(e.nativeParams=m(t)),e.nativeParams&&(e.nativeOrtbRequest=e.nativeParams.ortb||function(e){if(!e&&!(0,i.Qd)(e))return void(0,i.vV)("Native assets object is empty or not an object: ",e);var t={ver:"1.2",assets:[]};for(var _n11 in e){if(o._B.includes(_n11))continue;if(!o.x5.hasOwnProperty(_n11)){(0,i.vV)("Unrecognized native asset code: ".concat(_n11,". Asset will be ignored."));continue}if("privacyLink"===
_n11){t.privacy=1;continue}var _r9=e[_n11];var _s8=0;_r9.required&&(0,i.Lm)(_r9.required)&&(_s8=Number(_r9.required));var _a9={id:t.assets.length,required:_s8};if(_n11 in o.h0)_a9.data={type:o.jO[o.h0[_n11]]},_r9.len&&(_a9.data.len=_r9.len);else if("icon"===_n11||"image"===_n11){if(_a9.img={type:"icon"===_n11?o.oA.ICON:o.oA.MAIN},_r9.aspect_ratios)if((0,i.cy)(_r9.aspect_ratios))if(_r9.aspect_ratios.length){var _r9$aspect_ratios$=_r9.aspect_ratios[0],_e36=_r9$aspect_ratios$.min_width,_t11=_r9$aspect_ratios$.min_height;
(0,i.Fq)(_e36)&&(0,i.Fq)(_t11)?(_a9.img.wmin=_e36,_a9.img.hmin=_t11):(0,i.vV)("image.aspect_ratios min_width or min_height are invalid: ",_e36,_t11);var _n12=_r9.aspect_ratios.filter(function(e){return e.ratio_width&&e.ratio_height}).map(function(e){return"".concat(e.ratio_width,":").concat(e.ratio_height)});_n12.length>0&&(_a9.img.ext={aspectratios:_n12})}else(0,i.vV)("image.aspect_ratios was passed, but it's empty:",_r9.aspect_ratios);else(0,i.vV)("image.aspect_ratios was passed, but it's not a an array:",
_r9.aspect_ratios);_r9.sizes&&(2===_r9.sizes.length&&(0,i.Fq)(_r9.sizes[0])&&(0,i.Fq)(_r9.sizes[1])?(_a9.img.w=_r9.sizes[0],_a9.img.h=_r9.sizes[1],delete _a9.img.hmin,delete _a9.img.wmin):(0,i.vV)("image.sizes was passed, but its value is not an array of integers:",_r9.sizes))}else"title"===_n11?_a9.title={len:_r9.len||140}:"ext"===_n11&&(_a9.ext=_r9,delete _a9.required);t.assets.push(_a9)}return t}(e.nativeParams))})}function y(e){var t=e.assets;if(!Array.isArray(t)||0===t.length)return(0,i.vV)("assets in mediaTypes.native.ortb is not an array, or it's empty. Assets: ",
t),!1;var n=t.map(function(e){return e.id});return t.length!==(new Set(n)).size||n.some(function(e){return e!==parseInt(e,10)})?((0,i.vV)("each asset object must have 'id' property, it must be unique and it must be an integer"),!1):e.hasOwnProperty("eventtrackers")&&!Array.isArray(e.eventtrackers)?((0,i.vV)("ortb.eventtrackers is not an array. Eventtrackers: ",e.eventtrackers),!1):t.every(function(e){return function(e){if(!(0,i.Qd)(e))return(0,i.vV)("asset must be an object. Provided asset: ",e),
!1;if(e.img){if(!(0,i.Et)(e.img.w)&&!(0,i.Et)(e.img.wmin))return(0,i.vV)("for img asset there must be 'w' or 'wmin' property"),!1;if(!(0,i.Et)(e.img.h)&&!(0,i.Et)(e.img.hmin))return(0,i.vV)("for img asset there must be 'h' or 'hmin' property"),!1}else if(e.title){if(!(0,i.Et)(e.title.len))return(0,i.vV)("for title asset there must be 'len' property defined"),!1}else if(e.data){if(!(0,i.Et)(e.data.type))return(0,i.vV)("for data asset 'type' property must be a number"),!1}else if(e.video&&!(Array.isArray(e.video.mimes)&&
Array.isArray(e.video.protocols)&&(0,i.Et)(e.video.minduration)&&(0,i.Et)(e.video.maxduration)))return(0,i.vV)("video asset is not properly configured"),!1;return!0}(e)})}function v(e){var _e$native;var _ref23=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref23$index=_ref23.index,t=_ref23$index===void 0?r.n.index:_ref23$index;var n=t.getAdUnit(e);if(!n)return!1;var o=n.nativeOrtbRequest;return function(e,t,_e$link){if(!(e!==null&&e!==void 0&&(_e$link=e.link)!==null&&_e$link!==void 0&&
_e$link.url))return(0,i.vV)("native response doesn't have 'link' property. Ortb response: ",e),!1;var n=t.assets.filter(function(e){return 1===e.required}).map(function(e){return e.id}),r=e.assets.map(function(e){return e.id});var o=n.every(function(e){return r.includes(e)});o||(0,i.vV)("didn't receive a bid with all required assets. Required ids: ".concat(n,", but received ids in response: ").concat(r));return o}(((_e$native=e["native"])===null||_e$native===void 0?void 0:_e$native.ortb)||_(e["native"],
o),o)}function E(e,t){var n=t["native"].ortb||D(t["native"]);return"click"===e.action?function(e){var _e$link3;var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,_ref24=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},_ref24$fetchURL=_ref24.fetchURL,n=_ref24$fetchURL===void 0?i.z$:_ref24$fetchURL;if(t){var _e$link2;var _i9=(e.assets||[]).filter(function(e){return e.link}).reduce(function(e,t){return e[t.id]=t.link,e},{}),_r0=((_e$link2=e.link)===null||_e$link2===void 0?void 0:
_e$link2.clicktrackers)||[];var _o5=_i9[t],_s9=_r0;_o5&&(_s9=_o5.clicktrackers||[]),_s9.forEach(function(e){return n(e)})}else(((_e$link3=e.link)===null||_e$link3===void 0?void 0:_e$link3.clicktrackers)||[]).forEach(function(e){return n(e)})}(n,e===null||e===void 0?void 0:e.assetId):function(e){var _ref25=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref25$runMarkup=_ref25.runMarkup,t=_ref25$runMarkup===void 0?function(e){return(0,i.ro)(e)}:_ref25$runMarkup,_ref25$fetchURL=_ref25.fetchURL,
n=_ref25$fetchURL===void 0?i.z$:_ref25$fetchURL,_ref26=(0,c.$T)(e.eventtrackers||[])[c.OA]||{},_ref26$c$Ni=_ref26[c.Ni],r=_ref26$c$Ni===void 0?[]:_ref26$c$Ni,_ref26$c$fR=_ref26[c.fR],o=_ref26$c$fR===void 0?[]:_ref26$c$fR;e.imptrackers&&(r=r.concat(e.imptrackers));r.forEach(function(e){return n(e)}),o=o.map(function(e){return'\x3cscript async src\x3d"'.concat(e,'"\x3e\x3c/script\x3e')}),e.jstracker&&(o=o.concat([e.jstracker]));o.length&&t(o.join("\n"))}(n),e.action}function A(e,t){var _e$native2;var n=
t===null||t===void 0?void 0:t.nativeOrtbRequest,i=(_e$native2=e["native"])===null||_e$native2===void 0?void 0:_e$native2.ortb;if(n&&i){var _t12=function(e,t,_e$link4,_n$impressionTrackers){var n={},i=(t===null||t===void 0?void 0:t.assets)||[];n.clickUrl=(_e$link4=e.link)===null||_e$link4===void 0?void 0:_e$link4.url,n.privacyLink=e.privacy;var _iterator6=_createForOfIteratorHelper((e===null||e===void 0?void 0:e.assets)||[]),_step6;try{var _loop=function _loop(){var _e$img,_e$data;var t=_step6.value;
var e=i.find(function(e){return t.id===e.id});t.title?n.title=t.title.text:t.img?n[(e===null||e===void 0||(_e$img=e.img)===null||_e$img===void 0?void 0:_e$img.type)===o.oA.MAIN?"image":"icon"]={url:t.img.url,width:t.img.w,height:t.img.h}:t.data&&(n[g[p[e===null||e===void 0||(_e$data=e.data)===null||_e$data===void 0?void 0:_e$data.type]]]=t.data.value)};for(_iterator6.s();!(_step6=_iterator6.n()).done;)_loop()}catch(err){_iterator6.e(err)}finally{_iterator6.f()}n.impressionTrackers=[];var r=[];e.imptrackers&&
(_n$impressionTrackers=n.impressionTrackers).push.apply(_n$impressionTrackers,_toConsumableArray(e.imptrackers));var _iterator7=_createForOfIteratorHelper((e===null||e===void 0?void 0:e.eventtrackers)||[]),_step7;try{for(_iterator7.s();!(_step7=_iterator7.n()).done;){var _t13=_step7.value;_t13.event===c.OA&&_t13.method===c.Ni&&n.impressionTrackers.push(_t13.url),_t13.event===c.OA&&_t13.method===c.fR&&r.push(_t13.url)}}catch(err){_iterator7.e(err)}finally{_iterator7.f()}r=r.map(function(e){return'\x3cscript async src\x3d"'.concat(e,
'"\x3e\x3c/script\x3e')}),(e===null||e===void 0?void 0:e.jstracker)&&r.push(e.jstracker);r.length&&(n.javascriptTrackers=r.join("\n"));return n}(i,n);Object.assign(e["native"],_t12)}["rendererUrl","adTemplate"].forEach(function(n){var _t$nativeParams;var i=t===null||t===void 0||(_t$nativeParams=t.nativeParams)===null||_t$nativeParams===void 0?void 0:_t$nativeParams[n];i&&(e["native"][n]=R(i))})}function w(e){var _i$nativeParams,_i$nativeParams2;var _ref27=arguments.length>1&&void 0!==arguments[1]?
arguments[1]:{},_ref27$index=_ref27.index,t=_ref27$index===void 0?r.n.index:_ref27$index,n={};var i=t.getAdUnit(e),s=null==(i===null||i===void 0||(_i$nativeParams=i.nativeParams)===null||_i$nativeParams===void 0?void 0:_i$nativeParams.ortb)&&!1!==(i===null||i===void 0||(_i$nativeParams2=i.nativeParams)===null||_i$nativeParams2===void 0?void 0:_i$nativeParams2.sendTargetingKeys),a=function(e,_e$nativeParams){var t={};(e===null||e===void 0||(_e$nativeParams=e.nativeParams)===null||_e$nativeParams===
void 0?void 0:_e$nativeParams.ext)&&Object.keys(e.nativeParams.ext).forEach(function(e){t[e]="hb_native_".concat(e)});return _objectSpread(_objectSpread({},o.x5),t)}(i),d=_objectSpread(_objectSpread({},e["native"]),e["native"].ext);return delete d.ext,Object.keys(d).forEach(function(t){var _e$native3,_i$nativeParams3,_i$nativeParams4,_i$nativeParams5,_i$nativeParams6;var r=a[t];var o=R(e["native"][t])||R(e===null||e===void 0||(_e$native3=e["native"])===null||_e$native3===void 0||(_e$native3=_e$native3.ext)===
null||_e$native3===void 0?void 0:_e$native3[t]);if("adTemplate"===t||!r||!o)return;var d=i===null||i===void 0||(_i$nativeParams3=i.nativeParams)===null||_i$nativeParams3===void 0||(_i$nativeParams3=_i$nativeParams3[t])===null||_i$nativeParams3===void 0?void 0:_i$nativeParams3.sendId;if("boolean"!=typeof d&&(d=i===null||i===void 0||(_i$nativeParams4=i.nativeParams)===null||_i$nativeParams4===void 0||(_i$nativeParams4=_i$nativeParams4.ext)===null||_i$nativeParams4===void 0||(_i$nativeParams4=_i$nativeParams4[t])===
null||_i$nativeParams4===void 0?void 0:_i$nativeParams4.sendId),d)o="".concat(r,":").concat(e.adId);var c=i===null||i===void 0||(_i$nativeParams5=i.nativeParams)===null||_i$nativeParams5===void 0||(_i$nativeParams5=_i$nativeParams5[t])===null||_i$nativeParams5===void 0?void 0:_i$nativeParams5.sendTargetingKeys;"boolean"!=typeof c&&(c=i===null||i===void 0||(_i$nativeParams6=i.nativeParams)===null||_i$nativeParams6===void 0||(_i$nativeParams6=_i$nativeParams6.ext)===null||_i$nativeParams6===void 0||
(_i$nativeParams6=_i$nativeParams6[t])===null||_i$nativeParams6===void 0?void 0:_i$nativeParams6.sendTargetingKeys);("boolean"==typeof c?c:s)&&(n[r]=o)}),n}function T(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=[];return Object.entries(e).filter(function(e){var _e37=_slicedToArray(e,2),i=_e37[0],r=_e37[1];return r&&(!1===n&&"ext"===i||null==t||t.includes(i))}).forEach(function(e){var _e38=_slicedToArray(e,2),r=_e38[0],s=_e38[1];!1===n&&"ext"===r?i.push.apply(i,_toConsumableArray(T(s,
t,!0))):(n||o.x5.hasOwnProperty(r))&&i.push({key:r,value:R(s)})}),i}function I(e,t,n){var _ref28=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},_ref28$index=_ref28.index,s=_ref28$index===void 0?r.n.index:_ref28$index;var c={message:"assetResponse",adId:e.adId};var u=(0,a.vd)(t)["native"];return u?(c["native"]=Object.assign({},u),c.renderer=(0,d.kj)(t),c.rendererVersion=d.xh,null!=n&&(u.assets=u.assets.filter(function(e){var t=e.key;return n.includes(t)}))):u=function(e,t,n,_t$mediaTypes2){var r=
_objectSpread(_objectSpread({},(0,i.SH)(e["native"],["rendererUrl","adTemplate"])),{},{assets:T(e["native"],n),nativeKeys:o.x5});return e["native"].ortb?r.ortb=e["native"].ortb:((_t$mediaTypes2=t.mediaTypes)===null||_t$mediaTypes2===void 0||(_t$mediaTypes2=_t$mediaTypes2["native"])===null||_t$mediaTypes2===void 0?void 0:_t$mediaTypes2.ortb)&&(r.ortb=_(e["native"],t.nativeOrtbRequest)),r}(t,s.getAdUnit(t),n),Object.assign(c,u)}var C=Object.fromEntries(Object.entries(o.x5).map(function(e){var _e39=
_slicedToArray(e,2),t=_e39[0],n=_e39[1];return[n,t]}));function B(e,t){var n=e.assets.map(function(e){return C[e]});return I(e,t,n)}function O(e,t){return I(e,t,null)}function R(e){return(e===null||e===void 0?void 0:e.url)||e}function S(e,t){for(;e&&t&&e!==t;)e>t?e-=t:t-=e;return e||t}function k(e){if(!y(e))return;var t={};var _iterator8=_createForOfIteratorHelper(e.assets),_step8;try{var _loop2=function _loop2(){var n=_step8.value;if(n.title){var _e40={required:!!n.required&&Boolean(n.required),
len:n.title.len};t.title=_e40}else if(n.img){var _e41={required:!!n.required&&Boolean(n.required)};if(n.img.w&&n.img.h)_e41.sizes=[n.img.w,n.img.h];else if(n.img.wmin&&n.img.hmin){var _t14=S(n.img.wmin,n.img.hmin);_e41.aspect_ratios=[{min_width:n.img.wmin,min_height:n.img.hmin,ratio_width:n.img.wmin/_t14,ratio_height:n.img.hmin/_t14}]}n.img.type===o.oA.MAIN?t.image=_e41:t.icon=_e41}else if(n.data){var _e42=Object.keys(o.jO).find(function(e){return o.jO[e]===n.data.type}),_i0=Object.keys(o.h0).find(function(t){return o.h0[t]===
_e42});t[_i0]={required:!!n.required&&Boolean(n.required)},n.data.len&&(t[_i0].len=n.data.len)}e.privacy&&(t.privacyLink={required:!1})};for(_iterator8.s();!(_step8=_iterator8.n()).done;)_loop2()}catch(err){_iterator8.e(err)}finally{_iterator8.f()}return t}function U(e){if(!e||!(0,i.cy)(e))return e;if(!e.some(function(e){var _s$s;return(_s$s=((e===null||e===void 0?void 0:e.mediaTypes)||{})[s.s6])===null||_s$s===void 0?void 0:_s$s.ortb}))return e;var _t15=(0,i.Go)(e);var _iterator9=_createForOfIteratorHelper(_t15),
_step9;try{for(_iterator9.s();!(_step9=_iterator9.n()).done;){var _e43=_step9.value;_e43.mediaTypes&&_e43.mediaTypes[s.s6]&&_e43.mediaTypes[s.s6].ortb&&(_e43.mediaTypes[s.s6]=Object.assign((0,i.Up)(_e43.mediaTypes[s.s6],o._B),k(_e43.mediaTypes[s.s6].ortb)),_e43.nativeParams=m(_e43.mediaTypes[s.s6]))}}catch(err){_iterator9.e(err)}finally{_iterator9.f()}return _t15}function D(e){var t={link:{},eventtrackers:[]};return Object.entries(e).forEach(function(e){var _e44=_slicedToArray(e,2),n=_e44[0],i=_e44[1];
switch(n){case "clickUrl":t.link.url=i;break;case "clickTrackers":t.link.clicktrackers=Array.isArray(i)?i:[i];break;case "impressionTrackers":(Array.isArray(i)?i:[i]).forEach(function(e){t.eventtrackers.push({event:c.OA,method:c.Ni,url:e})});break;case "javascriptTrackers":t.jstracker=Array.isArray(i)?i.join(""):i;break;case "privacyLink":t.privacy=i}}),t}function _(e,t){var n=_objectSpread(_objectSpread({},D(e)),{},{assets:[]});function r(e,r){var o=t.assets.find(e);null!=o&&(o=(0,i.Go)(o),r(o),
n.assets.push(o))}return Object.keys(e).filter(function(t){return!!e[t]}).forEach(function(t){var n=R(e[t]);switch(t){case "title":r(function(e){return null!=e.title},function(e){e.title={text:n}});break;case "image":case "icon":var _e45="image"===t?o.oA.MAIN:o.oA.ICON;r(function(t){return null!=t.img&&t.img.type===_e45},function(e){e.img={url:n}});break;default:t in o.h0&&r(function(e){return null!=e.data&&e.data.type===o.jO[o.h0[t]]},function(e){e.data={value:n}})}}),n}function $(e){var t={};for(var n in e)t[e[n]]=
n;return t}},1E3:function _(e,t,n){n.d(t,{S3:function S3(){return r},pS:function pS(){return l}});var i=["request","imp","bidResponse","response"],r=i[0],o=i[1],s=i[2],a=i[3],d="default",c="pbs",u=new Set(i);var _ref29=function(){var e={};return{registerOrtbProcessor:function registerOrtbProcessor(t){var n=t.type,r=t.name,o=t.fn,_t$priority=t.priority,s=_t$priority===void 0?0:_t$priority,_t$dialects=t.dialects,a=_t$dialects===void 0?[d]:_t$dialects;if(!u.has(n))throw new Error("ORTB processor type must be one of: ".concat(i.join(", ")));
a.forEach(function(t){e.hasOwnProperty(t)||(e[t]={}),e[t].hasOwnProperty(n)||(e[t][n]={}),e[t][n][r]={priority:s,fn:o}})},getProcessors:function getProcessors(t){return e[t]||{}}}}(),l=_ref29.registerOrtbProcessor,f=_ref29.getProcessors},8934:function _(e,t,n){var i=n(7873),r=n(1069),o=n(433),s=n(3172),a=n(2449),d=n(8969),c=n(9075),u=n(6031);var _d$nl=d.nl,l=_d$nl.REQUEST,f=_d$nl.RESPONSE,g=_d$nl.NATIVE,p=_d$nl.EVENT,h=_defineProperty(_defineProperty({},l,function(e,t,n){(0,c.bw)({renderFn:function renderFn(t){e(Object.assign({message:f,
renderer:(0,u.kj)(n),rendererVersion:u.xh},t))},resizeFn:b(t.adId,n),options:t.options,adId:t.adId,bidResponse:n})}),p,function(e,t,n){if(null==n)return void(0,r.vV)("Cannot find ad '".concat(t.adId,"' for x-origin event request"));if(n.status!==d.tl.RENDERED)return void(0,r.JE)("Received x-origin event request without corresponding render request for ad '".concat(n.adId,"'"));return(0,c.Uc)(t,n)});function m(){window.addEventListener("message",function(e){!function(e){var t=e.message?"message":"data",
n={};try{n=JSON.parse(e[t])}catch(e){return}if(n&&n.adId&&n.message&&h.hasOwnProperty(n.message))(0,c.$A)(n.adId,n.message===d.nl.REQUEST).then(function(t){var i,o;h[n.message]((i=n.adId,o=function(e){return null==e.origin&&0===e.ports.length?function(){var e="Cannot post message to a frame with null origin. Please update creatives to use MessageChannel, see https://github.com/prebid/Prebid.js/issues/7870";throw(0,r.vV)(e),new Error(e);}:e.ports.length>0?function(t){e.ports[0].postMessage(JSON.stringify(t))}:
function(t){e.source.postMessage(JSON.stringify(t),e.origin)}}(e),function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return o.apply(void 0,[Object.assign({},e,{adId:i})].concat(n))}),n,t)})}(e)},!1)}function b(e,t){return function(n,i){!function(e){var t=e.instl,n=e.adId,i=e.adUnitCode,o=e.width,s=e.height;if(t)return;function a(e){return e?e+"px":"100%"}function d(e){var t=c(n,i),r=document.getElementById(t);return r&&r.querySelector(e)}function c(e,t){return(0,
r.II)()?u(e):(0,r.t1)()?l(t):t}function u(e){var t=window.googletag.pubads().getSlots().find(function(t){return t.getTargetingKeys().find(function(n){return t.getTargeting(n).includes(e)})});return t?t.getSlotElementId():null}function l(e){var t=window.apntag.getTag(e);return t&&t.targetId}["div","iframe"].forEach(function(e){var t=d(e+':not([style*\x3d"display: none"])');if(t){var _e46=t.style;_e46.width=a(o),_e46.height=a(s)}else(0,r.vV)("Unable to locate matching page element for adUnitCode ".concat(i,
".  Can't resize it to ad's dimensions.  Please review setup."))})}(_objectSpread(_objectSpread({},t),{},{width:n,height:i,adId:e}))}}Object.assign(h,_defineProperty({},g,function(e,t,n){if(null==n)return void(0,r.vV)("Cannot find ad for x-origin event request: '".concat(t.adId,"'"));switch(t.action){case "assetRequest":(0,c.Hh)(n,function(){return e((0,a.IX)(t,n))});break;case "allAssetRequest":(0,c.Hh)(n,function(){return e((0,a.yl)(t,n))});break;default:(0,c.vW)(t,n,{resizeFn:b(t.adId,n)}),(0,
c.Pk)(n)}}));var y=n(8230),v=n(3272),E=n(6881),A=n(7779),w=n(9214),T=n(3597),I=n(1580),C=n(5555),B=n(5569);var O="__pbjs_debugging__";function R(){return(0,i.m)().installedModules.includes("debugging")}function S(e){return new C.U9(function(t){(0,I.R)(e,B.tp,"debugging",t)})}function k(){var _ref30=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_ref30$alreadyInstall=_ref30.alreadyInstalled,e=_ref30$alreadyInstall===void 0?R:_ref30$alreadyInstall,_ref30$script=_ref30.script,t=_ref30$script===
void 0?S:_ref30$script,n=null;return function(){return null==n&&(n=new C.U9(function(n,o){setTimeout(function(){if(e())n();else{var _e47="https://cdn.jsdelivr.net/npm/prebid.js@latest/dist/debugging-standalone.js";(0,r.OG)('Debugging module not installed, loading it from "'.concat(_e47,'"...')),(0,i.m)()._installDebugging=!0,t(_e47).then(function(){(0,i.m)()._installDebugging({DEBUG_KEY:O,hook:w.A_,config:v.$W,createBid:T.O,logger:(0,r.h0)("DEBUG:")})}).then(n,o)}})})),n}}var U=function(){var _ref31=
arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_ref31$load=_ref31.load,e=_ref31$load===void 0?k():_ref31$load,_ref31$hook=_ref31.hook,t=_ref31$hook===void 0?(0,w.Yn)("requestBids"):_ref31$hook,n=null,i=!1;function r(e){var _this9=this;for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];return(n||C.U9.resolve()).then(function(){return e.apply(_this9,i)})}function o(){t.getHooks({hook:r}).remove(),i=!1}return{enable:function enable(){i||(n=e(),t.before(r,99),
i=!0)},disable:o,reset:function reset(){n=null,o()}}}();U.reset;v.$W.getConfig("debugging",function(e){var t=e.debugging;t!==null&&t!==void 0&&t.enabled?U.enable():U.disable()});var D=n(2938),_=n(8046),$=n(5023),j=n(6894),q=n(1970),x=n(6916),N=n(2713),W=n(3895);var P=new Map([["format",function(e){return Array.isArray(e)&&e.length>0&&e.every(function(e){return"object"==_typeof(e)})}],["w",r.Fq],["h",r.Fq],["btype",r.Uu],["battr",r.Uu],["pos",r.Fq],["mimes",function(e){return Array.isArray(e)&&e.length>
0&&e.every(function(e){return"string"==typeof e})}],["topframe",function(e){return[1,0].includes(e)}],["expdir",r.Uu],["api",r.Uu],["id",r.O8],["vcm",function(e){return[1,0].includes(e)}]]);var V=n(1371);function M(e,t){return function(){if(document.prerendering&&e()){var _e48=this,_n13=Array.from(arguments);return new Promise(function(i){document.addEventListener("prerenderingchange",function(){(0,r.fH)("Auctions were suspended while page was prerendering"),i(t.apply(_e48,_n13))},{once:!0})})}return Promise.resolve(t.apply(this,
arguments))}}var G=n(9115);var F=(0,i.m)(),H=y.zt.triggerUserSyncs,_d$qY=d.qY,L=_d$qY.ADD_AD_UNITS,z=_d$qY.REQUEST_BIDS,J=_d$qY.SET_TARGETING,Q={bidWon:function bidWon(e){if(!E.n.getBidsRequested().map(function(e){return e.bids.map(function(e){return e.adUnitCode})}).reduce(r.Bq).filter(r.hj).includes(e))return void(0,r.vV)('The "'+e+'" placement is not defined.');return!0}};function K(e,t){var n=[];return(0,r.cy)(e)&&(t?e.length===t:e.length>0)&&(e.every(function(e){return(0,r.Uu)(e,2)})?n=e:(0,
r.Uu)(e,2)&&n.push(e)),n}function Y(e,t){var n=(0,o.A)(e,"ortb2Imp.".concat(t)),i=(0,o.A)(e,"mediaTypes.".concat(t));if(!n&&!i)return;var a=_defineProperty(_defineProperty({},V.G_,W.Zy),V.D4,P)[t];a&&_toConsumableArray(a).forEach(function(n){var _n14=_slicedToArray(n,2),i=_n14[0],a=_n14[1];var d=(0,o.A)(e,"mediaTypes.".concat(t,".").concat(i)),c=(0,o.A)(e,"ortb2Imp.".concat(t,".").concat(i));null==d&&null==c||(null==d?(0,s.J)(e,"mediaTypes.".concat(t,".").concat(i),c):null==c?(0,s.J)(e,"ortb2Imp.".concat(t,
".").concat(i),d):((0,r.JE)("adUnit ".concat(e.code,": specifies conflicting ortb2Imp.").concat(t,".").concat(i," and mediaTypes.").concat(t,".").concat(i,", the latter will be ignored"),e),(0,s.J)(e,"mediaTypes.".concat(t,".").concat(i),c)))})}function X(e){var _e$ortb2Imp$banner$fo,_e$ortb2Imp,_ref32,_e$ortb2Imp$banner$ex,_e$ortb2Imp2;var t=(0,r.Go)(e),n=t.mediaTypes.banner,i=null==n.sizes?null:K(n.sizes),o=(_e$ortb2Imp$banner$fo=(_e$ortb2Imp=e.ortb2Imp)===null||_e$ortb2Imp===void 0||(_e$ortb2Imp=
_e$ortb2Imp.banner)===null||_e$ortb2Imp===void 0?void 0:_e$ortb2Imp.format)!==null&&_e$ortb2Imp$banner$fo!==void 0?_e$ortb2Imp$banner$fo:n===null||n===void 0?void 0:n.format;var a;if(null!=o){(0,s.J)(t,"ortb2Imp.banner.format",o),n.format=o;try{a=o.filter(function(t){var n=t.w,i=t.h,o=t.wratio,s=t.hratio;return null!=(n!==null&&n!==void 0?n:i)&&null!=(o!==null&&o!==void 0?o:s)?((0,r.JE)("Ad unit banner.format specifies both w/h and wratio/hratio",e),!1):null!=n&&null!=i||null!=o&&null!=s}).map(function(e){var t=
e.w,n=e.h,i=e.wratio,r=e.hratio;return[t!==null&&t!==void 0?t:i,n!==null&&n!==void 0?n:r]})}catch(t){(0,r.vV)("Invalid format definition on ad unit ".concat(e.code),o)}null==a||null==i||(0,r.bD)(i,a)||(0,r.JE)("Ad unit ".concat(e.code," has conflicting sizes and format definitions"),e)}var d=(_ref32=a!==null&&a!==void 0?a:i)!==null&&_ref32!==void 0?_ref32:[],c=(_e$ortb2Imp$banner$ex=(_e$ortb2Imp2=e.ortb2Imp)===null||_e$ortb2Imp2===void 0||(_e$ortb2Imp2=_e$ortb2Imp2.banner)===null||_e$ortb2Imp2===
void 0?void 0:_e$ortb2Imp2.expdir)!==null&&_e$ortb2Imp$banner$ex!==void 0?_e$ortb2Imp$banner$ex:n.expdir;return null!=c&&(n.expdir=c,(0,s.J)(t,"ortb2Imp.banner.expdir",c)),d.length>0?(n.sizes=d,t.sizes=d):((0,r.vV)("Detected a mediaTypes.banner object without a proper sizes field.  Please ensure the sizes are listed like: [[300, 250], ...].  Removing invalid mediaTypes.banner object from request."),delete t.mediaTypes.banner),Y(t,"banner"),t}function Z(e){var t=(0,r.Go)(e),n=t.mediaTypes.video;if(n.playerSize){var _e49=
"number"==typeof n.playerSize[0]?2:1;var _i1=K(n.playerSize,_e49);_i1.length>0?(2===_e49&&(0,r.fH)("Transforming video.playerSize from [640,480] to [[640,480]] so it's in the proper format."),n.playerSize=_i1,t.sizes=_i1):((0,r.vV)("Detected incorrect configuration of mediaTypes.video.playerSize.  Please specify only one set of dimensions in a format like: [[640, 480]]. Removing invalid mediaTypes.video.playerSize property from request."),delete t.mediaTypes.video.playerSize)}return(0,W.aP)(t),Y(t,
"video"),t}function ee(e){function t(t){return(0,r.vV)('Error in adUnit "'.concat(e.code,'": ').concat(t,". Removing native request from ad unit"),e),delete i.mediaTypes["native"],i}function n(e){for(var _i10=0,_arr=["sendTargetingKeys","types"];_i10<_arr.length;_i10++){var _t16=_arr[_i10];if(o.hasOwnProperty(_t16)){var _n15=e(_t16);if(_n15)return _n15}}}var i=(0,r.Go)(e),o=i.mediaTypes["native"];if(o.ortb){var _o$ortb$assets;if((_o$ortb$assets=o.ortb.assets)!==null&&_o$ortb$assets!==void 0&&_o$ortb$assets.some(function(e){return!(0,
r.Et)(e.id)||e.id<0||e.id%1!=0}))return t("native asset ID must be a nonnegative integer");if(n(function(e){return t('ORTB native requests cannot specify "'.concat(e,'"'))}))return i;var _e50=Object.keys(d.x5).filter(function(e){return d.x5[e].includes("hb_native_")}),_s0=Object.keys(o).filter(function(t){return _e50.includes(t)});_s0.length>0&&((0,r.vV)("when using native OpenRTB format, you cannot use legacy native properties. Deleting ".concat(_s0," keys from request.")),_s0.forEach(function(e){return delete i.mediaTypes["native"][e]}))}else n(function(e){return"mediaTypes.native.".concat(e,
" is deprecated, consider using native ORTB instead")});return o.image&&o.image.sizes&&!Array.isArray(o.image.sizes)&&((0,r.vV)("Please use an array of sizes for native.image.sizes field.  Removing invalid mediaTypes.native.image.sizes property from request."),delete i.mediaTypes["native"].image.sizes),o.image&&o.image.aspect_ratios&&!Array.isArray(o.image.aspect_ratios)&&((0,r.vV)("Please use an array of sizes for native.image.aspect_ratios field.  Removing invalid mediaTypes.native.image.aspect_ratios property from request."),
delete i.mediaTypes["native"].image.aspect_ratios),o.icon&&o.icon.sizes&&!Array.isArray(o.icon.sizes)&&((0,r.vV)("Please use an array of sizes for native.icon.sizes field.  Removing invalid mediaTypes.native.icon.sizes property from request."),delete i.mediaTypes["native"].icon.sizes),i}function te(e,t){var _e$mediaTypes2;var n=e===null||e===void 0||(_e$mediaTypes2=e.mediaTypes)===null||_e$mediaTypes2===void 0||(_e$mediaTypes2=_e$mediaTypes2[t])===null||_e$mediaTypes2===void 0?void 0:_e$mediaTypes2.pos;
if(!(0,r.Et)(n)||isNaN(n)||!isFinite(n)){var _n16="Value of property 'pos' on ad unit ".concat(e.code," should be of type: Number");(0,r.JE)(_n16),delete e.mediaTypes[t].pos}return e}function ne(e){var t=function t(_t17){return"adUnit.code '".concat(e.code,"' ").concat(_t17)},n=e.mediaTypes,i=e.bids;return null==i||(0,r.cy)(i)?null==i&&null==e.ortb2Imp?((0,r.vV)(t("has no 'adUnit.bids' and no 'adUnit.ortb2Imp'. Removing adUnit from auction")),null):n&&0!==Object.keys(n).length?(null==e.ortb2Imp||
null!=i&&0!==i.length||(e.bids=[{bidder:null}],(0,r.OG)(t("defines 'adUnit.ortb2Imp' with no 'adUnit.bids'; it will be seen only by S2S adapters"))),e):((0,r.vV)(t("does not define a 'mediaTypes' object.  This is a required field for the auction, so this adUnit has been removed.")),null):((0,r.vV)(t("defines 'adUnit.bids' that is not an array. Removing adUnit from auction")),null)}!function(){var e=null;try{e=window.sessionStorage}catch(e){}if(null!==e){var _t18=U,_n17=null;try{_n17=e.getItem(O)}catch(e){}null!==
_n17&&_t18.enable()}}(),F.bidderSettings=F.bidderSettings||{},F.libLoaded=!0,F.version="v9.53.0-pre",(0,r.fH)("Prebid.js v9.53.0-pre loaded"),F.installedModules=F.installedModules||[],F.adUnits=F.adUnits||[],F.triggerUserSyncs=H;var ie={validateAdUnit:ne,validateBannerMediaType:X,validateSizes:K};Object.assign(ie,{validateNativeMediaType:ee}),Object.assign(ie,{validateVideoMediaType:Z});var re=(0,w.A_)("sync",function(e){var t=[];return e.forEach(function(e){if(null==(e=ne(e)))return;var n=e.mediaTypes;
var i,r,o;n.banner&&(i=X(e),n.banner.hasOwnProperty("pos")&&(i=te(i,"banner"))),n.video&&(r=Z(i||e),n.video.hasOwnProperty("pos")&&(r=te(r,"video"))),n["native"]&&(o=ee(r||i||e));var s=Object.assign({},i,r,o);t.push(s)}),t},"checkAdUnitSetup");function oe(e){var t=E.n[e]().filter(function(e){return E.n.getAdUnitCodes().includes(e.adUnitCode)}),n=E.n.getLastAuctionId();return t.map(function(e){return e.adUnitCode}).filter(r.hj).map(function(e){return t.filter(function(t){return t.auctionId===n&&t.adUnitCode===
e})}).filter(function(e){return e&&e[0]&&e[0].adUnitCode}).map(function(e){return _defineProperty({},e[0].adUnitCode,{bids:e})}).reduce(function(e,t){return Object.assign(e,t)},{})}F.getAdserverTargetingForAdUnitCodeStr=function(e){if((0,r.fH)("Invoking pbjs.getAdserverTargetingForAdUnitCodeStr",arguments),e){var t=F.getAdserverTargetingForAdUnitCode(e);return(0,r.$D)(t)}(0,r.OG)("Need to call getAdserverTargetingForAdUnitCodeStr with adunitCode")},F.getHighestUnusedBidResponseForAdUnitCode=function(e){if(e){var _t19=
E.n.getAllBidsForAdUnitCode(e).filter(A.Yl);return _t19.length?_t19.reduce(N.Vk):{}}(0,r.OG)("Need to call getHighestUnusedBidResponseForAdUnitCode with adunitCode")},F.getAdserverTargetingForAdUnitCode=function(e){return F.getAdserverTargeting(e)[e]},F.getAdserverTargeting=function(e){return(0,r.fH)("Invoking pbjs.getAdserverTargeting",arguments),A.iS.getAllTargeting(e)},F.getConsentMetadata=function(){return(0,r.fH)("Invoking pbjs.getConsentMetadata"),x.SL.getConsentMeta()},F.getNoBids=function(){return(0,
r.fH)("Invoking pbjs.getNoBids",arguments),oe("getNoBids")},F.getNoBidsForAdUnitCode=function(e){return{bids:E.n.getNoBids().filter(function(t){return t.adUnitCode===e})}},F.getBidResponses=function(){return(0,r.fH)("Invoking pbjs.getBidResponses",arguments),oe("getBidsReceived")},F.getBidResponsesForAdUnitCode=function(e){return{bids:E.n.getBidsReceived().filter(function(t){return t.adUnitCode===e})}},F.setTargetingForGPTAsync=function(e,t){(0,r.fH)("Invoking pbjs.setTargetingForGPTAsync",arguments),
(0,r.II)()?A.iS.setTargetingForGPT(e,t):(0,r.vV)("window.googletag is not defined on the page")},F.setTargetingForAst=function(e){(0,r.fH)("Invoking pbjs.setTargetingForAn",arguments),A.iS.isApntagDefined()?(A.iS.setTargetingForAst(e),$.Ic(J,A.iS.getAllTargeting())):(0,r.vV)("window.apntag is not defined on the page")},F.renderAd=(0,w.A_)("async",function(e,t,n){(0,r.fH)("Invoking pbjs.renderAd",arguments),(0,r.OG)("Calling renderAd with adId :"+t),(0,c.BS)(e,t,n)}),F.removeAdUnit=function(e){if((0,
r.fH)("Invoking pbjs.removeAdUnit",arguments),!e)return void(F.adUnits=[]);var t;t=(0,r.cy)(e)?e:[e],t.forEach(function(e){for(var _t20=F.adUnits.length-1;_t20>=0;_t20--)F.adUnits[_t20].code===e&&F.adUnits.splice(_t20,1)})},F.requestBids=function(){var e=(0,w.A_)("async",function(){var _ref34=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=_ref34.bidsBackHandler,t=_ref34.timeout,n=_ref34.adUnits,i=_ref34.adUnitCodes,o=_ref34.labels,s=_ref34.auctionId,a=_ref34.ttlBuffer,d=_ref34.ortb2,
c=_ref34.metrics,u=_ref34.defer;$.Ic(z);var l=t||v.$W.getConfig("bidderTimeout");(0,r.fH)("Invoking pbjs.requestBids",arguments),null==i||Array.isArray(i)||(i=[i]),i&&i.length?n=n.filter(function(e){return i.includes(e.code)}):i=n&&n.map(function(e){return e.code}),i=i.filter(r.hj);var f={global:(0,r.D9)({},v.$W.getAnyConfig("ortb2")||{},d||{}),bidder:Object.fromEntries(Object.entries(v.$W.getBidderConfig()).map(function(e){var _e51=_slicedToArray(e,2),t=_e51[0],n=_e51[1];return[t,(0,r.Go)(n.ortb2)]}).filter(function(e){var _e52=
_slicedToArray(e,2),t=_e52[0],n=_e52[1];return null!=n}))};return(0,q.w)(C.U9.resolve(f.global)).then(function(t){return f.global=t,se({bidsBackHandler:e,timeout:l,adUnits:n,adUnitCodes:i,labels:o,auctionId:s,ttlBuffer:a,ortb2Fragments:f,metrics:c,defer:u})})},"requestBids");return(0,w.Y6)(e,M(function(){return!v.$W.getConfig("allowPrerendering")},function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.adUnits||F.adUnits;return t.adUnits=(0,r.cy)(n)?n.slice():[n],t.metrics=
(0,j.K7)(),t.metrics.checkpoint("requestBids"),t.defer=(0,C.v6)({promiseFactory:function promiseFactory(e){return new Promise(e)}}),e.call(this,t),t.defer.promise}))}();var se=(0,w.A_)("async",function(){var _ref35=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=_ref35.bidsBackHandler,t=_ref35.timeout,n=_ref35.adUnits,i=_ref35.ttlBuffer,o=_ref35.adUnitCodes,a=_ref35.labels,d=_ref35.auctionId,c=_ref35.ortb2Fragments,u=_ref35.metrics,l=_ref35.defer;var f=(0,_.pX)(v.$W.getConfig("s2sConfig")||
[]);function g(t,n,i){if("function"==typeof e)try{e(t,n,i)}catch(e){(0,r.vV)("Error executing bidsBackHandler",null,e)}l.resolve({bids:t,timedOut:n,auctionId:i})}!function(e){e.forEach(function(e){return(0,W.V0)(e)})}(n),n=(0,j.BO)(u).measureTime("requestBids.validate",function(){return re(n)});var p={};if(n.forEach(function(e){var _e$ortb2Imp3;var t=Object.keys(e.mediaTypes||{banner:"banner"}),n=e.bids.map(function(e){return e.bidder}),o=_.Ay.bidderRegistry,s=n.filter(function(e){return!f.has(e)});
e.adUnitId=(0,r.lk)();var a=(_e$ortb2Imp3=e.ortb2Imp)===null||_e$ortb2Imp3===void 0||(_e$ortb2Imp3=_e$ortb2Imp3.ext)===null||_e$ortb2Imp3===void 0?void 0:_e$ortb2Imp3.tid;a&&(p.hasOwnProperty(e.code)?(0,r.JE)("Multiple distinct ortb2Imp.ext.tid were provided for twin ad units '".concat(e.code,"'")):p[e.code]=a),null==i||e.hasOwnProperty("ttlBuffer")||(e.ttlBuffer=i),s.forEach(function(n){var i=o[n],s=i&&i.getSpec&&i.getSpec(),a=s&&s.supportedMediaTypes||["banner"];t.some(function(e){return a.includes(e)})||
((0,r.JE)((0,r.bz)(e,n)),e.bids=e.bids.filter(function(e){return e.bidder!==n}))})}),n&&0!==n.length){n.forEach(function(e){var _e$ortb2Imp4;var t=((_e$ortb2Imp4=e.ortb2Imp)===null||_e$ortb2Imp4===void 0||(_e$ortb2Imp4=_e$ortb2Imp4.ext)===null||_e$ortb2Imp4===void 0?void 0:_e$ortb2Imp4.tid)||p[e.code]||(0,r.lk)();p.hasOwnProperty(e.code)||(p[e.code]=t),e.transactionId=t,(0,s.J)(e,"ortb2Imp.ext.tid",t)});var _e53=E.n.createAuction({adUnits:n,adUnitCodes:o,callback:g,cbTimeout:t,labels:a,auctionId:d,
ortb2Fragments:c,metrics:u});var _i11=n.length;_i11>15&&(0,r.fH)("Current auction ".concat(_e53.getAuctionId()," contains ").concat(_i11," adUnits."),n),o.forEach(function(t){return A.iS.setLatestAuctionForAdUnit(t,_e53.getAuctionId())}),_e53.callBids()}else(0,r.OG)("No adUnits configured. No bids requested."),g()},"startAuction");F.requestBids.before(function(e,t){function n(e){for(var t;t=e.shift();)t()}n(D.s0),n(ae),e.call(this,t)},49),F.addAdUnits=function(e){(0,r.fH)("Invoking pbjs.addAdUnits",
arguments),F.adUnits.push.apply(F.adUnits,(0,r.cy)(e)?e:[e]),$.Ic(L)},F.onEvent=function(e,t,n){(0,r.fH)("Invoking pbjs.onEvent",arguments),(0,r.fp)(t)?!n||Q[e].call(null,n)?$.on(e,t,n):(0,r.vV)('The id provided is not valid for event "'+e+'" and no handler was set.'):(0,r.vV)('The event handler provided is not a function and was not set on event "'+e+'".')},F.offEvent=function(e,t,n){(0,r.fH)("Invoking pbjs.offEvent",arguments),n&&!Q[e].call(null,n)||$.AU(e,t,n)},F.getEvents=function(){return(0,
r.fH)("Invoking pbjs.getEvents"),$.kQ()},F.registerBidAdapter=function(e,t,n){(0,r.fH)("Invoking pbjs.registerBidAdapter",arguments);try{var _i12=n?(0,G.xb)(n):e();_.Ay.registerBidAdapter(_i12,t)}catch(e){(0,r.vV)("Error registering bidder adapter : "+e.message)}},F.registerAnalyticsAdapter=function(e){(0,r.fH)("Invoking pbjs.registerAnalyticsAdapter",arguments);try{_.Ay.registerAnalyticsAdapter(e)}catch(e){(0,r.vV)("Error registering analytics adapter : "+e.message)}},F.createBid=function(e){return(0,
r.fH)("Invoking pbjs.createBid",arguments),(0,T.O)(e)};var ae=[],de=(0,w.A_)("async",function(e){e&&!(0,r.Im)(e)?((0,r.fH)("Invoking pbjs.enableAnalytics for: ",e),_.Ay.enableAnalytics(e)):(0,r.vV)("pbjs.enableAnalytics should be called with option {}")},"enableAnalyticsCb");function ce(e){if("function"==typeof e)try{e.call()}catch(e){(0,r.vV)("Error processing command :",e.message,e.stack)}else(0,r.vV)("Commands written into pbjs.cmd.push must be wrapped in a function")}function ue(e){e.forEach(function(e){if(void 0===
e.called)try{e.call(),e.called=!0}catch(e){(0,r.vV)("Error processing command :","prebid.js",e)}})}F.enableAnalytics=function(e){ae.push(de.bind(this,e))},F.aliasBidder=function(e,t,n){(0,r.fH)("Invoking pbjs.aliasBidder",arguments),e&&t?_.Ay.aliasBidAdapter(e,t,n):(0,r.vV)("bidderCode and alias must be passed as arguments","pbjs.aliasBidder")},F.aliasRegistry=_.Ay.aliasRegistry,v.$W.getConfig("aliasRegistry",function(e){"private"===e.aliasRegistry&&delete F.aliasRegistry}),F.getAllWinningBids=function(){return E.n.getAllWinningBids()},
F.getAllPrebidWinningBids=function(){return(0,r.JE)("getAllPrebidWinningBids may be removed or renamed in a future version. This function returns bids that have won in prebid and have had targeting set but have not (yet?) won in the ad server. It excludes bids that have been rendered."),E.n.getBidsReceived().filter(function(e){return e.status===d.tl.BID_TARGETING_SET})},F.getHighestCpmBids=function(e){return A.iS.getWinningBids(e)},F.clearAllAuctions=function(){E.n.clearAllAuctions()},F.markWinningBidAsUsed=
function(e){var t,n=e.adId,i=e.adUnitCode,_e$analytics=e.analytics,o=_e$analytics===void 0?!1:_e$analytics,_e$events=e.events,s=_e$events===void 0?!1:_e$events;i&&null==n?t=A.iS.getWinningBids(i):n?t=E.n.getBidsReceived().filter(function(e){return e.adId===n}):(0,r.JE)("Improper use of markWinningBidAsUsed. It needs an adUnitCode or an adId to function."),t.length>0&&(o||s?(0,c.n6)(t[0]):E.n.addWinningBid(t[0]),(0,c.qn)(t[0]))},F.getConfig=v.$W.getAnyConfig,F.readConfig=v.$W.readAnyConfig,F.mergeConfig=
v.$W.mergeConfig,F.mergeBidderConfig=v.$W.mergeBidderConfig,F.setConfig=v.$W.setConfig,F.setBidderConfig=v.$W.setBidderConfig,F.que.push(function(){return m()}),F.processQueue=M(function(){return(0,i.m)().delayPrerendering},function(){F.que.push=F.cmd.push=ce,(0,c.XO)(),w.A_.ready(),ue(F.que),ue(F.cmd)}),F.triggerBilling=function(e){var t=e.adId,n=e.adUnitCode;E.n.getAllWinningBids().filter(function(e){return e.adId===t||null==t&&e.adUnitCode===n}).forEach(function(e){_.Ay.triggerBilling(e),(0,c.vB)(e)})}},
7873:function _(e,t,n){n.d(t,{E:function E(){return s},m:function m(){return o}});var i=window,r=i.pbjs=i.pbjs||{};function o(){return r}function s(e){r.installedModules.push(e)}r.cmd=r.cmd||[],r.que=r.que||[],i===window&&(i._pbjsGlobals=i._pbjsGlobals||[],i._pbjsGlobals.push("pbjs"))},7934:function _(e,t,n){n.d(t,{EN:function EN(){return d},gR:function gR(){return s}});var i=n(3272),r=n(1069);function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;if(!e)return e;if(/\w+:\/\//.exec(e))return e;
var n=t.location.protocol;try{n=t.top.location.protocol}catch(e){}return/^\/\//.exec(e)?n+e:"".concat(n,"//").concat(e)}function s(e){var _ref36=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref36$noLeadingWww=_ref36.noLeadingWww,t=_ref36$noLeadingWww===void 0?!1:_ref36$noLeadingWww,_ref36$noPort=_ref36.noPort,n=_ref36$noPort===void 0?!1:_ref36$noPort;try{e=new URL(o(e))}catch(e){return}return e=n?e.hostname:e.host,t&&e.startsWith("www.")&&(e=e.substring(4)),e}function a(e){try{var _t21=
e.querySelector("link[rel\x3d'canonical']");if(null!==_t21)return _t21.href}catch(e){}return null}var d=function(e){var t,n,i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return r.top!==r?e:function(){var o=a(r.document),s=r.location.href;return t===o&&s===n||(t=o,n=s,i=e()),i}}((c=window,function(){var e=[],t=function(e){try{if(!e.location.ancestorOrigins)return;return e.location.ancestorOrigins}catch(e){}}(c),n=i.$W.getConfig("maxNestedIframes");var d,u,l,f,g=!1,p=0,h=!1,m=!1,
b=!1;do{var _n18=d,_i13=m;var _o6=void 0,_s1=!1,_f=null;m=!1,d=d?d.parent:c;try{_o6=d.location.href||null}catch(e){_s1=!0}if(_s1)if(_i13){var _e54=_n18.context;try{_f=_e54.sourceUrl,u=_f,b=!0,h=!0,d===c.top&&(g=!0),_e54.canonicalUrl&&(l=_e54.canonicalUrl)}catch(e){}}else{(0,r.JE)("Trying to access cross domain iframe. Continuing without referrer and location");try{var _e55=_n18.document.referrer;_e55&&(_f=_e55,d===c.top&&(g=!0))}catch(e){}!_f&&t&&t[p-1]&&(_f=t[p-1],d===c.top&&(b=!0)),_f&&!h&&(u=_f)}else{if(_o6&&
(_f=_o6,u=_f,h=!1,d===c.top)){g=!0;var _e56=a(d.document);_e56&&(l=_e56)}d.context&&d.context.sourceUrl&&(m=!0)}e.push(_f),p++}while(d!==c.top&&p<n);e.reverse();try{f=c.top.document.referrer}catch(e){}var y=g||b?u:null,v=i.$W.getConfig("pageUrl")||l||null;var E=i.$W.getConfig("pageUrl")||y||o(v,c);return y&&y.indexOf("?")>-1&&-1===E.indexOf("?")&&(E="".concat(E).concat(y.substring(y.indexOf("?")))),{reachedTop:g,isAmp:h,numIframes:p-1,stack:e,topmostLocation:u||null,location:y,canonicalUrl:v,page:E,
domain:s(E)||null,ref:f||null,legacy:{reachedTop:g,isAmp:h,numIframes:p-1,stack:e,referer:u||null,canonicalUrl:v}}}));var c},2938:function _(e,t,n){n.d(t,{CK:function CK(){return m},s0:function s0(){return p}});var i=n(1069),r=n(2693),o=n(5569),s=n(5139),a=n(2604),d=n(6811),c=n(3272),u=n(8046),l=n(3441);var f="html5",g="cookie";var p=[];function h(){var _ref37=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=_ref37.moduleName,t=_ref37.moduleType,_ref38=arguments.length>1&&void 0!==arguments[1]?
arguments[1]:{},_ref38$isAllowed=_ref38.isAllowed,n=_ref38$isAllowed===void 0?s.io:_ref38$isAllowed;function r(i,r){var s=e;var f=c.$W.getCurrentBidder();f&&t===o.tW&&u.Ay.aliasRegistry[f]===e&&(s=f);return i({valid:n(d.Ue,(0,l.s)(t,s,_defineProperty({},a.Zw,r)))})}function h(e,t,n){if(!n||"function"!=typeof n)return r(e,t);p.push(function(){var i=r(e,t);n(i)})}function m(e){var t=e.charAt(0).toUpperCase()+e.substring(1),n=function n(){return window[e]},r=function r(t){return h(function(t){if(t&&
t.valid)try{return!!n()}catch(t){(0,i.vV)("".concat(e," api disabled"))}return!1},f,t)};return _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({},"has".concat(t),r),"".concat(e,"IsEnabled"),function IsEnabled(e){return h(function(e){if(e&&e.valid)try{return n().setItem("prebid.cookieTest","1"),"1"===n().getItem("prebid.cookieTest")}catch(e){}finally{try{n().removeItem("prebid.cookieTest")}catch(e){}}return!1},f,e)}),"setDataIn".concat(t),function setDataIn(e,t,i){return h(function(i){i&&
i.valid&&r()&&n().setItem(e,t)},f,i)}),"getDataFrom".concat(t),function getDataFrom(e,t){return h(function(t){return t&&t.valid&&r()?n().getItem(e):null},f,t)}),"removeDataFrom".concat(t),function removeDataFrom(e,t){return h(function(t){t&&t.valid&&r()&&n().removeItem(e)},f,t)})}return _objectSpread(_objectSpread(_objectSpread({setCookie:function setCookie(e,t,n,i,r,o){return h(function(o){if(o&&o.valid){var _o7=r&&""!==r?" ;domain\x3d".concat(encodeURIComponent(r)):"",_s10=n&&""!==n?" ;expires\x3d".concat(n):
"",_a0=null!=i&&"none"==i.toLowerCase()?"; Secure":"";document.cookie="".concat(e,"\x3d").concat(encodeURIComponent(t)).concat(_s10,"; path\x3d/").concat(_o7).concat(i?"; SameSite\x3d".concat(i):"").concat(_a0)}},g,o)},getCookie:function getCookie(e,t){return h(function(t){if(t&&t.valid){var _t22=window.document.cookie.match("(^|;)\\s*"+e+"\\s*\x3d\\s*([^;]*)\\s*(;|$)");return _t22?decodeURIComponent(_t22[2]):null}return null},g,t)},cookiesAreEnabled:function cookiesAreEnabled(e){return h(function(e){return!(!e||
!e.valid)&&(0,i.GE)()},g,e)}},m("localStorage")),m("sessionStorage")),{},{findSimilarCookies:function findSimilarCookies(e,t){return h(function(t){if(t&&t.valid){var _t23=[];if((0,i.N9)()){var _n19=document.cookie.split(";");for(;_n19.length;){var _i14=_n19.pop();var _r1=_i14.indexOf("\x3d");_r1=_r1<0?_i14.length:_r1;decodeURIComponent(_i14.slice(0,_r1).replace(/^\s+/,"")).indexOf(e)>=0&&_t23.push(decodeURIComponent(_i14.slice(_r1+1)))}}return _t23}},g,t)}})}function m(e){return h({moduleName:e,moduleType:o.tp})}
(0,s.qB)(d.Ue,"deviceAccess config",function(){if(!(0,i.N9)())return{allow:!1}}),(0,s.qB)(d.Ue,"bidderSettings.*.storageAllowed",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.u;if(e[a.Dk]!==o.tW)return;var n=t.get(e[a.q7],"storageAllowed");if(n&&!0!==n){var _t24=e[a.Zw];n=Array.isArray(n)?n.some(function(e){return e===_t24}):n===_t24}else n=!!n;return n?void 0:{allow:n}})},7779:function _(e,t,n){n.d(t,{Yl:function Yl(){return w},iS:function iS(){return B},uW:function uW(){return A}});
var i=n(6881),r=n(7863),o=n(2693),s=n(3272),a=n(8969),d=n(5023),c=n(9214),u=n(1371),l=n(2449),f=n(1069),g=n(433),p=n(2713),h=[];var m=20,b="targetingControls.allowTargetingKeys",y="targetingControls.addTargetingKeys",v='Only one of "'.concat(b,'" or "').concat(y,'" can be set'),E=Object.keys(a.xS).map(function(e){return a.xS[e]});var A={isActualBid:function isActualBid(e){return e.getStatusCode()===a.XQ.GOOD},isBidNotExpired:function isBidNotExpired(e){return e.responseTimestamp+1E3*(0,r.cT)(e)>(0,
f.vE)()},isUnusedBid:function isUnusedBid(e){return e&&(e.status&&![a.tl.RENDERED].includes(e.status)||!e.status)}};function w(e){return!Object.values(A).some(function(t){return!t(e)})}var T=(0,c.A_)("sync",function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:f.Q0;if(!i){var _i15=[],_o8=s.$W.getConfig("sendBidsControl.dealPrioritization");var _a1=(0,f.$z)(e,"adUnitCode");
return Object.keys(_a1).forEach(function(e){var s=[],d=(0,f.$z)(_a1[e],"bidderCode");Object.keys(d).forEach(function(e){s.push(d[e].reduce(t))}),n?(s=_o8?s.sort(I(!0)):s.sort(function(e,t){return t.cpm-e.cpm}),_i15.push.apply(_i15,_toConsumableArray(s.slice(0,n)))):(s=s.sort(r),_i15.push.apply(_i15,_toConsumableArray(s)))}),_i15}return e});function I(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n){return void 0!==t.adserverTargeting.hb_deal&&void 0===n.adserverTargeting.hb_deal?
-1:void 0===t.adserverTargeting.hb_deal&&void 0!==n.adserverTargeting.hb_deal?1:e?n.cpm-t.cpm:n.adserverTargeting.hb_pb-t.adserverTargeting.hb_pb}}function C(e,t){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return window.googletag.pubads().getSlots()})().reduce(function(e,n){var i=(0,f.fp)(t)&&t(n);return Object.keys(e).filter((0,f.fp)(i)?i:(0,f.iC)(n)).forEach(function(t){return e[t].push(n)}),e},Object.fromEntries(e.map(function(e){return[e,[]]})))}var B=function(e){var t=
{},n={};function i(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var i=E.concat(l.Nh),r=s.$W.getConfig("targetingControls.allowSendAllBidsTargetingKeys"),o=r?r.map(function(e){return a.xS[e]}):i;return e.reduce(function(e,r){if(t||n&&r.dealId){var _t25=function(e,t){return t.reduce(function(t,n){return e.adserverTargeting[n]&&t.push(_defineProperty({},"".concat(n,"_").concat(e.bidderCode).substring(0,20),[e.adserverTargeting[n]])),
t},[])}(r,i.filter(function(e){return void 0!==r.adserverTargeting[e]&&(n||-1!==o.indexOf(e))}));_t25&&e.push(_defineProperty({},r.adUnitCode,_t25))}return e},[])}function r(t){return"string"==typeof t?[t]:(0,f.cy)(t)?t:e.getAdUnitCodes()||[]}function A(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p.Bq,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=e.getBidsReceived().reduce(function(e,t){var _t$video;var i=s.$W.getConfig("useBidCache"),r=s.$W.getConfig("bidCacheFilterFunction"),
o=n[t.adUnitCode]===t.auctionId,a=!(i&&!o&&"function"==typeof r)||!!r(t);return(i||o)&&a&&(t===null||t===void 0||(_t$video=t.video)===null||_t$video===void 0?void 0:_t$video.context)!==u.LM&&w(t)&&(t.latestTargetedAuctionId=n[t.adUnitCode],e.push(t)),e},[]);return T(r,t,void 0,void 0,void 0,i)}function B(e,n){var i=t.getWinningBids(n,e),r=O();return i=i.map(function(e){return _defineProperty({},e.adUnitCode,Object.keys(e.adserverTargeting).filter(function(t){return void 0===e.sendStandardTargeting||
e.sendStandardTargeting||-1===r.indexOf(t)}).reduce(function(t,n){var i=[e.adserverTargeting[n]],r=_defineProperty({},n.substring(0,m),i);if(n===a.xS.DEAL){var _o9="".concat(n,"_").concat(e.bidderCode).substring(0,m),_s11=_defineProperty({},_o9,i);return[].concat(_toConsumableArray(t),[r,_s11])}return[].concat(_toConsumableArray(t),[r])},[]))}),i}function O(){return e.getStandardBidderAdServerTargeting().map(function(e){return e.key}).concat(E).filter(f.hj)}return t.setLatestAuctionForAdUnit=function(e,
t){n[e]=t},t.resetPresetTargeting=function(e,t){if((0,f.II)()){var _n20=r(e);Object.values(C(_n20,t)).forEach(function(e){e.forEach(function(e){!function(e){h.forEach(function(t){e.getTargeting(t)&&e.clearTargeting(t)})}(e)})})}},t.resetPresetTargetingAST=function(e){r(e).forEach(function(e){var t=window.apntag.getTag(e);if(t&&t.keywords){var _n21=Object.keys(t.keywords),_i16={};_n21.forEach(function(e){h.includes(e.toLowerCase())||(_i16[e]=t.keywords[e])}),window.apntag.modifyTag(e,{keywords:_i16})}})},
t.getAllTargeting=function(t,n,d){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:p.Vk,u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:f.Q0;d||(d=A(c,u));var g=r(t),E=s.$W.getConfig("enableSendAllBids"),w=s.$W.getConfig("sendBidsControl.bidLimit"),C=E&&(n||w)||0,_ref42=function(e,t){var n=[],i={},r=s.$W.getConfig("targetingControls.alwaysIncludeDeals");return t.forEach(function(t){var s=e.includes(t.adUnitCode),a=!0===o.u.get(t.bidderCode,"allowZeroCpmBids")?t.cpm>=0:t.cpm>0,
d=r&&t.dealId;s&&(d||a)&&(n.push(t),Object.keys(t.adserverTargeting).filter(function(){var e=O();e=e.concat(l.Nh);return function(t){return-1===e.indexOf(t)}}()).forEach(function(e){var n=e.substring(0,m),r=i[t.adUnitCode]||{},o=[t.adserverTargeting[e]];r[n]?r[n]=r[n].concat(o).filter(f.hj):r[n]=o,i[t.adUnitCode]=r}))}),{filteredBids:n,customKeysByUnit:i}}(g,d),R=_ref42.customKeysByUnit,S=_ref42.filteredBids;var k=function(t,n,r){var o=!1!==s.$W.getConfig("targetingControls.allBidsCustomTargeting"),
d=B(t,r).concat(function(e){var t=s.$W.getConfig("targetingControls.alwaysIncludeDeals");return i(e,s.$W.getConfig("enableSendAllBids"),t)}(t)).concat(function(t){function n(e){return e===null||e===void 0?void 0:e[a.iD.ADSERVER_TARGETING]}function i(e){var t=n(e);return Object.keys(t).map(function(e){return(0,f.O8)(t[e])&&(t[e]=t[e].split(",").map(function(e){return e.trim()})),(0,f.cy)(t[e])||(t[e]=[t[e]]),_defineProperty({},e,t[e])})}return e.getAdUnits().filter(function(e){return t.includes(e.code)&&
n(e)}).reduce(function(e,t){var n=i(t);return n&&e.push(_defineProperty({},t.code,n)),e},[])}(r));o&&d.push.apply(d,_toConsumableArray(function(e,t){return e.reduce(function(e,n){var i=Object.assign({},n),r=t[i.adUnitCode],o=[];return r&&Object.keys(r).forEach(function(e){e&&r[e]&&o.push(_defineProperty({},e,r[e]))}),e.push(_defineProperty({},i.adUnitCode,o)),e},[])}(t,n)));return d.forEach(function(e){!function(e){Object.keys(e).forEach(function(t){e[t].forEach(function(e){var t=Object.keys(e);-1===
h.indexOf(t[0])&&(h=t.concat(h))})})}(e)}),d}(T(S,c,C,void 0,u),R,g);var U=Object.keys(Object.assign({},a.Zh,a.x5));var D=s.$W.getConfig(b);var _=s.$W.getConfig(y);if(null!=_&&null!=D)throw new Error(v);D=null!=_?U.concat(_):D||U,Array.isArray(D)&&D.length>0&&(k=function(e,t){var n=Object.assign({},a.xS,a.x5),i=Object.keys(n),r={};(0,f.fH)("allowTargetingKeys - allowed keys [ ".concat(t.map(function(e){return n[e]}).join(", ")," ]")),e.map(function(e){var o=Object.keys(e)[0],s=e[o].filter(function(e){var o=
Object.keys(e)[0],s=0===i.filter(function(e){return 0===o.indexOf(n[e])}).length||t.find(function(e){var t=n[e];return 0===o.indexOf(t)});return r[o]=!s,s});e[o]=s});var o=Object.keys(r).filter(function(e){return r[e]});return(0,f.fH)("allowTargetingKeys - removed keys [ ".concat(o.join(", ")," ]")),e.filter(function(e){return e[Object.keys(e)[0]].length>0})}(k,D)),k=function(e){var t=e.map(function(e){return _defineProperty({},Object.keys(e)[0],e[Object.keys(e)[0]].map(function(e){return _defineProperty({},
Object.keys(e)[0],e[Object.keys(e)[0]].join(","))}).reduce(function(e,t){return Object.assign(t,e)},{}))});return t=t.reduce(function(e,t){var n=Object.keys(t)[0];return e[n]=Object.assign({},e[n],t[n]),e},{}),t}(k);var $=s.$W.getConfig("targetingControls.auctionKeyMaxChars");return $&&((0,f.fH)("Detected 'targetingControls.auctionKeyMaxChars' was active for this auction; set with a limit of ".concat($," characters.  Running checks on auction keys...")),k=function(e,t){var n=(0,f.Go)(e),i=Object.keys(n).map(function(e){return{adUnitCode:e,
adserverTargeting:n[e]}}).sort(I());return i.reduce(function(e,i,r,o){var s=(a=i.adserverTargeting,Object.keys(a).reduce(function(e,t){return e+"".concat(t,"%3d").concat(encodeURIComponent(a[t]),"%26")},""));var a;r+1===o.length&&(s=s.slice(0,-3));var d=i.adUnitCode,c=s.length;return c<=t?(t-=c,(0,f.fH)("AdUnit '".concat(d,"' auction keys comprised of ").concat(c," characters.  Deducted from running threshold; new limit is ").concat(t),n[d]),e[d]=n[d]):(0,f.JE)("The following keys for adUnitCode '".concat(d,
"' exceeded the current limit of the 'auctionKeyMaxChars' setting.\nThe key-set size was ").concat(c,", the current allotted amount was ").concat(t,".\n"),n[d]),r+1===o.length&&0===Object.keys(e).length&&(0,f.vV)("No auction targeting keys were permitted due to the setting in setConfig(targetingControls.auctionKeyMaxChars).  Please review setup and consider adjusting."),e},{})}(k,$)),g.forEach(function(e){k[e]||(k[e]={})}),k},s.$W.getConfig("targetingControls",function(e){null!=(0,g.A)(e,b)&&null!=
(0,g.A)(e,y)&&(0,f.vV)(v)}),t.setTargetingForGPT=(0,c.A_)("sync",function(n,i){var r=t.getAllTargeting(n),o=Object.fromEntries(h.map(function(e){return[e,null]}));Object.entries(C(Object.keys(r),i)).forEach(function(e){var _e57=_slicedToArray(e,2),t=_e57[0],n=_e57[1];n.length>1&&(0,f.JE)("Multiple slots found matching: ".concat(t,". Targeting will be set on all matching slots, which can lead to duplicate impressions if more than one are requested from GAM. To resolve this, ensure the arguments to setTargetingForGPTAsync resolve to a single slot by explicitly matching the desired slotElementID.")),
n.forEach(function(e){Object.keys(r[t]).forEach(function(e){var n=r[t][e];"string"==typeof n&&-1!==n.indexOf(",")&&(n=n.split(",")),r[t][e]=n}),(0,f.OG)("Attempting to set targeting-map for slot: ".concat(e.getSlotElementId()," with targeting-map:"),r[t]),e.updateTargetingFromMap(Object.assign({},o,r[t]))})}),Object.keys(r).forEach(function(t){Object.keys(r[t]).forEach(function(n){"hb_adid"===n&&e.setStatusForBids(r[t][n],a.tl.BID_TARGETING_SET)})}),t.targetingDone(r),d.Ic(a.qY.SET_TARGETING,r)},
"setTargetingForGPT"),t.targetingDone=(0,c.A_)("sync",function(e){return e},"targetingDone"),t.getWinningBids=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.Vk,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:f.Q0;var s=t||A(n,i),a=r(e);return s.filter(function(e){return a.includes(e.adUnitCode)}).filter(function(e){return!0===o.u.get(e.bidderCode,"allowZeroCpmBids")?e.cpm>=0:e.cpm>0}).map(function(e){return e.adUnitCode}).filter(f.hj).map(function(e){return s.filter(function(t){return t.adUnitCode===
e?t:null}).reduce(p.Vk)})},t.setTargetingForAst=function(e){var n=t.getAllTargeting(e);try{t.resetPresetTargetingAST(e)}catch(e){(0,f.vV)("unable to reset targeting for AST"+e)}Object.keys(n).forEach(function(e){return Object.keys(n[e]).forEach(function(t){if((0,f.OG)("Attempting to set targeting for targetId: ".concat(e," key: ").concat(t," value: ").concat(n[e][t])),(0,f.O8)(n[e][t])||(0,f.cy)(n[e][t])){var _i17={},_r11=/pt[0-9]/;t.search(_r11)<0?_i17[t.toUpperCase()]=n[e][t]:_i17[t]=n[e][t],window.apntag.setKeywords(e,
_i17,{overrideKeyValue:!0})}})})},t.isApntagDefined=function(){if(window.apntag&&(0,f.fp)(window.apntag.setKeywords))return!0},t}(i.n)},8230:function _(e,t,n){n.d(t,{zt:function zt(){return f}});var i=n(1069),r=n(3272),o=n(2938),s=n(5139),a=n(6811),d=n(2604),c=n(5569),u=n(3441);r.$W.setDefaults({userSync:(0,i.Go)({syncEnabled:!0,filterSettings:{image:{bidders:"*",filter:"include"}},syncsPerBidder:5,syncDelay:3E3,auctionDelay:500})});var l=(0,o.CK)("usersync");var f=function(e){var t={},n={image:[],
iframe:[]},o=new Set,s={},l={image:!0,iframe:!1},f=e.config;function g(){if(f.syncEnabled&&e.browserSupportsCookies){try{!function(){if(!l.iframe)return;p(n.iframe,function(e){var _e58=_slicedToArray(e,2),t=_e58[0],r=_e58[1];(0,i.OG)("Invoking iframe user sync for bidder: ".concat(t)),(0,i.SG)(r),function(e,t){e.image=e.image.filter(function(e){return e[0]!==t})}(n,t)})}(),function(){if(!l.image)return;p(n.image,function(e){var _e59=_slicedToArray(e,2),t=_e59[0],n=_e59[1];(0,i.OG)("Invoking image pixel user sync for bidder: ".concat(t)),
(0,i.z$)(n)})}()}catch(e){return(0,i.vV)("Error firing user syncs",e)}n={image:[],iframe:[]}}}function p(e,t){(0,i.k4)(e).forEach(t)}function h(e,t){var n=f.filterSettings;if(function(e,t){if(e.all&&e[t])return(0,i.JE)('Detected presence of the "filterSettings.all" and "filterSettings.'.concat(t,'" in userSync config.  You cannot mix "all" with "iframe/image" configs; they are mutually exclusive.')),!1;var n=e.all?e.all:e[t],r=e.all?"all":t;if(!n)return!1;var o=n.filter,s=n.bidders;if(o&&"include"!==
o&&"exclude"!==o)return(0,i.JE)('UserSync "filterSettings.'.concat(r,".filter\" setting '").concat(o,"' is not a valid option; use either 'include' or 'exclude'.")),!1;if("*"!==s&&!(Array.isArray(s)&&s.length>0&&s.every(function(e){return(0,i.O8)(e)&&"*"!==e})))return(0,i.JE)('Detected an invalid setup in userSync "filterSettings.'.concat(r,".bidders\"; use either '*' (to represent all bidders) or an array of bidders.")),!1;return!0}(n,e)){l[e]=!0;var _i18=n.all?n.all:n[e],_r12="*"===_i18.bidders?
[t]:_i18.bidders;var _o0={include:function include(e,t){return!e.includes(t)},exclude:function exclude(e,t){return e.includes(t)}};return _o0[_i18.filter||"include"](_r12,t)}return!l[e]}return r.$W.getConfig("userSync",function(e){if(e.userSync){var _t26=e.userSync.filterSettings;(0,i.Qd)(_t26)&&(_t26.image||_t26.all||(e.userSync.filterSettings.image={bidders:"*",filter:"include"}))}f=Object.assign(f,e.userSync)}),e.regRule(a.Ml,"userSync config",function(e){if(!f.syncEnabled)return{allow:!1,reason:"syncs are disabled"};
if(e[d.Dk]===c.tW){var _n22=e[d.bt],_i19=e[d.iK];if(!t.canBidderRegisterSync(_n22,_i19))return{allow:!1,reason:"".concat(_n22," syncs are not enabled for ").concat(_i19)}}}),t.registerSync=function(t,r,l){return o.has(r)?(0,i.OG)('already fired syncs for "'.concat(r,'", ignoring registerSync call')):f.syncEnabled&&(0,i.cy)(n[t])?r?0!==f.syncsPerBidder&&Number(s[r])>=f.syncsPerBidder?(0,i.JE)('Number of user syncs exceeded for "'.concat(r,'"')):void(e.isAllowed(a.Ml,(0,u.s)(c.tW,r,_defineProperty(_defineProperty({},
d.bt,t),d.e3,l)))&&(n[t].push([r,l]),s=function(e,t){return e[t]?e[t]+=1:e[t]=1,e}(s,r))):(0,i.JE)("Bidder is required for registering sync"):(0,i.JE)('User sync type "'.concat(t,'" not supported'))},t.bidderDone=o.add.bind(o),t.syncUsers=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(e)return setTimeout(g,Number(e));g()},t.triggerUserSyncs=function(){f.enableOverride&&t.syncUsers()},t.canBidderRegisterSync=function(e,t){return!f.filterSettings||!h(e,t)},t}(Object.defineProperties({config:r.$W.getConfig("userSync"),
isAllowed:s.io,regRule:s.qB},{browserSupportsCookies:{get:function get(){return!(0,i.Vt)()&&l.cookiesAreEnabled()}}}))},1069:function _(e,t,n){n.d(t,{$D:function $D(){return D},$z:function $z(){return Re},Bg:function Bg(){return ze},Bj:function Bj(){return Le},Bq:function Bq(){return fe},CA:function CA(){return Q},D9:function D9(){return Ge},Dl:function Dl(){return Pe},El:function El(){return Je},Et:function Et(){return te},Ez:function Ez(){return K},Fq:function Fq(){return qe},GE:function GE(){return Be},
Go:function Go(){return ye},II:function II(){return pe},Im:function Im(){return re},JE:function JE(){return G},Lm:function Lm(){return ie},N9:function N9(){return Ce},O8:function O8(){return Z},OG:function OG(){return V},Ot:function Ot(){return C},PB:function PB(){return He},Q0:function Q0(){return me},Qd:function Qd(){return ne},SB:function SB(){return Ue},SG:function SG(){return ue},SH:function SH(){return Se},U6:function U6(){return Oe},Up:function Up(){return xe},Uu:function Uu(){return Ne},V:function V(){return Ie},
Vt:function Vt(){return Ee},YE:function YE(){return W},ZA:function ZA(){return ge},ZK:function ZK(){return Ye},ZU:function ZU(){return we},_s:function _s(){return se},al:function al(){return ve},bD:function bD(){return Me},bL:function bL(){return U},bz:function bz(){return je},c$:function c$(){return Ve},cD:function cD(){return T},cy:function cy(){return ee},dp:function dp(){return z},eY:function eY(){return Qe},fH:function fH(){return M},fp:function fp(){return X},gM:function gM(){return Ae},h0:function h0(){return H},
hj:function hj(){return le},hw:function hw(){return J},iC:function iC(){return $e},k4:function k4(){return be},kK:function kK(){return _},l4:function l4(){return x},l9:function l9(){return De},lk:function lk(){return k},mM:function mM(){return B},mb:function mb(){return q},nT:function nT(){return Ke},ro:function ro(){return ce},s0:function s0(){return S},t1:function t1(){return he},vE:function vE(){return Te},vV:function vV(){return F},wD:function wD(){return ke},xQ:function xQ(){return oe},z$:function z$(){return de}});
var i=n(3272),r=n(5751),o=n(8969),s=n(5555),a=n(7873),d=n(433),c="String",u="Function",l="Number",f="Object",g="Boolean",p=Object.prototype.toString;var h,m,b=Boolean(window.console),y=Boolean(b&&window.console.log),v=Boolean(b&&window.console.info),E=Boolean(b&&window.console.warn),A=Boolean(b&&window.console.error);var w=(0,a.m)();function T(e){h=e}function I(){null!=h&&h.apply(void 0,arguments)}var C=function(){var e;return function(){return(!m||!e||Date.now()-e>20)&&(B.resetWinDimensions(),e=
Date.now()),m}}();var B={checkCookieSupport:Be,createTrackPixelIframeHtml:function createTrackPixelIframeHtml(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(!e)return"";t&&(e=encodeURI(e));n&&(n='sandbox\x3d"'.concat(n,'"'));return"\x3ciframe ".concat(n,' id\x3d"').concat(S(),'"\n      frameborder\x3d"0"\n      allowtransparency\x3d"true"\n      marginheight\x3d"0" marginwidth\x3d"0"\n      width\x3d"0" hspace\x3d"0" vspace\x3d"0" height\x3d"0"\n      style\x3d"height:0px;width:0px;display:none;"\n      scrolling\x3d"no"\n      src\x3d"').concat(e,
'"\x3e\n    \x3c/iframe\x3e')},getWindowSelf:x,getWindowTop:q,canAccessWindowTop:P,getWindowLocation:N,insertUserSyncIframe:ue,insertElement:se,isFn:X,triggerPixel:de,logError:F,logWarn:G,logMessage:V,logInfo:M,parseQS:We,formatQS:function formatQS(e){return Object.keys(e).map(function(t){return Array.isArray(e[t])?e[t].map(function(e){return"".concat(t,"[]\x3d").concat(e)}).join("\x26"):"".concat(t,"\x3d").concat(e[t])}).join("\x26")},deepEqual:Me,resetWinDimensions:function resetWinDimensions(){var _e$screen,
_e$screen2,_e$screen3,_e$screen4,_e$screen5,_e$visualViewport,_e$visualViewport2,_e$document,_e$document2,_e$document3,_e$document4,_document$body,_document$body2,_document$body3,_document$body4;var e=P()?B.getWindowTop():B.getWindowSelf();m={screen:{width:(_e$screen=e.screen)===null||_e$screen===void 0?void 0:_e$screen.width,height:(_e$screen2=e.screen)===null||_e$screen2===void 0?void 0:_e$screen2.height,availWidth:(_e$screen3=e.screen)===null||_e$screen3===void 0?void 0:_e$screen3.availWidth,availHeight:(_e$screen4=
e.screen)===null||_e$screen4===void 0?void 0:_e$screen4.availHeight,colorDepth:(_e$screen5=e.screen)===null||_e$screen5===void 0?void 0:_e$screen5.colorDepth},innerHeight:e.innerHeight,innerWidth:e.innerWidth,outerWidth:e.outerWidth,outerHeight:e.outerHeight,visualViewport:{height:(_e$visualViewport=e.visualViewport)===null||_e$visualViewport===void 0?void 0:_e$visualViewport.height,width:(_e$visualViewport2=e.visualViewport)===null||_e$visualViewport2===void 0?void 0:_e$visualViewport2.width},document:{documentElement:{clientWidth:(_e$document=
e.document)===null||_e$document===void 0||(_e$document=_e$document.documentElement)===null||_e$document===void 0?void 0:_e$document.clientWidth,clientHeight:(_e$document2=e.document)===null||_e$document2===void 0||(_e$document2=_e$document2.documentElement)===null||_e$document2===void 0?void 0:_e$document2.clientHeight,scrollTop:(_e$document3=e.document)===null||_e$document3===void 0||(_e$document3=_e$document3.documentElement)===null||_e$document3===void 0?void 0:_e$document3.scrollTop,scrollLeft:(_e$document4=
e.document)===null||_e$document4===void 0||(_e$document4=_e$document4.documentElement)===null||_e$document4===void 0?void 0:_e$document4.scrollLeft},body:{scrollTop:(_document$body=document.body)===null||_document$body===void 0?void 0:_document$body.scrollTop,scrollLeft:(_document$body2=document.body)===null||_document$body2===void 0?void 0:_document$body2.scrollLeft,clientWidth:(_document$body3=document.body)===null||_document$body3===void 0?void 0:_document$body3.clientWidth,clientHeight:(_document$body4=
document.body)===null||_document$body4===void 0?void 0:_document$body4.clientHeight}}}}};var O,R=(O=0,function(){return++O});function S(){return R()+Math.random().toString(16).substr(2)}function k(e){return e?(e^(window&&window.crypto&&window.crypto.getRandomValues?crypto.getRandomValues(new Uint8Array(1))[0]%16:16*Math.random())>>e/4).toString(16):([1E7]+-1E3+-4E3+-8E3+-1E11).replace(/[018]/g,k)}function U(e){var t="";for(var n in e)e.hasOwnProperty(n)&&(t+=n+"\x3d"+encodeURIComponent(e[n])+"\x26");
return t=t.replace(/&$/,""),t}function D(e){return e&&Object.getOwnPropertyNames(e).length>0?Object.keys(e).map(function(t){return"".concat(t,"\x3d").concat(encodeURIComponent(e[t]))}).join("\x26"):""}function _(e){return(t=e,"string"==typeof t?t.split(/\s*,\s*/).map(function(e){return e.match(/^(\d+)x(\d+)$/i)}).filter(function(e){return e}).map(function(e){var _e60=_slicedToArray(e,3),t=_e60[0],n=_e60[1],i=_e60[2];return[parseInt(n,10),parseInt(i,10)]}):Array.isArray(t)?j(t)?[t]:t.filter(j):[]).map($);
var t}function $(e){return e[0]+"x"+e[1]}function j(e){return ee(e)&&2===e.length&&!isNaN(e[0])&&!isNaN(e[1])}function q(){return window.top}function x(){return window.self}function N(){return window.location}function W(){return document}function P(){try{if(B.getWindowTop().location.href)return!0}catch(e){return!1}}function V(){z()&&y&&console.log.apply(console,L(arguments,"MESSAGE:"))}function M(){z()&&v&&console.info.apply(console,L(arguments,"INFO:"))}function G(){z()&&E&&console.warn.apply(console,
L(arguments,"WARNING:")),I(o.qY.AUCTION_DEBUG,{type:"WARNING",arguments:arguments})}function F(){z()&&A&&console.error.apply(console,L(arguments,"ERROR:")),I(o.qY.AUCTION_DEBUG,{type:"ERROR",arguments:arguments})}function H(e){function t(t){return function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];t.apply(void 0,[e].concat(i))}}return{logError:t(F),logWarn:t(G),logMessage:t(V),logInfo:t(M)}}function L(e,t){e=[].slice.call(e);var n=i.$W.getCurrentBidder();return t&&
e.unshift(t),n&&e.unshift(r("#aaa")),e.unshift(r("#3b88c3")),e.unshift("%cPrebid"+(n?"%c".concat(n):"")),e;function r(e){return"display: inline-block; color: #fff; background: ".concat(e,"; padding: 1px 4px; border-radius: 3px;")}}function z(){return!!i.$W.getConfig("debug")}var J=function(){var e={border:"0px",hspace:"0",vspace:"0",marginWidth:"0",marginHeight:"0",scrolling:"no",frameBorder:"0",allowtransparency:"true"};return function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:
{};var r=t.createElement("iframe");return Object.assign(r,Object.assign({},e,n)),Object.assign(r.style,i),r}}();function Q(){return J(document,{id:S(),width:0,height:0,src:"about:blank"},{display:"none",height:"0px",width:"0px",border:"0px"})}function K(e){return We(N().search)[e]||""}function Y(e,t){return p.call(e)==="[object "+t+"]"}function X(e){return Y(e,u)}function Z(e){return Y(e,c)}var ee=Array.isArray.bind(Array);function te(e){return Y(e,l)}function ne(e){return Y(e,f)}function ie(e){return Y(e,
g)}function re(e){return!e||(ee(e)||Z(e)?!(e.length>0):Object.keys(e).length<=0)}function oe(e){return Z(e)&&(!e||0===e.length)}function se(e,t,n,i){var r;t=t||document,r=n?t.getElementsByTagName(n):t.getElementsByTagName("head");try{if(r=r.length?r:t.getElementsByTagName("body"),r.length){r=r[0];var _t27=i?null:r.firstChild;return r.insertBefore(e,_t27)}}catch(e){}}function ae(e,t){var n=null;return new s.U9(function(i){var _r13=function r(){e.removeEventListener("load",_r13),e.removeEventListener("error",
_r13),null!=n&&window.clearTimeout(n),i()};e.addEventListener("load",_r13),e.addEventListener("error",_r13),null!=t&&(n=window.setTimeout(_r13,t))})}function de(e,t,n){var i=new Image;t&&B.isFn(t)&&ae(i,n).then(t),i.src=e}function ce(e){if(!e)return;var t=Q();var n;B.insertElement(t,document,"body"),(n=t.contentWindow.document).open(),n.write(e),n.close()}function ue(e,t,n){var i=B.createTrackPixelIframeHtml(e,!1,"allow-scripts allow-same-origin"),r=document.createElement("div");r.innerHTML=i;var o=
r.firstChild;t&&B.isFn(t)&&ae(o,n).then(t),B.insertElement(o,document,"html",!0)}function le(e,t,n){return n.indexOf(e)===t}function fe(e,t){return e.concat(t)}function ge(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:w.adUnits).map(function(e){return e.bids.map(function(e){return e.bidder}).reduce(fe,[])}).reduce(fe,[]).filter(function(e){return void 0!==e}).filter(le)}function pe(){if(window.googletag&&X(window.googletag.pubads)&&X(window.googletag.pubads().getSlots))return!0}
function he(){if(window.apntag&&X(window.apntag.getTag))return!0}var me=function me(e,t){return t.cpm-e.cpm};function be(e){var t=e.length;for(;t>0;){var _n23=Math.floor(Math.random()*t);t--;var _i20=e[t];e[t]=e[_n23],e[_n23]=_i20}return e}function ye(e){return(0,r.Q)(e)||{}}function ve(){try{return B.getWindowSelf()!==B.getWindowTop()}catch(e){return!0}}function Ee(){return/^((?!chrome|android|crios|fxios).)*safari/i.test(navigator.userAgent)}function Ae(e,t){if(e)return Object.entries(t).reduce(function(e,
t){var _t28=_slicedToArray(t,2),n=_t28[0],i=_t28[1];return e.replace(new RegExp("\\$\\{"+n+"\\}","g"),i||"")},e)}function we(e,t){return Ae(e,{AUCTION_PRICE:t})}function Te(){return(new Date).getTime()}function Ie(){return window.performance&&window.performance.now&&window.performance.now()||0}function Ce(){return!1!==i.$W.getConfig("deviceAccess")}function Be(){if(window.navigator.cookieEnabled||document.cookie.length)return!0}function Oe(e,t){if(t<1)throw new Error("numRequiredCalls must be a positive number. Got ".concat(t));
var n=0;return function(){n++,n===t&&e.apply(this,arguments)}}function Re(e,t){return e.reduce(function(e,n){return(e[n[t]]=e[n[t]]||[]).push(n),e},{})}function Se(e,t){return t.filter(function(t){return e[t]}).reduce(function(t,n){return Object.assign(t,_defineProperty({},n,e[n]))},{})}function ke(e){var t=["banner","native","video"],n=["instream","outstream","adpod"];return!!Object.keys(e).every(function(e){return t.includes(e)})&&(!e.video||!e.video.context||n.includes(e.video.context))}function Ue(e,
t,n){return e.filter(function(e){return e.code===t}).flatMap(function(e){return e.bids}).filter(function(e){return e.bidder===n}).map(function(e){return e.params||{}})}function De(){return"1"===navigator.doNotTrack||"1"===window.doNotTrack||"1"===navigator.msDoNotTrack||"yes"===navigator.doNotTrack}var _e=function _e(e,t){return e.getAdUnitPath()===t||e.getSlotElementId()===t};function $e(e){return function(t){return _e(e,t)}}function je(e,t){var n=Object.keys(e.mediaTypes||{banner:"banner"}).join(", ");
return"\n    ".concat(e.code," is a ").concat(n," ad unit\n    containing bidders that don't support ").concat(n,": ").concat(t,".\n    This bidder won't fetch demand.\n  ")}var qe=Number.isInteger.bind(Number);function xe(e,t){return"object"!=_typeof(e)?{}:t.reduce(function(n,i,r){if("function"==typeof i)return n;var o=i,s=i.match(/^(.+?)\sas\s(.+?)$/i);s&&(i=s[1],o=s[2]);var a=e[i];return"function"==typeof t[r+1]&&(a=t[r+1](a,n)),void 0!==a&&(n[o]=a),n},{})}function Ne(e,t){return ee(e)&&(!t||e.length===
t)&&e.every(function(e){return qe(e)})}function We(e){return e?e.replace(/^\?/,"").split("\x26").reduce(function(e,t){var _t$split=t.split("\x3d"),_t$split2=_slicedToArray(_t$split,2),n=_t$split2[0],i=_t$split2[1];return/\[\]$/.test(n)?(n=n.replace("[]",""),e[n]=e[n]||[],e[n].push(i)):e[n]=i||"",e},{}):{}}function Pe(e,t){var n=document.createElement("a");t&&"noDecodeWholeURL"in t&&t.noDecodeWholeURL?n.href=e:n.href=decodeURIComponent(e);var i=t&&"decodeSearchAsString"in t&&t.decodeSearchAsString;
return{href:n.href,protocol:(n.protocol||"").replace(/:$/,""),hostname:n.hostname,port:+n.port,pathname:n.pathname.replace(/^(?!\/)/,"/"),search:i?n.search:B.parseQS(n.search||""),hash:(n.hash||"").replace(/^#/,""),host:n.host||window.location.host}}function Ve(e){return(e.protocol||"http")+"://"+(e.host||e.hostname+(e.port?":".concat(e.port):""))+(e.pathname||"")+(e.search?"?".concat(B.formatQS(e.search||"")):"")+(e.hash?"#".concat(e.hash):"")}function Me(e,t){var _ref47=arguments.length>2&&void 0!==
arguments[2]?arguments[2]:{},_ref47$checkTypes=_ref47.checkTypes,n=_ref47$checkTypes===void 0?!1:_ref47$checkTypes;if(e===t)return!0;if("object"!=_typeof(e)||null===e||"object"!=_typeof(t)||null===t)return!1;var i=Array.isArray(e),r=Array.isArray(t);if(i&&r){if(e.length!==t.length)return!1;for(var _i21=0;_i21<e.length;_i21++)if(!Me(e[_i21],t[_i21],{checkTypes:n}))return!1;return!0}if(i||r)return!1;if(n&&e.constructor!==t.constructor)return!1;var o=Object.keys(e),s=Object.keys(t);if(o.length!==s.length)return!1;
for(var _i22=0,_o1=o;_i22<_o1.length;_i22++){var _i23=_o1[_i22];if(!Object.prototype.hasOwnProperty.call(t,_i23))return!1;if(!Me(e[_i23],t[_i23],{checkTypes:n}))return!1}return!0}function Ge(e){for(var _t29=0;_t29<(arguments.length<=1?0:arguments.length-1);_t29++){var _n24=_t29+1<1||arguments.length<=_t29+1?void 0:arguments[_t29+1];ne(_n24)&&Fe(e,_n24)}return e}function Fe(e,t){if(!ne(e)||!ne(t))return;var n=Object.keys(t);var _loop3=function _loop3(){var r=n[_i24];if("__proto__"===r||"constructor"===
r)return 1;var o=t[r];ne(o)?(e[r]||(e[r]={}),Fe(e[r],o)):Array.isArray(o)?Array.isArray(e[r])?o.forEach(function(t){e[r].some(function(e){return Me(e,t)})||e[r].push(t)}):e[r]=_toConsumableArray(o):e[r]=o};for(var _i24=0;_i24<n.length;_i24++)if(_loop3())continue}function He(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=function n(e,t){if(X(Math.imul))return Math.imul(e,t);var n=(4194303&e)*(t|=0);return 4290772992&e&&(n+=(4290772992&e)*t|0),0|n},i=3735928559^t,r=1103547991^t;
for(var _t30,_o10=0;_o10<e.length;_o10++)_t30=e.charCodeAt(_o10),i=n(i^_t30,2654435761),r=n(r^_t30,1597334677);return i=n(i^i>>>16,2246822507)^n(r^r>>>13,3266489909),r=n(r^r>>>16,2246822507)^n(i^i>>>13,3266489909),(4294967296*(2097151&r)+(i>>>0)).toString()}function Le(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return e};var n=new Map,i=function i(){var i=t.apply(this,arguments);return n.has(i)||n.set(i,e.apply(this,arguments)),n.get(i)};return i.clear=n.clear.bind(n),
i}function ze(e,t){Object.entries(t).forEach(function(t){var _t31=_slicedToArray(t,2),n=_t31[0],i=_t31[1];return e.setAttribute(n,i)})}function Je(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){return e},i=0,r=e.length&&e.length-1;var o=n(t);for(;r-i>1;){var _t32=i+Math.round((r-i)/2);o>n(e[_t32])?i=_t32:r=_t32}for(;e.length>i&&o>n(e[i]);)i++;return i}function Qe(e,t){for(var _n25,_i25=0;_i25<e.length;_i25++)if(_n25=(0,d.A)(e[_i25],t),_n25)return _n25}var Ke=function(){var e;
return function(){if(void 0!==e)return e;try{void 0===window.CompressionStream?e=!1:(new window.CompressionStream("gzip"),e=!0)}catch(t){e=!1}return e}}();function Ye(_x){return _Ye.apply(this,arguments)}function _Ye(){_Ye=_asyncToGenerator(_regenerator().m(function _callee(e){var t,n,i,r;return _regenerator().w(function(_context){while(1)switch(_context.n){case 0:"string"!=typeof e&&(e=JSON.stringify(e));t=(new TextEncoder).encode(e);n=(new Blob([t])).stream().pipeThrough(new window.CompressionStream("gzip"));
_context.n=1;return(new Response(n)).blob();case 1:i=_context.v;_context.n=2;return i.arrayBuffer();case 2:r=_context.v;return _context.a(2,new Uint8Array(r))}},_callee)}));return _Ye.apply(this,arguments)}},6894:function _(e,t,n){n.d(t,{Ak:function Ak(){return m},BO:function BO(){return g},K7:function K7(){return p},NL:function NL(){return b}});var i=n(3272);var r="performanceMetrics",o=window.performance&&window.performance.now?function(){return window.performance.now()}:function(){return Date.now()},
s=new WeakMap;function a(){var _ref48=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_ref48$now=_ref48.now,e=_ref48$now===void 0?o:_ref48$now,_ref48$mkNode=_ref48.mkNode,t=_ref48$mkNode===void 0?u:_ref48$mkNode,_ref48$mkTimer=_ref48.mkTimer,n=_ref48$mkTimer===void 0?c:_ref48$mkTimer,_ref48$mkRenamer=_ref48.mkRenamer,i=_ref48$mkRenamer===void 0?function(e){return e}:_ref48$mkRenamer,_ref48$nodes=_ref48.nodes,r=_ref48$nodes===void 0?s:_ref48$nodes;return function(){return function o(s){var a=
arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return{forEach:function forEach(t){t(e)}}};a=i(a);var d=(c="timestamps",function(e){return s.dfWalk({visit:function visit(t,n){var i=n[c];if(i.hasOwnProperty(e))return i[e]}})});var c;function u(e,t){var n=a(e);s.dfWalk({follow:function follow(e,t){return t.propagate&&(!e||!e.stopPropagation)},visit:function visit(e,i){n.forEach(function(n){null==e?i.metrics[n]=t:(i.groups.hasOwnProperty(n)||(i.groups[n]=[]),i.groups[n].push(t))})}})}
function l(t){return n(e,function(e){return u(t,e)})}function f(){var e={};return s.dfWalk({visit:function visit(t,n){e=Object.assign({},!t||t.includeGroups?n.groups:null,n.metrics,e)}}),e}var g={startTiming:l,measureTime:function measureTime(e,t){return l(e).stopAfter(t)()},measureHookTime:function measureHookTime(e,t,n){var i=l(e);return n(function(e){var t=i.stopBefore(e);return t.bail=e.bail&&i.stopBefore(e.bail),t.stopTiming=i,t.untimed=e,t}(t))},checkpoint:function checkpoint(t){s.timestamps[t]=
e()},timeSince:function timeSince(t,n){var i=d(t),r=null!=i?e()-i:null;return null!=n&&u(n,r),r},timeBetween:function timeBetween(e,t,n){var i=d(e),r=d(t),o=null!=i&&null!=r?r-i:null;return null!=n&&u(n,o),o},setMetric:u,getMetrics:f,fork:function fork(){var _ref49=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_ref49$propagate=_ref49.propagate,e=_ref49$propagate===void 0?!0:_ref49$propagate,_ref49$stopPropagatio=_ref49.stopPropagation,n=_ref49$stopPropagatio===void 0?!1:_ref49$stopPropagatio,
_ref49$includeGroups=_ref49.includeGroups,i=_ref49$includeGroups===void 0?!1:_ref49$includeGroups;return o(t([[s,{propagate:e,stopPropagation:n,includeGroups:i}]]),a)},join:function join(e){var _ref50=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},_ref50$propagate=_ref50.propagate,t=_ref50$propagate===void 0?!0:_ref50$propagate,_ref50$stopPropagatio=_ref50.stopPropagation,n=_ref50$stopPropagatio===void 0?!1:_ref50$stopPropagatio,_ref50$includeGroups=_ref50.includeGroups,i=_ref50$includeGroups===
void 0?!1:_ref50$includeGroups;var o=r.get(e);null!=o&&o.addParent(s,{propagate:t,stopPropagation:n,includeGroups:i})},newMetrics:function newMetrics(){return o(s.newSibling(),a)},renameWith:function renameWith(e){return o(s,e)},toJSON:function toJSON(){return f()}};return r.set(g,s),g}(t([]))}}function d(e,t,n){return function(){t&&t();try{return e.apply(this,arguments)}finally{n&&n()}}}function c(e,t){var n=e();var i=!1;function r(){i||(t(e()-n),i=!0)}return r.stopBefore=function(e){return d(e,
r)},r.stopAfter=function(e){return d(e,null,r)},r}function u(e){return{metrics:{},timestamps:{},groups:{},addParent:function addParent(t,n){e.push([t,n])},newSibling:function newSibling(){return u(e.slice())},dfWalk:function dfWalk(){var t,_ref51=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=_ref51.visit,_ref51$follow=_ref51.follow,i=_ref51$follow===void 0?function(){return!0}:_ref51$follow,_ref51$visited=_ref51.visited,r=_ref51$visited===void 0?new Set:_ref51$visited,o=_ref51.inEdge;
if(!r.has(this)){if(r.add(this),t=n(o,this),null!=t)return t;var _iterator0=_createForOfIteratorHelper(e),_step0;try{for(_iterator0.s();!(_step0=_iterator0.n()).done;){var _step0$value=_slicedToArray(_step0.value,2),_s13=_step0$value[0],_a10=_step0$value[1];if(i(o,_a10)&&(t=_s13.dfWalk({visit:n,follow:i,visited:r,inEdge:_a10}),null!=t))return t}}catch(err){_iterator0.e(err)}finally{_iterator0.f()}}}}}var l=function(){var e=function e(){},t=function t(){return{}},n={forEach:e},i=function i(){return null};
i.stopBefore=function(e){return e},i.stopAfter=function(e){return e};var r=Object.defineProperties({dfWalk:e,newSibling:function newSibling(){return r},addParent:e},Object.fromEntries(["metrics","timestamps","groups"].map(function(e){return[e,{get:t}]})));return a({now:function now(){return 0},mkNode:function mkNode(){return r},mkRenamer:function mkRenamer(){return function(){return n}},mkTimer:function mkTimer(){return i},nodes:{get:e,set:e}})()}();var f=!0;function g(e){return f&&e||l}i.$W.getConfig(r,
function(e){f=!!e[r]});var p=function(){var e=a();return function(){return f?e():l}}();function h(e,t){return function(n,i){return function(r){for(var o=arguments.length,s=new Array(o>1?o-1:0),a=1;a<o;a++)s[a-1]=arguments[a];var d=this;return g(t.apply(d,s)).measureHookTime(e+n,r,function(e){return i.call.apply(i,[d,e].concat(s))})}}}var m=h("requestBids.",function(e){return e.metrics}),b=h("addBidResponse.",function(e,t){return t.metrics})},5555:function _(e,t,n){var _setTimeout,_Promise;n.d(t,{U9:function U9(){return o},
v6:function v6(){return s}});var i=n(43),r=n(7873);(_setTimeout=(0,r.m)().setTimeout)!==null&&_setTimeout!==void 0?_setTimeout:i.w;var o=(_Promise=(0,r.m)().Promise)!==null&&_Promise!==void 0?_Promise:i.k;function s(){var e,t,_ref52=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_ref52$promiseFactory=_ref52.promiseFactory,n=_ref52$promiseFactory===void 0?function(e){return new o(e)}:_ref52$promiseFactory;function i(e){return function(t){return e(t)}}return{promise:n(function(n,i){e=n,t=
i}),resolve:i(e),reject:i(t)}}},2713:function _(e,t,n){function i(e,t){return e===t?0:e<t?-1:1}function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return function(t,n){return i(e(t),e(n))}}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;return function(t,n){return-e(t,n)||0}}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n){for(var _i26=0,_t33=t;_i26<_t33.length;_i26++){var _i27=
_t33[_i26];var _t34=_i27(e,n);if(0!==_t34)return _t34}return 0}}function a(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;return function(t,n){return e(n,t)<0?n:t}}(o(arguments.length>0&&void 0!==arguments[0]?arguments[0]:i))}n.d(t,{Bq:function Bq(){return l},Vk:function Vk(){return u}});var d=r(function(e){return e.cpm}),c=r(function(e){return e.responseTimestamp}),u=a(s(d,o(r(function(e){return e.timeToRespond})))),l=a(s(d,o(c)));a(s(d,c))},6853:function _(e,
t,n){n.d(t,{H:function H(){return u}});var i=n(5555),r=n(1069);var o=null,s=0,a=[];function d(){document.hidden?o=Date.now():(s+=Date.now()-(o!==null&&o!==void 0?o:0),o=null,a.forEach(function(e){var t=e.callback,n=e.startTime,i=e.setTimerId;return i(c(t,s-n)())}),a=[])}function c(e,t){var n=s;var i=setTimeout(function(){s===n&&null==o?e():null!=o?a.push({callback:e,startTime:n,setTimerId:function setTimerId(e){i=e}}):i=c(e,s-n)()},t);return function(){return i}}function u(){var _ref53=arguments.length>
0&&void 0!==arguments[0]?arguments[0]:{},_ref53$startTime=_ref53.startTime,e=_ref53$startTime===void 0?r.vE:_ref53$startTime,_ref53$ttl=_ref53.ttl,t=_ref53$ttl===void 0?function(){return null}:_ref53$ttl,_ref53$monotonic=_ref53.monotonic,n=_ref53$monotonic===void 0?!1:_ref53$monotonic,_ref53$slack=_ref53.slack,o=_ref53$slack===void 0?5E3:_ref53$slack;var s=new Map,a=[],d=[],u=n?function(e){return d.push(e)}:function(e){return d.splice((0,r.El)(d,e,function(e){return e.expiry}),0,e)};var l,f;function g(){if(f&&
clearTimeout(f),d.length>0){var _e61=(0,r.vE)();l=Math.max(_e61,d[0].expiry+o),f=c(function(){var e=(0,r.vE)();var t=0;var _iterator1=_createForOfIteratorHelper(d),_step1;try{var _loop4=function _loop4(){var n=_step1.value;if(n.expiry>e)return 1;a.forEach(function(e){try{e(n.item)}catch(e){(0,r.vV)(e)}}),s["delete"](n.item),t++};for(_iterator1.s();!(_step1=_iterator1.n()).done;)if(_loop4())break}catch(err){_iterator1.e(err)}finally{_iterator1.f()}d.splice(0,t),f=null,g()},l-_e61)}else f=null}function p(n){var r=
{},s=h;var a;var _Object$entries$map=Object.entries({start:e,delta:t}).map(function(e){var t,_e62=_slicedToArray(e,2),d=_e62[0],c=_e62[1];return function(){var e=t={};i.U9.resolve(c(n)).then(function(n){e===t&&(r[d]=n,s===h&&null!=r.start&&null!=r.delta&&(a=r.start+r.delta,u(p),(null==f||l>a+o)&&g()))})}}),_Object$entries$map2=_slicedToArray(_Object$entries$map,2),d=_Object$entries$map2[0],c=_Object$entries$map2[1],p={item:n,refresh:c,get expiry(){return a}};return d(),c(),p}var h={};return _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({},
Symbol.iterator,function(){return s.keys()}),"add",function add(e){!s.has(e)&&s.set(e,p(e))}),"clear",function clear(){d.length=0,g(),s.clear(),h={}}),"toArray",function toArray(){return Array.from(s.keys())}),"refresh",function refresh(){d.length=0,g();var _iterator10=_createForOfIteratorHelper(s.values()),_step10;try{for(_iterator10.s();!(_step10=_iterator10.n()).done;){var _e63=_step10.value;_e63.refresh()}}catch(err){_iterator10.e(err)}finally{_iterator10.f()}}),"onExpiry",function onExpiry(e){return a.push(e),
function(){var t=a.indexOf(e);t>=0&&a.splice(t,1)}})}document.addEventListener("visibilitychange",d)},3895:function _(e,t,n){n.d(t,{H6:function H6(){return a},V0:function V0(){return c},Zy:function Zy(){return d},aP:function aP(){return u},vk:function vk(){return l}});var i=n(1069),r=n(3272),o=n(9214),s=n(6881);var a="outstream",d=new Map([["mimes",function(e){return Array.isArray(e)&&e.length>0&&e.every(function(e){return"string"==typeof e})}],["minduration",i.Fq],["maxduration",i.Fq],["startdelay",
i.Fq],["maxseq",i.Fq],["poddur",i.Fq],["protocols",i.Uu],["w",i.Fq],["h",i.Fq],["podid",i.O8],["podseq",i.Fq],["rqddurs",i.Uu],["placement",i.Fq],["plcmt",i.Fq],["linearity",i.Fq],["skip",function(e){return[1,0].includes(e)}],["skipmin",i.Fq],["skipafter",i.Fq],["sequence",i.Fq],["slotinpod",i.Fq],["mincpmpersec",i.Et],["battr",i.Uu],["maxextended",i.Fq],["minbitrate",i.Fq],["maxbitrate",i.Fq],["boxingallowed",i.Fq],["playbackmethod",i.Uu],["playbackend",i.Fq],["delivery",i.Uu],["pos",i.Fq],["api",
i.Uu],["companiontype",i.Uu],["poddedupe",i.Uu]]);function c(e){var _e$mediaTypes3;var t=e===null||e===void 0||(_e$mediaTypes3=e.mediaTypes)===null||_e$mediaTypes3===void 0?void 0:_e$mediaTypes3.video;null!=t&&null==t.plcmt&&(t.context===a||[2,3,4].includes(t.placement)?t.plcmt=4:t.context!==a&&[2,6].includes(t.playbackmethod)&&(t.plcmt=2))}function u(e,t){var _e$mediaTypes4;var n=e===null||e===void 0||(_e$mediaTypes4=e.mediaTypes)===null||_e$mediaTypes4===void 0?void 0:_e$mediaTypes4.video;(0,i.Qd)(n)?
null!=n&&Object.entries(n).forEach(function(r){var _r14=_slicedToArray(r,2),o=_r14[0],s=_r14[1];if(!d.has(o))return;d.get(o)(s)||("function"==typeof t?t(o,s,e):(delete n[o],(0,i.JE)('Invalid prop in adUnit "'.concat(e.code,'": Invalid value for mediaTypes.video.').concat(o," ORTB property. The property has been removed."))))}):(0,i.JE)("validateOrtbVideoFields: videoParams must be an object.")}function l(e){var _t$getMediaTypes;var _ref55=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},
_ref55$index=_ref55.index,t=_ref55$index===void 0?s.n.index:_ref55$index;var n=(_t$getMediaTypes=t.getMediaTypes(e))===null||_t$getMediaTypes===void 0?void 0:_t$getMediaTypes.video,i=n&&(n===null||n===void 0?void 0:n.context),r=n&&(n===null||n===void 0?void 0:n.useCacheKey),o=t.getAdUnit(e);return f(e,o,n,i,r)}var f=(0,o.A_)("sync",function(e,t,n,o,s){if(n&&(s||o!==a)){var _ref56=r.$W.getConfig("cache")||{},_t35=_ref56.url,_n26=_ref56.useLocal;return _t35||_n26||!e.vastXml||e.vastUrl?!(!e.vastUrl&&
!e.vastXml):((0,i.vV)('\n        This bid contains only vastXml and will not work when a prebid cache url is not specified.\n        Try enabling either prebid cache with pbjs.setConfig({ cache: {url: "..."} });\n        or local cache with pbjs.setConfig({ cache: { useLocal: true }});\n      '),!1)}return!(o===a&&!s)||!!(e.renderer||t&&t.renderer||n.renderer)},"checkVideoBidSetup")}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[769],{8944:function _(t,e,n){n.d(e,{M:function M(){return i}});var c=
n(1069);function i(){var t=(0,c.Ot)();try{var _e64=t.innerHeight||t.document.documentElement.clientHeight||t.document.body.clientHeight||0;return{width:t.innerWidth||t.document.documentElement.clientWidth||t.document.body.clientWidth||0,height:_e64}}catch(t){return{}}}}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[139],{43:function _(t,e,n){n.d(e,{k:function k(){return c},w:function w(){return r}});var s=0,l=1;var _t36=new WeakMap;var _e65=new WeakMap;var c=function(){function c(t){_classCallCheck(this,
c);_classPrivateFieldInitSpec(this,_t36,void 0);_classPrivateFieldInitSpec(this,_e65,void 0);if("function"!=typeof t)throw new Error("resolver not a function");var e=[],n=[];var _map5=[s,l].map(function(t){return function(l){if(t===s&&"function"==typeof(l===null||l===void 0?void 0:l.then))l.then(_c4,r);else if(!e.length)for(e.push(t,l);n.length;)n.shift()()}}),_map6=_slicedToArray(_map5,2),_c4=_map6[0],r=_map6[1];try{t(_c4,r)}catch(t){r(t)}_classPrivateFieldSet(_t36,this,e),_classPrivateFieldSet(_e65,
this,n)}return _createClass(c,[{key:"then",value:function then(t,e){var _this0=this;var n=_classPrivateFieldGet(_t36,this);return new this.constructor(function(l,_c5){var r=function r(){var r=n[1],_ref57=n[0]===s?[t,l]:[e,_c5],_ref58=_slicedToArray(_ref57,2),h=_ref58[0],i=_ref58[1];if("function"==typeof h){try{r=h(r)}catch(t){return void _c5(t)}i=l}i(r)};n.length?r():_classPrivateFieldGet(_e65,_this0).push(r)})}},{key:"catch",value:function _catch(t){return this.then(null,t)}},{key:"finally",value:function _finally(t){var _this1=
this;var e;return this.then(function(n){return e=n,t()},function(n){return e=_this1.constructor.reject(n),t()}).then(function(){return e})}}],[{key:"race",value:function race(t){var _this10=this;return new this(function(e,n){_assertClassBrand(c,_this10,_n27).call(_this10,t,function(t,s){return t?e(s):n(s)})})}},{key:"all",value:function all(t){var _this11=this;return new this(function(e,n){var s=[];_assertClassBrand(c,_this11,_n27).call(_this11,t,function(t,e,l){return t?s[l]=e:n(e)},function(){return e(s)})})}},
{key:"allSettled",value:function allSettled(t){var _this12=this;return new this(function(e){var n=[];_assertClassBrand(c,_this12,_n27).call(_this12,t,function(t,e,s){return n[s]=t?{status:"fulfilled",value:e}:{status:"rejected",reason:e}},function(){return e(n)})})}},{key:"resolve",value:function resolve(t){return new this(function(e){return e(t)})}},{key:"reject",value:function reject(t){return new this(function(e,n){return n(t)})}}])}();function _n27(t,e,n){var _this13=this;var s=t.length;function l(){e.apply(this,
arguments),--s<=0&&n&&n()}0===t.length&&n?n():t.forEach(function(t,e){return _this13.resolve(t).then(function(t){return l(!0,t,e)},function(t){return l(!1,t,e)})})}function r(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e>0)return setTimeout(t,e);t()}}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[85],{4595:function _(e,t,n){n.d(t,{G:function G(){return s}});var s='(()\x3d\x3e{"use strict";window.render\x3dfunction({ad:e,adUrl:t,width:n,height:i,instl:d},{mkFrame:r},s){if(!e\x26\x26!t)throw{reason:"noAd",message:"Missing ad markup or URL"};{if(null\x3d\x3di){const e\x3ds.document?.body;[e,e?.parentElement].filter((e\x3d\x3enull!\x3de?.style)).forEach((e\x3d\x3ee.style.height\x3d"100%"))}const h\x3ds.document,o\x3d{width:n??"100%",height:i??"100%"};if(t\x26\x26!e?o.src\x3dt:o.srcdoc\x3de,h.body.appendChild(r(h,o)),d\x26\x26s.frameElement){const e\x3ds.frameElement.style;e.width\x3dn?`${n}px`:"100vw",e.height\x3di?`${i}px`:"100vh"}}}})();'}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[147],{9495:function _(e,r,n){function u(e){var _e$ortb;return e===null||e===void 0||(_e$ortb=e.ortb2)===null||_e$ortb===void 0||(_e$ortb=_e$ortb.ext)===null||_e$ortb===void 0||(_e$ortb=_e$ortb.prebid)===null||_e$ortb===void 0?void 0:_e$ortb.adServerCurrency}n.d(r,{b:function b(){return u}})}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[109],{965:function _(t,e,n){n.d(e,{c5:function c5(){return o},q4:function q4(){return r}});var a=n(5555);var c=0,l=1,r=
2;function o(t){var e=t.apiName,n=t.apiVersion,_t$apiArgs=t.apiArgs,o=_t$apiArgs===void 0?["command","callback","parameter","version"]:_t$apiArgs,_t$callbackArgs=t.callbackArgs,s=_t$callbackArgs===void 0?["returnValue","success"]:_t$callbackArgs,_t$mode=t.mode,i=_t$mode===void 0?c:_t$mode,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;var f={},d="".concat(e,"Call"),p="".concat(e,"Return");function b(t){var _e$p;var e="string"==typeof t.data&&t.data.includes(p)?JSON.parse(t.data):
t.data;if(e!==null&&e!==void 0&&(_e$p=e[p])!==null&&_e$p!==void 0&&_e$p.callId){var _t37=e[p];f.hasOwnProperty(_t37.callId)&&f[_t37.callId].apply(f,_toConsumableArray(s.map(function(e){return _t37[e]})))}}var _ref59=function(){var t,n=u,a=!1;for(;null!=n;){try{if("function"==typeof n[e]){t=n,a=!0;break}}catch(t){}try{if(n.frames["".concat(e,"Locator")]){t=n;break}}catch(t){}if(n===u.top)break;n=n.parent}return[t,a]}(),_ref60=_slicedToArray(_ref59,2),k=_ref60[0],m=_ref60[1];if(!k)return;function g(t){return t=
Object.assign({version:n},t),o.map(function(e){return[e,t[e]]})}function h(t,e,n,a){var c="function"==typeof t;return function(r,o){if(a&&a(),i!==l)(null==o||o?e:n)(c?void 0:r);c&&t.apply(this,arguments)}}var v;return m?v=function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new a.U9(function(n,a){var o=k[e].apply(k,_toConsumableArray(g(_objectSpread(_objectSpread({},t),{},{callback:t.callback||i===r?h(t.callback,n,a):void 0})).map(function(t){var _t38=_slicedToArray(t,
2),e=_t38[0],n=_t38[1];return n})));(i===l||null==t.callback&&i===c)&&n(o)})}:(u.addEventListener("message",b,!1),v=function v(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new a.U9(function(n,a){var c=Math.random().toString(),r=_defineProperty({},d,_objectSpread(_objectSpread({},Object.fromEntries(g(t).filter(function(t){var _t39=_slicedToArray(t,1),e=_t39[0];return"callback"!==e}))),{},{callId:c}));f[c]=h(t===null||t===void 0?void 0:t.callback,n,a,(e||null==(t===null||
t===void 0?void 0:t.callback))&&function(){delete f[c]}),k.postMessage(r,"*"),i===l&&n()})}),Object.assign(v,{isDirect:m,close:function close(){!m&&u.removeEventListener("message",b)}})}}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[10],{3806:function _(t,n,e){e.d(n,{yq:function yq(){return c}});var o=e(6894),a=e(1069),s=e(7873),i=e(5555),r=e(2604);function c(){var t,n,e,_ref61=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},c=_ref61.namespace,u=_ref61.displayName,l=_ref61.consentDataHandler,
m=_ref61.parseConsentData,d=_ref61.getNullConsent,f=_ref61.cmpHandlers,_ref61$DEFAULT_CMP=_ref61.DEFAULT_CMP,g=_ref61$DEFAULT_CMP===void 0?"iab":_ref61$DEFAULT_CMP,_ref61$DEFAULT_CONSEN=_ref61.DEFAULT_CONSENT_TIMEOUT,p=_ref61$DEFAULT_CONSEN===void 0?1E4:_ref61$DEFAULT_CONSEN;function C(t){return"consentManagement.".concat(c," ").concat(t)}function D(t,n){return t(Object.assign(_defineProperty({},"".concat(c,"Consent"),l.getConsentData()),n))}function h(){return n().then(function(t){var n=t.error;
return{error:n,consentData:l.getConsentData()}})}function T(){null==t&&(t=function(t,n){var e=new WeakSet;return(0,o.Ak)(t,function(t,o){var _this14=this;return n().then(function(n){var s=n.consentData,i=n.error;!i||s&&e.has(i)||(e.add(i),(0,a.JE).apply(void 0,[i.message].concat(_toConsumableArray(i.args||[])))),t.call(_this14,o)})["catch"](function(n){(0,a.vV).apply(void 0,["".concat(n===null||n===void 0?void 0:n.message," Canceling auction as per consentManagement config.")].concat(_toConsumableArray((n===
null||n===void 0?void 0:n.args)||[]))),t.stopTiming(),"function"==typeof o.bidsBackHandler?o.bidsBackHandler():(0,a.vV)("Error executing bidsBackHandler")})})}(c,function(){return n()}),(0,s.m)().requestBids.before(t,50),r.U3.before(D),(0,a.fH)("".concat(u," consentManagement module has been activated...")))}return function(o){var _o11;if(o=(_o11=o)===null||_o11===void 0?void 0:_o11[c],!o||"object"!=_typeof(o))return(0,a.JE)(C("config not defined, exiting consent manager module")),null!=t&&((0,s.m)().requestBids.getHooks({hook:t}).remove(),
r.U3.getHooks({hook:D}).remove(),t=null),{};var b,k;(0,a.O8)(o.cmpApi)?b=o.cmpApi:(b=g,(0,a.fH)(C("config did not specify cmp.  Using system default setting (".concat(g,").")))),(0,a.Et)(o.timeout)?k=o.timeout:(k=p,(0,a.fH)(C("config did not specify timeout.  Using system default setting (".concat(p,")."))));var H=(0,a.Et)(o.actionTimeout)?o.actionTimeout:null;var w;"static"===b?(0,a.Qd)(o.consentData)?(e=o.consentData,k=null,w=function w(){return new i.U9(function(t){return t(l.setConsentData(m(e)))})}):
(0,a.vV)(C("config with cmpApi: 'static' did not specify consentData. No consents will be available to adapters.")):f.hasOwnProperty(b)?w=f[b]:(l.setConsentData(null),(0,a.JE)("".concat(u," CMP framework (").concat(b,") is not a supported framework.  Aborting consentManagement module and resuming auction.")),w=function w(){return i.U9.resolve()});var y=function y(){return function(t){var n,e=t.name,o=t.consentDataHandler,a=t.setupCmp,s=t.cmpTimeout,i=t.actionTimeout,r=t.getNullConsent;return o.enable(),
(new Promise(function(t,c){var u,l=!1;function m(a){null!=n&&clearTimeout(n),n=null!=a?setTimeout(function(){var _o$getConsentData;var n=(_o$getConsentData=o.getConsentData())!==null&&_o$getConsentData!==void 0?_o$getConsentData:l?u:r(),a="timeout waiting for "+(l?"user action on CMP":"CMP to load");o.setConsentData(n),t({consentData:n,error:new Error("".concat(e," ").concat(a))})},a):null}a(function(t){u=t,l||(l=!0,null!=i&&m(i))}).then(function(){return t({consentData:o.getConsentData()})},c),null!=
s&&m(s)}))["finally"](function(){n&&clearTimeout(n)})["catch"](function(t){throw o.setConsentData(null),t;})}({name:u,consentDataHandler:l,setupCmp:w,cmpTimeout:k,actionTimeout:H,getNullConsent:d})};return n=function(){var t;return function(){return null==t&&(t=y()["catch"](function(n){throw t=null,n;})),t}}(),T(),{cmpHandler:b,cmpTimeout:k,actionTimeout:H,staticConsentData:e,loadConsentData:h,requestBidsHook:t}}}}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[957],{6400:function _(s,e,t){function n(){var s=
[];return{submit:function submit(e,t,n){var u=[t,setTimeout(function(){s.splice(s.indexOf(u),1),n()},e)];s.push(u)},resume:function resume(){for(;s.length;){var _s$shift=s.shift(),_s$shift2=_slicedToArray(_s$shift,2),_e66=_s$shift2[0],_t40=_s$shift2[1];clearTimeout(_t40),_e66()}}}}t.d(e,{L:function L(){return n}})}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[82],{7274:function _(e,n,t){t.d(n,{l:function l(){return v}});var i=t(5139),o=t(6811),s=t(6916),r=t(1069);function a(e){return null!=e&&0!==
e}function c(e){return["MspaServiceProviderMode","Gpc"].some(function(n){return 1===e[n]})||2===e.PersonalDataConsents||1===e.KnownChildSensitiveDataConsents[0]||1===e.KnownChildSensitiveDataConsents[2]||a(e.KnownChildSensitiveDataConsents[1])||0===e.MspaCoveredTransaction}function l(e,n){return["SensitiveDataProcessingOptOutNotice","SensitiveDataLimitUseNotice"].some(function(t){return e[t]===n})}function u(e){return c(e)||["Sale","Sharing","TargetedAdvertising"].some(function(n){var t=e["".concat(n,
"OptOut")],i=e["".concat(n,"OptOutNotice")];return 1===t||2===i||2===t&&0===i})||2===e.SharingNotice||2===e.SharingOptOut&&0===e.SharingNotice}var f=function(){var e=function(){var e=[6,7,9,10,12,14,16].map(function(e){return--e}),n=Array.from(Array(16).keys()).filter(function(e){return 7!==e}),t=n.filter(function(n){return!e.includes(n)});return Object.fromEntries(Object.entries({1:12,2:16}).map(function(i){var _i28=_slicedToArray(i,2),o=_i28[0],s=_i28[1];var r=function r(e){return e<s};return[o,
{cannotBeInScope:e.filter(r),allExceptGeo:n.filter(r),mustHaveConsent:t.filter(r)}]}))}();return function(n){var _e$n$Version=e[n.Version],t=_e$n$Version.cannotBeInScope,i=_e$n$Version.mustHaveConsent,o=_e$n$Version.allExceptGeo;return u(n)||l(n,2)||t.some(function(e){return a(n.SensitiveDataProcessing[e])})||i.some(function(e){return 1===n.SensitiveDataProcessing[e]})||l(n,0)&&o.some(function(e){return 2===n.SensitiveDataProcessing[e]})}}();var p=_defineProperty(_defineProperty(_defineProperty(_defineProperty({},
o.Ml,u),o.yl,u),o.qX,f),o.hE,function(e){var n=e.SensitiveDataProcessing[7];return 1===n||c(e)||l(e,2)||l(e,0)&&2===n});function v(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){return e},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:p,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:i.qB,c=arguments.length>5&&void 0!==arguments[5]?arguments[5]:function(){return s.ad.getConsentData()};var l=[],u="MSPA (GPP '".concat(e,"' for section").concat(n.length>
1?"s":""," ").concat(n.join(", "),")");return(0,r.fH)("Enabling activity controls for ".concat(u)),Object.entries(o).forEach(function(i){var _i29=_slicedToArray(i,2),o=_i29[0],r=_i29[1];l.push(a(o,u,function(e,n,t){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:function(){var _s$ad$getConsentData;return(_s$ad$getConsentData=s.ad.getConsentData())===null||_s$ad$getConsentData===void 0?void 0:_s$ad$getConsentData.applicableSections};return function(){if(i().some(function(n){return e.includes(n)})){var _e67=
n();if(null==_e67)return{allow:!1,reason:"consent data not available"};if(![1,2].includes(_e67.Version))return{allow:!1,reason:'unsupported consent specification version "'.concat(_e67.Version,'"')};if(t(_e67))return{allow:!1}}}}(n,function(){var _c6;return t((n=(_c6=c())===null||_c6===void 0||(_c6=_c6.parsedSections)===null||_c6===void 0?void 0:_c6[e],Array.isArray(n)?n.reduceRight(function(e,n){return Object.assign(n,e)},{}):n));var n},r,function(){var _c7;return((_c7=c())===null||_c7===void 0?
void 0:_c7.applicableSections)||[]})))}),function(){return l.forEach(function(e){return e()})}}}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[5],{1252:function _(t,e,n){n.d(e,{Cn:function Cn(){return u},eu:function eu(){return s}});var a=n(3858),l=n(433),r=n(1069);var u=["IAB_AUDIENCE_1_1","IAB_CONTENT_2_2"];function s(t){return Object.entries(_defineProperty(_defineProperty({},u[0],f(t,["user.data"],4)),u[1],f(t,a.Dy.map(function(t){return"".concat(t,".content.data")}),6))).map(function(t){var _t41=
_slicedToArray(t,2),e=_t41[0],n=_t41[1];return n.length?{taxonomy:e,values:n}:null}).filter(function(t){return t})}function f(t,e,n){return e.flatMap(function(e){return(0,l.A)(t,e)||[]}).filter(function(t){var _t$ext;return((_t$ext=t.ext)===null||_t$ext===void 0?void 0:_t$ext.segtax)===n}).flatMap(function(t){var _t$segment;return(_t$segment=t.segment)===null||_t$segment===void 0?void 0:_t$segment.map(function(t){return t.id})}).filter(function(t){return t}).filter(r.hj)}}}]);(self.pbjsChunk=self.pbjsChunk||
[]).push([[931],{9192:function _(e,i,r){r.d(i,{xv:function xv(){return A},Qz:function Qz(){return T}});var t=r(433),a=r(1069),c=r(1371);function s(e,i){return(0,a.ZU)(e,i)}var n=55,A={NATIVE:{IMAGE_TYPE:{ICON:1,MAIN:3},ASSET_ID:{TITLE:1,IMAGE:2,ICON:3,BODY:4,SPONSORED:5,CTA:6},DATA_ASSET_TYPE:{SPONSORED:1,DESC:2,CTA_TEXT:12}}};function T(e){return{requestId:e.impid,mediaType:c.s6,cpm:e.price,creativeId:e.adid||e.crid,width:1,height:1,ttl:n,meta:{advertiserDomains:e.adomain},netRevenue:!0,currency:"USD",
"native":E(s(e.adm,e.price))}}function E(e){try{var _i30=JSON.parse(e)["native"];if(_i30){var _e68={clickUrl:encodeURI(_i30.link.url),impressionTrackers:_i30.imptrackers||_i30.eventtrackers[0].url};return _i30.link.clicktrackers&&(_e68.clickTrackers=_i30.link.clicktrackers[0]),_i30.assets.forEach(function(i){switch(i.id){case A.NATIVE.ASSET_ID.TITLE:_e68.title=(0,t.A)(i,"title.text");break;case A.NATIVE.ASSET_ID.IMAGE:_e68.image={url:encodeURI(i.img.url),width:(0,t.A)(i,"img.w"),height:(0,t.A)(i,
"img.h")};break;case A.NATIVE.ASSET_ID.ICON:_e68.icon={url:encodeURI(i.img.url),width:(0,t.A)(i,"img.w"),height:(0,t.A)(i,"img.h")};break;case A.NATIVE.ASSET_ID.BODY:_e68.body=(0,t.A)(i,"data.value");break;case A.NATIVE.ASSET_ID.SPONSORED:_e68.sponsoredBy=(0,t.A)(i,"data.value");break;case A.NATIVE.ASSET_ID.CTA:_e68.cta=(0,t.A)(i,"data.value")}}),_e68}}catch(e){(0,a.fH)("Error in bidUtils interpretNativeAd"+e)}}}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[107],{5820:function _(o,n,i){function t(){return window.location.origin?
window.location.origin:window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")}i.d(n,{$:function $(){return t}})}}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[332],{7316:function _(e,t,r){var s=r(7873),a=r(9115),i=r(1371),n=r(1069),d=r(3172),o=r(433),p=r(3272),c=r(5789),m=r(9495);var u=p.$W.getConfig,l={code:"adf",aliases:[{code:"adformOpenRTB",gvlid:50},{code:"adform",gvlid:50}],gvlid:50,supportedMediaTypes:[i.s6,i.D4,i.G_],isBidRequestValid:function isBidRequestValid(e){var t=
e.params||{},r=t.mid,s=t.inv,a=t.mname;return!!(r||s&&a)},buildRequests:function buildRequests(e,t){var r,s;var a=t.ortb2||{};var i=a.user||{};"object"==_typeof(u("app"))?(r=u("app")||{},a.app&&(0,n.D9)(r,a.app)):(s=u("site")||{},a.site&&(0,n.D9)(s,a.site),s.page||(s.page=t.refererInfo.page));var p=u("device")||{};a.device&&(0,n.D9)(p,a.device);var _ref62=(0,n.Ot)(),c=_ref62.innerWidth,l=_ref62.innerHeight;p.w=p.w||c,p.h=p.h||l,p.ua=p.ua||navigator.userAgent;var v=a.source||{};v.fd=1;var b=a.regs||
{};var f=(0,n.eY)(e,"params.adxDomain")||"adx.adform.net",g=(0,n.eY)(e,"params.pt")||(0,n.eY)(e,"params.priceType")||"net",h=(0,n.eY)(e,"params.test"),y=(0,m.b)(t),I=y&&[y],x=(0,n.eY)(e,"userIdAsEids"),R=(0,n.eY)(e,"schain");x&&(0,d.J)(i,"ext.eids",x),R&&(0,d.J)(v,"ext.schain",R);var w=e.map(function(e,t){var _e$ortb2Imp5;e.netRevenue=g;var r=e.getFloor?e.getFloor({currency:y||"USD",size:"*",mediaType:"*"}):{},s=r===null||r===void 0?void 0:r.floor,a=r===null||r===void 0?void 0:r.currency,_e$params2=
e.params,i=_e$params2.mid,d=_e$params2.inv,p=_e$params2.mname,c=(_e$ortb2Imp5=e.ortb2Imp)===null||_e$ortb2Imp5===void 0||(_e$ortb2Imp5=_e$ortb2Imp5.ext)===null||_e$ortb2Imp5===void 0?void 0:_e$ortb2Imp5.data,m={id:t+1,tagid:i,bidfloor:s,bidfloorcur:a,ext:{data:c,bidder:{inv:d,mname:p}}};if(e.nativeOrtbRequest&&e.nativeOrtbRequest.assets){var _t42=e.nativeOrtbRequest.assets,_r16=[];for(var _e69=0;_e69<_t42.length;_e69++){var _s14=(0,n.Go)(_t42[_e69]),_a11=_s14.img;if(_a11){var _e70=_a11.ext&&_a11.ext.aspectratios;
if(_e70){var _t43=parseInt(_e70[0].split(":")[0],10),_r17=parseInt(_e70[0].split(":")[1],10);_a11.wmin=_a11.wmin||0,_a11.hmin=_r17*_a11.wmin/_t43|0}}_r16.push(_s14)}m["native"]={request:{assets:_r16}}}var u=(0,o.A)(e,"mediaTypes.banner");if(u&&u.sizes){var _e71=(0,n.kK)(u.sizes).map(function(e){var _e$split=e.split("x"),_e$split2=_slicedToArray(_e$split,2),t=_e$split2[0],r=_e$split2[1];return{w:parseInt(t,10),h:parseInt(r,10)}});m.banner={format:_e71}}var l=(0,o.A)(e,"mediaTypes.video");return l&&
(m.video=l),m}),A={id:t.bidderRequestId,site:s,app:r,user:i,device:p,source:v,ext:{pt:g},cur:I,imp:w,regs:b};return h&&(A.is_debug=!!h,A.test=1),{method:"POST",url:"https://"+f+"/adx/openrtb",data:JSON.stringify(A),bids:e}},interpretResponse:function interpretResponse(e,t){var _ref63;var r=t.bids;if(!e.body)return;var _e$body=e.body,s=_e$body.seatbid,a=_e$body.cur,n=(d=s.map(function(e){return e.bid}),(_ref63=[]).concat.apply(_ref63,_toConsumableArray(d))).reduce(function(e,t){return e[t.impid-1]=
t,e},[]);var d;return r.map(function(e,t){var r=n[t];if(r){var _r$cat,_r$cat2;var _t44=(0,o.A)(r,"ext.prebid.type"),_s15=(0,o.A)(r,"ext.dsa"),_n29={requestId:e.bidId,cpm:r.price,creativeId:r.crid,ttl:360,netRevenue:"net"===e.netRevenue,currency:a,mediaType:_t44,width:r.w,height:r.h,dealId:r.dealid,meta:{mediaType:_t44,advertiserDomains:r.adomain,dsa:_s15,primaryCatId:(_r$cat=r.cat)===null||_r$cat===void 0?void 0:_r$cat[0],secondaryCatIds:(_r$cat2=r.cat)===null||_r$cat2===void 0?void 0:_r$cat2.slice(1)}};
return r["native"]?_n29["native"]={ortb:r["native"]}:_t44===i.G_?(_n29.vastXml=r.adm,r.nurl&&(_n29.vastUrl=r.nurl)):_n29.ad=r.adm,e.renderer||_t44!==i.G_||"outstream"!==(0,o.A)(e,"mediaTypes.video.context")||(_n29.renderer=c.A4.install({id:e.bidId,url:"https://s2.adform.net/banners/scripts/video/outstream/render.js",adUnitCode:e.adUnitCode}),_n29.renderer.setRender(v)),_n29}}).filter(Boolean)}};function v(e){e.renderer.push(function(){window.Adform.renderOutstream(e)})}(0,a.a$)(l),(0,s.E)("adfBidAdapter")}},
function(e){e.O(0,[147,802,769,139,85],function(){return t=7316,e(e.s=t);var t});e.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[793],{6345:function _(e,n,t){var s=t(7873),i=t(1069),a=t(3172),r=t(3272),o=t(6916),p=t(1970),c=t(965),l=t(5555),u=t(3806);var d={};var g=_createClass(function g(e,n){_classCallCheck(this,g);this.message=e,this.args=null==n?[]:[n]});var _e72=new WeakMap;var _n30=new WeakMap;var _t45=new WeakMap;var h=function(){function h(e){var _map7,_map8,_this15=this;_classCallCheck(this,
h);_defineProperty(this,"apiVersion","1.1");_classPrivateFieldInitSpec(this,_e72,void 0);_classPrivateFieldInitSpec(this,_n30,void 0);_classPrivateFieldInitSpec(this,_t45,[]);_defineProperty(this,"initialized",!1);this.cmp=e,_map7=["resolve","reject"].map(function(e){return function(n){for(;_classPrivateFieldGet(_t45,_this15).length;)_classPrivateFieldGet(_t45,_this15).pop()[e](n)}}),_map8=_slicedToArray(_map7,2),_toSetter(_classPrivateFieldSet,[_e72,this])._=_map8[0],_toSetter(_classPrivateFieldSet,
[_n30,this])._=_map8[1]}return _createClass(h,[{key:"init",value:function init(e){var _this16=this;var n=this.updateWhenReady(e);return this.initialized||(e.gppVersion!==this.apiVersion&&(0,i.JE)("Unrecognized GPP CMP version: ".concat(e.apiVersion,". Continuing using GPP API version ").concat(this.apiVersion,"...")),this.initialized=!0,this.cmp({command:"addEventListener",callback:function callback(e,n){var _e$pingData;null==n||n?"error"===(e===null||e===void 0||(_e$pingData=e.pingData)===null||
_e$pingData===void 0?void 0:_e$pingData.cmpStatus)?_classPrivateFieldGet(_n30,_this16).call(_this16,new g('CMP status is "error"; please check CMP setup',e)):_this16.isCMPReady((e===null||e===void 0?void 0:e.pingData)||{})&&["sectionChange","signalStatus"].includes(e===null||e===void 0?void 0:e.eventName)&&_classPrivateFieldGet(_e72,_this16).call(_this16,_this16.updateConsent(e.pingData)):_classPrivateFieldGet(_n30,_this16).call(_this16,new g("Received error response from CMP",e)),null==o.ad.getConsentData()||
null==(e===null||e===void 0?void 0:e.pingData)||_this16.isCMPReady(e.pingData)||o.ad.setConsentData(null)}})),n}},{key:"refresh",value:function refresh(){return this.cmp({command:"ping"}).then(this.init.bind(this))}},{key:"updateConsent",value:function updateConsent(e){return new l.U9(function(n){if(null==e||(0,i.Im)(e))throw new g("Received empty response from CMP",e);var t=C(e);(0,i.fH)("Retrieved GPP consent from CMP:",t),o.ad.setConsentData(t),n(t)})}},{key:"nextUpdate",value:function nextUpdate(){var e=
(0,l.v6)();return _classPrivateFieldGet(_t45,this).push(e),e.promise}},{key:"updateWhenReady",value:function updateWhenReady(e){return this.isCMPReady(e)?this.updateConsent(e):this.nextUpdate()}},{key:"isCMPReady",value:function isCMPReady(e){return"ready"===e.signalStatus}}],[{key:"get",value:function get(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c.c5;if(null==this.INST){var _n31=e({apiName:"__gpp",apiArgs:["command","callback","parameter"],mode:c.q4});if(null==_n31)throw new g("GPP CMP not found");
this.INST=new this(_n31)}return this.INST}}])}();_defineProperty(h,"INST",void 0);var m={iab:function iab(){return new l.U9(function(e){return e(h.get().refresh())})}};function C(e){if(null!=(e===null||e===void 0?void 0:e.applicableSections)&&!Array.isArray(e.applicableSections)||null!=(e===null||e===void 0?void 0:e.gppString)&&!(0,i.O8)(e.gppString)||null!=(e===null||e===void 0?void 0:e.parsedSections)&&!(0,i.Qd)(e.parsedSections))throw new g("CMP returned unexpected value during lookup process.",
e);return["usnatv1","uscav1"].forEach(function(n){var _e$parsedSections;(e===null||e===void 0||(_e$parsedSections=e.parsedSections)===null||_e$parsedSections===void 0?void 0:_e$parsedSections[n])&&(0,i.JE)("Received invalid section from cmp: '".concat(n,"'. Some functionality may not work as expected"),e)}),f(e)}function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{gppString:e===null||e===void 0?void 0:e.gppString,applicableSections:(e===null||e===void 0?void 0:e.applicableSections)||
[],parsedSections:(e===null||e===void 0?void 0:e.parsedSections)||{},gppData:e}}var S=(0,u.yq)({namespace:"gpp",displayName:"GPP",consentDataHandler:o.ad,parseConsentData:C,getNullConsent:function getNullConsent(){return f(null)},cmpHandlers:m});r.$W.getConfig("consentManagement",function(e){return function(e,_d$loadConsentData,_d4,_d$loadConsentData$ca){return d=S(e),(_d$loadConsentData=(_d4=d).loadConsentData)===null||_d$loadConsentData===void 0||(_d$loadConsentData=_d$loadConsentData.call(_d4))===
null||_d$loadConsentData===void 0||(_d$loadConsentData$ca=_d$loadConsentData["catch"])===null||_d$loadConsentData$ca===void 0?void 0:_d$loadConsentData$ca.call(_d$loadConsentData,function(){return null})}(e.consentManagement)}),p.w.before(function(e,n){return e(n.then(function(e){var n=o.ad.getConsentData();return n&&(Array.isArray(n.applicableSections)&&(0,a.J)(e,"regs.gpp_sid",n.applicableSections),(0,a.J)(e,"regs.gpp",n.gppString)),e}))}),(0,s.E)("consentManagementGpp")}},function(e){e.O(0,[109,
10,802,769,139,85],function(){return n=6345,e(e.s=n);var n});e.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[81],{6117:function _(e,n,t){var r=t(7873),s=t(1069),a=t(3172),o=t(3272),c=t(6916),i=t(1E3),p=t(1970),d=t(965),l=t(3806);var u,g,f={};var C=2,m={iab:function iab(e){return new Promise(function(n,t){var r=(0,d.c5)({apiName:"__tcfapi",apiVersion:C,apiArgs:["command","version","callback","parameter"]});r||t(new Error("TCF2 CMP not found.")),r.isDirect?(0,s.fH)("Detected CMP API is directly accessible, calling it now..."):
(0,s.fH)("Detected CMP is outside the current iframe where Prebid.js is located, calling it now..."),r({command:"addEventListener",callback:function callback(r,a){if((0,s.fH)("Received a response from CMP",r),a){try{e(b(r))}catch(e){}if(!1===r.gdprApplies||"tcloaded"===r.eventStatus||"useractioncomplete"===r.eventStatus)try{c.mW.setConsentData(b(r)),n()}catch(e){t(e)}}else t(Error("CMP unable to register callback function.  Please check CMP setup."))}})})}};function b(e){if(function(){var n=e&&"boolean"==
typeof e.gdprApplies?e.gdprApplies:u,t=e&&e.tcString;return!("boolean"==typeof n&&(!0!==n||t&&(0,s.O8)(t)))}())throw Object.assign(new Error("CMP returned unexpected value during lookup process."),{args:[e]});return D(e)}function D(e){var n={consentString:e?e.tcString:void 0,vendorData:e||void 0,gdprApplies:e&&"boolean"==typeof e.gdprApplies?e.gdprApplies:u};return e&&e.addtlConsent&&(0,s.O8)(e.addtlConsent)&&(n.addtlConsent=e.addtlConsent),n.apiVersion=C,n}var v=(0,l.yq)({namespace:"gdpr",displayName:"TCF",
consentDataHandler:c.mW,cmpHandlers:m,parseConsentData:b,getNullConsent:function getNullConsent(){return D(null)}});o.$W.getConfig("consentManagement",function(e){return function(e,_e73,_e74,_e75,_f$loadConsentData,_f2,_f$loadConsentData$ca){return e=e&&(e.gdpr||e.usp||e.gpp?e.gdpr:e),null!=((_e73=e)===null||_e73===void 0||(_e73=_e73.consentData)===null||_e73===void 0?void 0:_e73.getTCData)&&(e.consentData=e.consentData.getTCData),u=!0===((_e74=e)===null||_e74===void 0?void 0:_e74.defaultGdprScope),
g=!!((_e75=e)!==null&&_e75!==void 0&&_e75.dsaPlatform),f=v({gdpr:e}),(_f$loadConsentData=(_f2=f).loadConsentData)===null||_f$loadConsentData===void 0||(_f$loadConsentData=_f$loadConsentData.call(_f2))===null||_f$loadConsentData===void 0||(_f$loadConsentData$ca=_f$loadConsentData["catch"])===null||_f$loadConsentData$ca===void 0?void 0:_f$loadConsentData$ca.call(_f$loadConsentData,function(){return null})}(e.consentManagement)}),p.w.before(function(e,n){return e(n.then(function(e){var n=c.mW.getConsentData();
return n&&("boolean"==typeof n.gdprApplies&&(0,a.J)(e,"regs.ext.gdpr",n.gdprApplies?1:0),(0,a.J)(e,"user.ext.consent",n.consentString)),g&&(0,a.J)(e,"regs.ext.dsa.dsarequired",3),e}))}),(0,i.pS)({type:i.S3,name:"gdprAddtlConsent",fn:function fn(e,n){var _n$gdprConsent;var t=(_n$gdprConsent=n.gdprConsent)===null||_n$gdprConsent===void 0?void 0:_n$gdprConsent.addtlConsent;t&&"string"==typeof t&&(0,a.J)(e,"user.ext.ConsentedProvidersSettings.consented_providers",t)}}),(0,r.E)("consentManagementTcf")}},
function(e){e.O(0,[802,109,10,769,139,85],function(){return n=6117,e(e.s=n);var n});e.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[662],{6056:function _(n,t,e){var o=e(7873),a=e(1069),s=e(3172),i=e(3272),c=e(8046),r=e(6916),u=e(6894),l=e(9214),f=e(1970),g=e(965);var d="iab";var p,m,P=d,v=50,S=!1;var b={iab:function iab(n){var t=n.onSuccess,e=n.onError;var o=function(){var n={};return{consentDataCallback:function consentDataCallback(o,a){a&&o.uspString&&(n.usPrivacy=o.uspString),n.usPrivacy?h(n,
{onSuccess:t,onError:e}):e("Unable to get USP consent string.")}}}();var s=(0,g.c5)({apiName:"__uspapi",apiVersion:1,apiArgs:["command","version","callback"]});if(!s)return e("USP CMP not found.");s.isDirect?(0,a.fH)("Detected USP CMP is directly accessible, calling it now..."):(0,a.fH)("Detected USP CMP is outside the current iframe where Prebid.js is located, calling it now...");s({command:"getUSPData",callback:o.consentDataCallback}),s({command:"registerDeletion",callback:function callback(n,t){return(null==
t||t)&&c.Ay.callDataDeletionRequest(n)}})["catch"](function(n){(0,a.vV)("Error invoking CMP `registerDeletion`:",n)})},"static":function _static(n){var t=n.onSuccess,e=n.onError;h(p,{onSuccess:t,onError:e})}};function D(n){var t=null,e=!1;function o(o,a){if(null!=t&&clearTimeout(t),e=!0,r.t6.setConsentData(o),null!=n){for(var s=arguments.length,i=new Array(s>2?s-2:0),c=2;c<s;c++)i[c-2]=arguments[c];n.apply(void 0,[a].concat(i))}}if(!b[P])return void o(null,"USP framework (".concat(P,") is not a supported framework. Aborting consentManagement module and resuming auction."));
var a={onSuccess:o,onError:function onError(n){for(var t=arguments.length,e=new Array(t>1?t-1:0),a=1;a<t;a++)e[a-1]=arguments[a];o.apply(void 0,[null,"".concat(n," Resuming auction without consent data as per consentManagement config.")].concat(e))}};b[P](a),e||(0===v?h(void 0,a):t=setTimeout(a.onError.bind(null,"USPAPI workflow exceeded timeout threshold."),v))}var y=(0,u.Ak)("usp",function(n,t){var e=this;S||U(),D(function(o){if(null!=o){for(var s=arguments.length,i=new Array(s>1?s-1:0),c=1;c<s;c++)i[c-
1]=arguments[c];(0,a.JE).apply(void 0,[o].concat(i))}n.call(e,t)})});function h(n,t){var e=t.onSuccess,o=t.onError;!n||!n.usPrivacy?o("USPAPI returned unexpected value during lookup process.",n):(!function(n){n&&n.usPrivacy&&(m=n.usPrivacy)}(n),e(m))}function U(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];S||((0,a.fH)("USPAPI consentManagement module has been activated"+(n?"":" using default values (api: '".concat(P,"', timeout: ").concat(v,"ms)"))),S=!0,r.t6.enable()),D()}i.$W.getConfig("consentManagement",
function(n){return function(n){(n=n&&n.usp)&&"object"==_typeof(n)||(0,a.JE)("consentManagement.usp config not defined, using defaults"),n&&(0,a.O8)(n.cmpApi)?P=n.cmpApi:(P=d,(0,a.fH)("consentManagement.usp config did not specify cmpApi. Using system default setting (".concat(d,")."))),n&&(0,a.Et)(n.timeout)?v=n.timeout:(v=50,(0,a.fH)("consentManagement.usp config did not specify timeout. Using system default setting (50).")),"static"===P&&((0,a.Qd)(n.consentData)&&(0,a.Qd)(n.consentData.getUSPData)?
(n.consentData.getUSPData.uspString&&(p={usPrivacy:n.consentData.getUSPData.uspString}),v=0):(0,a.vV)("consentManagement config with cmpApi: 'static' did not specify consentData. No consents will be available to adapters.")),U(!0)}(n.consentManagement)}),(0,l.Yn)("requestBids").before(y,50),f.w.before(function(n,t){return n(t.then(function(n){var t=r.t6.getConsentData();return t&&(0,s.J)(n,"regs.ext.us_privacy",t),n}))}),(0,o.E)("consentManagementUsp")}},function(n){n.O(0,[109,802,769,139,85],function(){return t=
6056,n(n.s=t);var t});n.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[466],{9236:function _(e,r,n){var t=n(7873),o=n(1069),c=n(3172),i=n(8969),s=n(8044),u=n(3272),a=n(9214),f=n(5555),l=n(1E3),d=n(6894),y=n(5023),v=n(1970),p=n(6400);var h=4;var C,g=[],b={},$=!1,R=!0,m="USD";var O=!1,S={};var E,N={},T=(0,f.v6)();var U=(0,p.L)();var w=0;function D(e){if(C="https://cdn.jsdelivr.net/gh/prebid/currency-file@1/latest.json?date\x3d$$TODAY$$",null!==e.rates&&"object"==_typeof(e.rates)&&(S.conversions=
e.rates,$=!0,R=!1),null!==e.defaultRates&&"object"==_typeof(e.defaultRates)&&(E=e.defaultRates,S.conversions=E,$=!0),"string"==typeof e.adServerCurrency){w=e.auctionDelay,(0,o.fH)("enabling currency support",arguments),m=e.adServerCurrency,e.conversionRateFile&&((0,o.fH)("currency using override conversionRateFile:",e.conversionRateFile),C=e.conversionRateFile);var _r18=C.indexOf("$$TODAY$$");if(-1!==_r18){var _e76=new Date;var _n32="".concat(_e76.getMonth()+1),_t46="".concat(_e76.getDate());_n32.length<
2&&(_n32="0".concat(_n32)),_t46.length<2&&(_t46="0".concat(_t46));var _o12="".concat(_e76.getFullYear()).concat(_n32).concat(_t46);C="".concat(C.substring(0,_r18)).concat(_o12).concat(C.substring(_r18+9,C.length))}b={},O||(O=!0,(0,t.m)().convertCurrency=function(e,r,n){return parseFloat(e)*J(r,n)},(0,a.Yn)("addBidResponse").before(A,100),(0,a.Yn)("responsesReady").before(k),v.w.before(_),(0,a.Yn)("requestBids").before(q,50),(0,y.on)(i.qY.AUCTION_TIMEOUT,F),(0,y.on)(i.qY.AUCTION_INIT,I),I())}else w=
0,(0,o.fH)("disabling currency support"),O&&((0,a.Yn)("addBidResponse").getHooks({hook:A}).remove(),(0,a.Yn)("responsesReady").getHooks({hook:k}).remove(),v.w.getHooks({hook:_}).remove(),(0,a.Yn)("requestBids").getHooks({hook:q}).remove(),(0,y.AU)(i.qY.AUCTION_TIMEOUT,F),(0,y.AU)(i.qY.AUCTION_INIT,I),delete (0,t.m)().convertCurrency,m="USD",b={},O=!1,$=!1,R=!0,S={},N={},T=(0,f.v6)());"object"==_typeof(e.bidderCurrencyDefault)&&(N=e.bidderCurrencyDefault)}function Y(e){E?((0,o.JE)(e),(0,o.JE)("Currency failed loading rates, falling back to currency.defaultRates")):
(0,o.vV)(e)}function I(){R?(R=!1,$=!1,(0,s.RD)(C,{success:function success(e){try{S=JSON.parse(e),(0,o.fH)("currencyRates set to "+JSON.stringify(S)),b={},$=!0,H(),U.resume()}catch(r){Y("Failed to parse currencyRates response: "+e)}},error:function error(){Y.apply(void 0,arguments),$=!0,H(),U.resume(),R=!0}})):H()}function k(e,r){e(r.then(function(){return T.promise}))}u.$W.getConfig("currency",function(e){return D(e.currency)});var A=(0,d.NL)("currency",function(e,r,n,t){if(!n)return e.call(this,
r,n,t);var c=n.bidderCode||n.bidder;if(N[c]){var _e77=N[c];n.currency&&_e77!==n.currency?(0,o.JE)("Currency default '".concat(c,": ").concat(_e77,"' ignored. adapter specified '").concat(n.currency,"'")):n.currency=_e77}if(n.currency||((0,o.JE)('Currency not specified on bid.  Defaulted to "USD"'),n.currency="USD"),n.getCpmInNewCurrency=function(e){return(parseFloat(this.cpm)*J(this.currency,e)).toFixed(3)},n.currency===m)return e.call(this,r,n,t);g.push([e,this,r,n,t]),O&&!$||H()});function F(e){var r=
e.auctionId;g=g.filter(function(e){var _e78=_slicedToArray(e,5),n=_e78[0],t=_e78[1],o=_e78[2],c=_e78[3],s=_e78[4];if(c.auctionId!==r)return!0;s(i.Tf.CANNOT_CONVERT_CURRENCY)})}function H(){for(;g.length>0;){var _g$shift=g.shift(),_g$shift2=_slicedToArray(_g$shift,5),_e79=_g$shift2[0],_r19=_g$shift2[1],_n33=_g$shift2[2],_t47=_g$shift2[3],_c8=_g$shift2[4];if(void 0!==_t47&&"currency"in _t47&&"cpm"in _t47){var _e80=_t47.currency;try{var _r20=J(_e80);1!==_r20&&(_t47.cpm=(parseFloat(_t47.cpm)*_r20).toFixed(4),
_t47.currency=m)}catch(e){(0,o.JE)("getCurrencyConversion threw error: ",e),_c8(i.Tf.CANNOT_CONVERT_CURRENCY);continue}}_e79.call(_r19,_n33,_t47,_c8)}T.resolve()}function J(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:m;var n,t=null;var c="".concat(e,"-\x3e").concat(r);if(c in b)t=b[c],(0,o.OG)("Using conversionCache value "+t+" for "+c);else if(!1===O){if("USD"!==e)throw new Error("Prebid currency support has not been enabled and fromCurrency is not USD");t=1}else if(e===r)t=1;
else if(e in S.conversions){if(!(r in(n=S.conversions[e])))throw new Error("Specified adServerCurrency in config '"+r+"' not found in the currency rates file");t=n[r],(0,o.fH)("getCurrencyConversion using direct "+e+" to "+r+" conversionRate "+t)}else if(r in S.conversions){if(!(e in(n=S.conversions[r])))throw new Error("Specified fromCurrency '"+e+"' not found in the currency rates file");t=j(1/n[e],h),(0,o.fH)("getCurrencyConversion using reciprocal "+e+" to "+r+" conversionRate "+t)}else{var i=
Object.keys(S.conversions)[0];if(!(e in S.conversions[i]))throw new Error("Specified fromCurrency '"+e+"' not found in the currency rates file");var s=1/S.conversions[i][e];if(!(r in S.conversions[i]))throw new Error("Specified adServerCurrency in config '"+r+"' not found in the currency rates file");t=j(s*S.conversions[i][r],h),(0,o.fH)("getCurrencyConversion using intermediate "+e+" thru "+i+" to "+r+" conversionRate "+t)}return c in b||((0,o.OG)("Adding conversionCache value "+t+" for "+c),b[c]=
t),t}function j(e,r){var n=1;for(var _e81=0;_e81<r;_e81++)n+="0";return Math.round(e*n)/n}function _(e,r){return e(r.then(function(e){return(0,c.J)(e,"ext.prebid.adServerCurrency",m),e}))}(0,l.pS)({type:l.S3,name:"currency",fn:function fn(e,r,n){O&&(e.cur=e.cur||[n.currency||m])}});var q=(0,d.Ak)("currency",function(e,r){var n=(t=this,function(){return e.call(t,r)});var t;!$&&w>0?U.submit(w,n,function(){(0,o.JE)("currency: Fetch attempt did not return in time for auction ".concat(r.auctionId)),n()}):
n()});(0,t.E)("currency")}},function(e){e.O(0,[802,957,769,139,85],function(){return r=9236,e(e.s=r);var r});e.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[301],{1057:function _(n,e,s){var t=s(7873),a=s(3272),l=s(7274);var p=!1;a.$W.getConfig("consentManagement",function(n){var _n$consentManagement;null==(n===null||n===void 0||(_n$consentManagement=n.consentManagement)===null||_n$consentManagement===void 0?void 0:_n$consentManagement.gpp)||p||((0,l.l)("usnat",[7]),p=!0)}),(0,t.E)("gppControl_usnat")}},
function(n){n.O(0,[82,802,769,139,85],function(){return e=1057,n(n.s=e);var e});n.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[534],{8194:function _(n,t,e){var s=e(7873),r=e(3272),o=e(7274),i=e(3172),a=e(1069);var l={Version:0,Gpc:0,SharingNotice:0,SaleOptOutNotice:0,SharingOptOutNotice:0,TargetedAdvertisingOptOutNotice:0,SensitiveDataProcessingOptOutNotice:0,SensitiveDataLimitUseNotice:0,SaleOptOut:0,SharingOptOut:0,TargetedAdvertisingOptOut:0,SensitiveDataProcessing:12,KnownChildSensitiveDataConsents:2,
PersonalDataConsents:0,MspaCoveredTransaction:0,MspaOptOutOptionMode:0,MspaServiceProviderMode:0};function c(n){var _n$nullify=n.nullify,t=_n$nullify===void 0?[]:_n$nullify,_n$move=n.move,e=_n$move===void 0?{}:_n$move,s=n.fn,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;return e=Object.fromEntries(Object.entries(e).map(function(n){var _n34=_slicedToArray(n,2),t=_n34[0],e=_n34[1];return[t,Object.fromEntries(Object.entries(e).map(function(n){var _n35=_slicedToArray(n,2),t=_n35[0],e=_n35[1];
return[t,Array.isArray(e)?e:[e]]}).map(function(n){var _n36=_slicedToArray(n,2),t=_n36[0],e=_n36[1];return[--t,e.map(function(n){return--n})]}))]})),function(n){var o=Object.fromEntries(Object.entries(r).map(function(t){var _t48=_slicedToArray(t,2),s=_t48[0],r=_t48[1],o=null;if(r>0){if(o=Array(r).fill(null),Array.isArray(n[s])){var _t49=e[s]||{},_i31=[];n[s].forEach(function(n,e){var _ref64=_t49.hasOwnProperty(e)?[_t49[e],!0]:[[e],!1],_ref65=_slicedToArray(_ref64,2),s=_ref65[0],a=_ref65[1];s.forEach(function(t){t<
r&&!_i31.includes(t)&&(o[t]=n,a&&_i31.push(t))})})}}else null!=n[s]&&(o=Array.isArray(n[s])?null:n[s]);return[s,o]}));return t.forEach(function(n){return(0,i.J)(o,n,null)}),s&&s(n,o),o}}function u(n,t){t.KnownChildSensitiveDataConsents=0===n.KnownChildSensitiveDataConsents?[0,0]:[1,1]}var p={7:function _(n){return n},8:c({move:{SensitiveDataProcessing:{1:9,2:10,3:8,4:[1,2],5:12,8:3,9:4}},fn:function fn(n,t){n.KnownChildSensitiveDataConsents.some(function(n){return 0!==n})&&(t.KnownChildSensitiveDataConsents=
[1,1])}}),9:c({fn:u}),10:c({fn:u}),11:c({move:{SensitiveDataProcessing:{3:4,4:5,5:3}},fn:u}),12:c({fn:function fn(n,t){var e=n.KnownChildSensitiveDataConsents;var s;s=e.some(function(n){return 0!==n})?2===e[1]&&2===e[2]?[2,1]:[1,1]:[0,0],t.KnownChildSensitiveDataConsents=s}})},f={8:"usca",9:"usva",10:"usco",11:"usut",12:"usct"},O=function(){var n=Object.keys(f).map(Number);return function(){var _ref66=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_ref66$sections=_ref66.sections,t=_ref66$sections===
void 0?{}:_ref66$sections,_ref66$sids=_ref66.sids,e=_ref66$sids===void 0?n:_ref66$sids;return e.map(function(n){var e=(0,a.h0)("Cannot set up MSPA controls for SID ".concat(n,":")),s=t[n]||{},r=s.normalizeAs||n;if(!p.hasOwnProperty(r))return void e.logError("no normalization rules are known for SID ".concat(r));var o=s.name||f[n];if("string"==typeof o)return[o,[n],p[r]];e.logError("cannot determine GPP section name")}).filter(function(n){return null!=n})}}(),v=[];r.$W.getConfig("consentManagement",
function(n){var _n$consentManagement2;var t=(_n$consentManagement2=n.consentManagement)===null||_n$consentManagement2===void 0?void 0:_n$consentManagement2.gpp;if(t){for(;v.length;)v.pop()();O((t===null||t===void 0?void 0:t.mspa)||{}).forEach(function(n){var _n37=_slicedToArray(n,3),t=_n37[0],e=_n37[1],s=_n37[2];return v.push((0,o.l)(t,e,s))})}}),(0,s.E)("gppControl_usstates")}},function(n){n.O(0,[82,802,769,139,85],function(){return t=8194,n(n.s=t);var t});n.O()}]);(self.pbjsChunk=self.pbjsChunk||
[]).push([[584],{4232:function _(t,e,o){var n=o(7873),a=o(1252),d=o(6881),r=o(3272),s=o(8969),i=o(9214),l=o(1069),c=o(3172),u=o(433);var f={},p=!1;function g(t){return(0,a.eu)(t)}var b=function b(t){return(r.$W.getConfig("gptPreAuction")||{}).mcmEnabled?t.replace(/(^\/\d*),\d*\//,"$1/"):t};function m(t){(0,l.JE)("pbadslot is deprecated and will soon be removed, use gpid instead",t)}var h=function h(t,e){var o=function(t){var _f4=f,e=_f4.customGptSlotMatching;if(!(0,l.II)())return;var o=t.reduce(function(t,
e){return t[e.code]=t[e.code]||[],t[e.code].push(e),t},{}),n={};return window.googletag.pubads().getSlots().forEach(function(t){var a=Object.keys(o).find(e?e(t):(0,l.iC)(t));if(a){var _e82=n[a]=t.getAdUnitPath(),_d5={name:"gam",adslot:b(_e82)};o[a].forEach(function(t){var _t$ortb2Imp;(0,c.J)(t,"ortb2Imp.ext.data.adserver",Object.assign({},(_t$ortb2Imp=t.ortb2Imp)===null||_t$ortb2Imp===void 0||(_t$ortb2Imp=_t$ortb2Imp.ext)===null||_t$ortb2Imp===void 0||(_t$ortb2Imp=_t$ortb2Imp.data)===null||_t$ortb2Imp===
void 0?void 0:_t$ortb2Imp.adserver,_d5))})}}),n}(e),_f3=f,n=_f3.useDefaultPreAuction,a=_f3.customPreAuction;e.forEach(function(t){t.ortb2Imp=t.ortb2Imp||{},t.ortb2Imp.ext=t.ortb2Imp.ext||{},t.ortb2Imp.ext.data=t.ortb2Imp.ext.data||{};var e=t.ortb2Imp.ext;if(a||n){var _e$data2;((_e$data2=e.data)===null||_e$data2===void 0?void 0:_e$data2.pbadslot)&&m(t);var _d6,_r21=(0,u.A)(e,"data.adserver.adslot");a?_d6=a(t,_r21,o===null||o===void 0?void 0:o[t.code]):n&&(_d6=function(t,e,o){var n=t.ortb2Imp.ext.data;
if(n.pbadslot)return n.pbadslot;if((0,l.II)()){var a=window.googletag.pubads().getSlots().filter(function(t){return t.getAdUnitPath()===o});if(0!==a.length)return 1===a.length?e:"".concat(e,"#").concat(t.code)}}(t,_r21,o===null||o===void 0?void 0:o[t.code])),_d6&&(e.gpid=e.data.pbadslot=_d6)}else{m(t);var _o13=function(t){var e=t.ortb2Imp.ext.data,_f5=f,o=_f5.customPbAdSlot;if(!e.pbadslot)if(o)e.pbadslot=o(t.code,(0,u.A)(e,"adserver.adslot"));else{try{var _o14=document.getElementById(t.code);if(_o14.dataset.adslotid)return void(e.pbadslot=
_o14.dataset.adslotid)}catch(t){}if(!(0,u.A)(e,"adserver.adslot"))return e.pbadslot=t.code,!0;e.pbadslot=e.adserver.adslot}}(t);e.gpid||_o13||(e.gpid=e.data.pbadslot)}});for(var d=arguments.length,r=new Array(d>2?d-2:0),s=2;s<d;s++)r[s-2]=arguments[s];return t.call.apply(t,[undefined,e].concat(r))},A=function A(t,e){var o=function(t){var e={};return a.Cn.forEach(function(o){var n=t.flatMap(function(t){return t}).filter(function(t){return t.taxonomy===o}).map(function(t){return t.values});e[o]=n.length?
n.reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}):[],e[o]={values:e[o]}}),e}(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d.n.index;return t.map(function(t){var _e$getAuction;return(_e$getAuction=e.getAuction({auctionId:t}))===null||_e$getAuction===void 0||(_e$getAuction=_e$getAuction.getFPD())===null||_e$getAuction===void 0?void 0:_e$getAuction.global}).map(g).filter(function(t){return t})}(function(t){var e=arguments.length>1&&void 0!==arguments[1]?
arguments[1]:d.n;return Object.values(t).flatMap(function(t){return Object.entries(t)}).filter(function(t){return t[0]===s.xS.AD_ID||t[0].startsWith(s.xS.AD_ID+"_")}).flatMap(function(t){return t[1]}).map(function(t){var _e$findBidByAdId;return(_e$findBidByAdId=e.findBidByAdId(t))===null||_e$findBidByAdId===void 0?void 0:_e$findBidByAdId.auctionId}).filter(function(t){return null!=t}).filter(l.hj)}(e)));window.googletag.setConfig&&window.googletag.setConfig({pps:{taxonomies:o}}),t(e)},I=function I(t){f=
(0,l.Up)(t,["enabled",function(t){return!1!==t},"customGptSlotMatching",function(t){return"function"==typeof t&&t},"customPbAdSlot",function(t){return"function"==typeof t&&t},"customPreAuction",function(t){return"function"==typeof t&&t},"useDefaultPreAuction",function(t){return t!==null&&t!==void 0?t:!0}]),f.enabled?p||((0,i.Yn)("makeBidRequests").before(h),(0,i.Yn)("targetingDone").after(A),p=!0):((0,l.fH)("GPT Pre-Auction: Turning off module"),f={},(0,i.Yn)("makeBidRequests").getHooks({hook:h}).remove(),
(0,i.Yn)("targetingDone").getHooks({hook:A}).remove(),p=!1)};r.$W.getConfig("gptPreAuction",function(t){return I(t.gptPreAuction)}),I({}),(0,n.E)("gptPreAuction")}},function(t){t.O(0,[5,802,769,139,85],function(){return e=4232,t(t.s=e);var e});t.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[517],{8233:function _(e,r,t){var n=t(7873),i=t(1069),s=t(433),a=t(5820),d=t(1371),o=t(9115),u=t(2449),p=t(9192);var c=["prebid-eu","prebid-us","prebid-asia"],m=["USD"],l=[d.D4,d.s6],T=[{name:"dsarequired",
min:0,max:3},{name:"pubrender",min:0,max:2},{name:"datatopub",min:0,max:2}],h={code:"rtbhouse",supportedMediaTypes:l,gvlid:16,isBidRequestValid:function isBidRequestValid(e){return!(!c.includes(e.params.region)||!e.params.publisherId)},buildRequests:function buildRequests(e,r){e=(0,u.Xj)(e);var t={id:r.bidderRequestId,imp:e.map(function(e){return function(e,r,_t$ext2){var t={id:e.bidId,banner:A(e),"native":E(e),tagid:e.adUnitCode.toString()},n=function(e){var r=[];"function"==typeof e.getFloor&&Object.keys(e.mediaTypes).forEach(function(t){var _e$getFloor;
l.includes(t)&&r.push((_e$getFloor=e.getFloor({currency:m[0],mediaType:t,size:e.sizes||"*"}))===null||_e$getFloor===void 0?void 0:_e$getFloor.floor)});return r.length>0?Math.max.apply(Math,r):parseFloat(e.params.bidfloor)}(e);n&&(t.bidfloor=n);((_t$ext2=t.ext)===null||_t$ext2===void 0?void 0:_t$ext2.ae)&&delete t.ext.ae;var i=(0,s.A)(e,"ortb2Imp.ext.tid");i&&(t.ext=t.ext||{},t.ext.tid=i);return t}(e)}),site:f(e,r),cur:m,test:e[0].params.test||0,source:y(e[0],r)};if(r&&r.gdprConsent&&r.gdprConsent.gdprApplies){var _e83=
r.gdprConsent.consentString?r.gdprConsent.consentString.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):"",_n38=r.gdprConsent.gdprApplies?1:0;t.regs={ext:{gdpr:_n38}},t.user={ext:{consent:_e83}}}if(e[0].schain){var _r22=function(e){if(!e)return null;if(!function(e){if(!e.nodes)return!1;var r=["asi","sid","hp"];return e.nodes.every(function(e){return r.every(function(r){return e[r]})})}(e))return(0,i.vV)("RTB House: required schain params missing"),null;return e}(e[0].schain);_r22&&(t.ext=
{schain:_r22})}if(e[0].userIdAsEids){var _r23={eids:e[0].userIdAsEids};t.user&&t.user.ext?t.user.ext=_objectSpread(_objectSpread({},t.user.ext),_r23):t.user={ext:_r23}}var n=(r===null||r===void 0?void 0:r.ortb2)||{};["site","user","device","bcat","badv"].forEach(function(e){var r=n[e];r&&(0,i.D9)(t,_defineProperty({},e,r))});var a=(0,s.A)(n,"regs.ext.dsa");(function(e){return!((0,i.Im)(e)||!(0,i.Qd)(e))&&T.reduce(function(r,t){var n=e[t.name];return r&&(!e.hasOwnProperty(t.name)||(0,i.Et)(n)&&n>=
t.min&&n<=t.max)},!0)&&(!e.hasOwnProperty("transparency")||(0,i.cy)(e.transparency)&&e.transparency.every(function(e){return(0,i.Qd)(e)&&(0,i.O8)(e.domain)&&e.domain&&(0,i.cy)(e.dsaparams)&&e.dsaparams.every(function(e){return(0,i.Et)(e)})}))})(a)&&(0,i.D9)(t,{regs:{ext:{dsa:a}}});return{method:"POST",url:"https://"+e[0].params.region+".creativecdn.com/bidder/prebid/bids",data:JSON.stringify(t)}},interpretOrtbResponse:function interpretOrtbResponse(e,r){var t=e.body;if(!(0,i.cy)(t))return[];var n=
[];return t.forEach(function(e){if(!e.price)return;var r;r=0===e.adm.indexOf("{")?(0,p.Qz)(e):function(e){return{requestId:e.impid,mediaType:d.D4,cpm:e.price,creativeId:e.adid,ad:e.adm,width:e.w,height:e.h,ttl:55,meta:{advertiserDomains:e.adomain},netRevenue:!0,currency:"USD"}}(e),e.ext&&(r.ext=(0,i.Go)(e.ext),e.ext.dsa&&(r.meta=Object.assign({},r.meta,{dsa:e.ext.dsa}))),n.push(r)}),n},interpretResponse:function interpretResponse(e,r){return this.interpretOrtbResponse(e,r)}};function A(e){if("banner"===
e.mediaType||(0,s.A)(e,"mediaTypes.banner")||!e.mediaType&&!e.mediaTypes){var r=e.sizes||e.mediaTypes.banner.sizes;return{w:r[0][0],h:r[0][1],format:r.map(function(e){return{w:e[0],h:e[1]}})}}}function f(e,r){var t="unknown",n=null;e&&e.length>0&&(t=e[0].params.publisherId,n=e[0].params.channel&&e[0].params.channel.toString().slice(0,50));var i={publisher:{id:t.toString()},page:r.refererInfo.page,name:(0,a.$)()};return n&&(i.channel=n),i}function y(e,r){return{tid:(r===null||r===void 0?void 0:r.auctionId)||
""}}function E(e){if("native"===e.mediaType||(0,s.A)(e,"mediaTypes.native"))return{request:{assets:g(e)},ver:"1.1"}}function g(e){var r=e.nativeParams||(0,s.A)(e,"mediaTypes.native"),t=[];return r.title&&t.push({id:p.xv.NATIVE.ASSET_ID.TITLE,required:r.title.required?1:0,title:{len:r.title.len||25}}),r.image&&t.push({id:p.xv.NATIVE.ASSET_ID.IMAGE,required:r.image.required?1:0,img:x(r.image,p.xv.NATIVE.IMAGE_TYPE.MAIN)}),r.icon&&t.push({id:p.xv.NATIVE.ASSET_ID.ICON,required:r.icon.required?1:0,img:x(r.icon,
p.xv.NATIVE.IMAGE_TYPE.ICON)}),r.sponsoredBy&&t.push({id:p.xv.NATIVE.ASSET_ID.SPONSORED,required:r.sponsoredBy.required?1:0,data:{type:p.xv.NATIVE.DATA_ASSET_TYPE.SPONSORED,len:r.sponsoredBy.len}}),r.body&&t.push({id:p.xv.NATIVE.ASSET_ID.BODY,required:r.body.request?1:0,data:{type:p.xv.NATIVE.DATA_ASSET_TYPE.DESC,len:r.body.len}}),r.cta&&t.push({id:p.xv.NATIVE.ASSET_ID.CTA,required:r.cta.required?1:0,data:{type:p.xv.NATIVE.DATA_ASSET_TYPE.CTA_TEXT,len:r.cta.len}}),t}function x(e,r){var t={type:r};
if(e.aspect_ratios){var _r24=e.aspect_ratios[0],_n39=_r24.min_width||100;t.wmin=_n39,t.hmin=_n39/_r24.ratio_width*_r24.ratio_height}if(e.sizes){var _r25=Array.isArray(e.sizes[0])?e.sizes[0]:e.sizes;t.w=_r25[0],t.h=_r25[1]}return t}(0,o.a$)(h),(0,n.E)("rtbhouseBidAdapter")}},function(e){e.O(0,[931,107,802,769,139,85],function(){return r=8233,e(e.s=r);var r});e.O()}]);(self.pbjsChunk=self.pbjsChunk||[]).push([[847],{3297:function _(e,n,t){var r=t(7873),o=t(1069),u=t(433),s=t(3272),p=t(8046),i=t(6916),
l=t(5023),c=t(8969),d=t(5569),a=t(2604),f=t(5139),g=t(6811);var h={purpose:{},feature:{}},v={purpose:!1,feature:"specialFeatureOptins"},E={storage:{type:"purpose","default":{purpose:"storage",enforcePurpose:!0,enforceVendor:!0,vendorExceptions:[]},id:1},basicAds:{type:"purpose",id:2,"default":{purpose:"basicAds",enforcePurpose:!0,enforceVendor:!0,vendorExceptions:[]}},personalizedAds:{type:"purpose",id:4},measurement:{type:"purpose",id:7},transmitPreciseGeo:{type:"feature",id:1}},m=new Set,y=new Set,
B=new Set,k=new Set,q=new Set,A=new Set;var $=!1,b=!1;var C=[d.tW,d.fW,d.Tn,d.zu],D="TCF2",w=[],O=[2],P=[2,7,9,10];function S(e,n,t,r){var _o$consents,_o$legitimateInterest;var o=(0,u.A)(e,"vendorData.".concat(n));return!!(o!==null&&o!==void 0&&(_o$consents=o.consents)!==null&&_o$consents!==void 0&&_o$consents[t])||r&&!!(o!==null&&o!==void 0&&(_o$legitimateInterest=o.legitimateInterests)!==null&&_o$legitimateInterest!==void 0&&_o$legitimateInterest[t])}function T(e,n,t,r){var o;if(!1!==v[n])o=!!(0,
u.A)(e,"vendorData.".concat(v[n],".").concat(t));else{var _ref68=r===i.B1?["publisher",P]:["purpose",O],_ref69=_slicedToArray(_ref68,2),_n40=_ref69[0],_u2=_ref69[1];o=S(e,_n40,t,_u2.includes(t))}return{purpose:o,vendor:S(e,"vendor",r,O.includes(t))}}function V(e,n,t,r){var o=E[e.purpose];if((e.vendorExceptions||[]).includes(t))return!0;var u=e.enforceVendor&&!(r===i.B1||(e.softVendorExceptions||[]).includes(t)),_T=T(n,o.type,o.id,r),s=_T.purpose,p=_T.vendor;return(!e.enforcePurpose||s)&&(!u||p)}function x(e,
n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:function(){return null};return function(u){var p=i.mW.getConsentData(),l=u[a.iK];if(function(e,n,t){return null==e&&i.mW.enabled?((0,o.JE)("Attempting operation that requires purpose ".concat(n," consent while consent data is not available").concat(t?" (module: ".concat(t,")"):"",". Assuming no consent was given.")),!0):e&&e.gdprApplies}(p,e,l)){var _e84=function(e,n,t){if(n){var _r26=
s.$W.getConfig("gvlMapping");if(_r26&&_r26[n])return _r26[n];if(e===d.tp)return i.B1;var _i$o2$get=i.o2.get(n),_r27=_i$o2$get.gvlid,_u3=_i$o2$get.modules;if(null==_r27&&Object.keys(_u3).length>0)for(var _i32=0,_C=C;_i32<_C.length;_i32++){var _t50=_C[_i32];if(_u3.hasOwnProperty(_t50)){_r27=_u3[_t50],_t50!==e&&(0,o.JE)("Multiple GVL IDs found for module '".concat(n,"'; using the ").concat(_t50," module's ID (").concat(_r27,") instead of the ").concat(e,"'s ID (").concat(_u3[e],")"));break}}return null==
_r27&&t&&(_r27=t()),_r27||null}return null}(u[a.Dk],l,r(u));var _c9=!!n(p,l,_e84);if(!_c9)return t&&t.add(l),{allow:_c9}}}}function F(e){return x(e,function(n,t,r){return!!V(h.purpose[e],n,t,r)},arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return null})}function I(e){return function(n){if(n[a.Dk]!==d.tp)return e(n)}}var M=(W=F(1,m),function(e){if(e[a.Dk]!==d.tp||b)return W(e)});var W;var j=F(1,m),N=F(1,m),J=I(F(2,y)),
z=F(7,B,function(e){return function(e,n,_t$adapter){var t=p.Ay.getAnalyticsAdapter(e);return function(r){if("function"!=typeof r)return r;try{return r.call(t.adapter,n)}catch(n){(0,o.vV)("Error invoking ".concat(e," adapter.gvlid()"),n)}}(t===null||t===void 0||(_t$adapter=t.adapter)===null||_t$adapter===void 0?void 0:_t$adapter.gvlid)}(e[a.iK],e[a.TQ])}),G=F(4,k),K=I(function(){var e=x("2-10",function(e,n,t){for(var _r28=2;_r28<=10;_r28++){var _h$purpose$_r,_h$purpose$_r2;if((_h$purpose$_r=h.purpose[_r28])!==
null&&_h$purpose$_r!==void 0&&(_h$purpose$_r=_h$purpose$_r.vendorExceptions)!==null&&_h$purpose$_r!==void 0&&_h$purpose$_r.includes(n))return!0;var _T2=T(e,"purpose",_r28,t),_o15=_T2.purpose,_u4=_T2.vendor;if(_o15&&(_u4||(_h$purpose$_r2=h.purpose[_r28])!==null&&_h$purpose$_r2!==void 0&&(_h$purpose$_r2=_h$purpose$_r2.softVendorExceptions)!==null&&_h$purpose$_r2!==void 0&&_h$purpose$_r2.includes(n)))return!0}return!1},q),n=F(4,q);return function(){var _h$purpose$;return((_h$purpose$=h.purpose[4])!==
null&&_h$purpose$!==void 0&&_h$purpose$.eidsRequireP4Consent?n:e).apply(this,arguments)}}()),L=x("Special Feature 1",function(e,n,t){return V(h.feature[1],e,n,t)},A);l.on(c.qY.AUCTION_END,function(){var e=function e(_e85){return Array.from(_e85.keys()).filter(function(e){return null!=e})},n={storageBlocked:e(m),biddersBlocked:e(y),analyticsBlocked:e(B),ufpdBlocked:e(k),eidsBlocked:e(q),geoBlocked:e(A)};l.Ic(c.qY.TCF2_ENFORCEMENT,n),[m,y,B,k,q,A].forEach(function(e){return e.clear()})}),s.$W.getConfig("consentManagement",
function(e){return function(e){var n=(0,u.A)(e,"gdpr.rules");n||(0,o.JE)("TCF2: enforcing P1 and P2 by default"),n=Object.fromEntries((n||[]).map(function(e){return[e.purpose,e]})),b=!!(0,u.A)(e,"strictStorageEnforcement"),Object.entries(E).forEach(function(e){var _n$t;var _e86=_slicedToArray(e,2),t=_e86[0],r=_e86[1];h[r.type][r.id]=(_n$t=n[t])!==null&&_n$t!==void 0?_n$t:r["default"]}),$||(null!=h.purpose[1]&&($=!0,w.push((0,f.qB)(g.Ue,D,M)),w.push((0,f.qB)(g.Ml,D,j)),w.push((0,f.qB)(g.yl,D,N))),
null!=h.purpose[2]&&w.push((0,f.qB)(g.uc,D,J)),null!=h.purpose[4]&&w.push((0,f.qB)(g.DL,D,G),(0,f.qB)(g.qX,D,G)),null!=h.purpose[7]&&w.push((0,f.qB)(g.mo,D,z)),null!=h.feature[1]&&w.push((0,f.qB)(g.hE,D,L)),w.push((0,f.qB)(g.hq,D,K)))}(e.consentManagement)}),(0,r.E)("tcfControl")}},function(e){e.O(0,[802,769,139,85],function(){return n=3297,e(e.s=n);var n});e.O()}])})(),pbjs.processQueue();