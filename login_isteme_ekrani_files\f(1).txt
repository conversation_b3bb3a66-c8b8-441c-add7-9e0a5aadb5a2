(function(sttc){var window=this;if(window.googletag&&googletag.evalScripts){googletag.evalScripts();}if(window.googletag&&googletag._loaded_)return;var q,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},da=ca(this),ea=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",u={},fa={},v=function(a,b,c){if(!c||a!=null){c=fa[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}},w=function(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in u?f=u:f=da;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ea&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?ba(u,d,{configurable:!0,writable:!0,value:b}):b!==c&&(fa[d]===void 0&&(a=Math.random()*1E9>>>0,fa[d]=ea?da.Symbol(d):"$jscp$"+a+"$"+d),ba(f,fa[d],{configurable:!0,writable:!0,value:b})))}};w("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");w("Symbol.iterator",function(a){if(a)return a;a=(0,u.Symbol)("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(aa(this))}})}return a},"es6");var ha=function(a){a={next:a};a[v(u.Symbol,"iterator")]=function(){return this};return a},ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;if(ea&&typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja,x=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Lb=b.prototype},y=function(a){var b=typeof u.Symbol!="undefined"&&v(u.Symbol,"iterator")&&a[v(u.Symbol,"iterator")];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},B=function(a){if(!(a instanceof Array)){a=y(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},qa=function(a){return oa(a,a)},oa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},C=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},ra=ea&&typeof v(Object,"assign")=="function"?v(Object,"assign"):function(a,b){if(a==null)throw new TypeError("No nullish arg");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)C(d,e)&&(a[e]=d[e])}return a};w("Object.assign",function(a){return a||ra},"es6");var sa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};w("globalThis",function(a){return a||da},"es_2020");w("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}},"es6");w("WeakMap",function(a){function b(){}function c(g){var h=typeof g;return h==="object"&&g!==null||h==="function"}if(function(){if(!a||!Object.seal)return!1;try{var g=Object.seal({}),h=Object.seal({}),k=new a([[g,2],[h,3]]);if(k.get(g)!=2||k.get(h)!=3)return!1;k.delete(g);k.set(h,4);return!k.has(g)&&k.get(h)==4}catch(m){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,f=function(g){this.g=(e+=Math.random()+1).toString();if(g){g=y(g);for(var h;!(h=g.next()).done;)h=h.value,this.set(h[0],h[1])}};f.prototype.set=function(g,h){if(!c(g))throw Error("Invalid WeakMap key");if(!C(g,d)){var k=new b;ba(g,d,{value:k})}if(!C(g,d))throw Error("WeakMap key fail: "+g);g[d][this.g]=h;return this};f.prototype.get=function(g){return c(g)&&C(g,d)?g[d][this.g]:void 0};f.prototype.has=function(g){return c(g)&&C(g,d)&&C(g[d],this.g)};f.prototype.delete=function(g){return c(g)&&C(g,d)&&C(g[d],this.g)?delete g[d][this.g]:!1};return f},"es6");w("Map",function(a){if(function(){if(!a||typeof a!="function"||!v(a.prototype,"entries")||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(y([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var m=v(k,"entries").call(k),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(l){return!1}}())return a;var b=new u.WeakMap,c=function(h){this[0]={};this[1]=f();this.size=0;if(h){h=y(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=k:(m.entry={next:this[1],H:this[1].H,head:this[1],key:h,value:k},m.list.push(m.entry),this[1].H.next=m.entry,this[1].H=m.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.H.next=h.entry.next,h.entry.next.H=h.entry.H,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].H=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var m=v(this,"entries").call(this),n;!(n=m.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[v(u.Symbol,"iterator")]=v(c.prototype,"entries");var d=function(h,k){var m=k&&typeof k;m=="object"||m=="function"?b.has(k)?m=b.get(k):(m=""+ ++g,b.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&C(h[0],m))for(h=0;h<n.length;h++){var l=n[h];if(k!==k&&l.key!==l.key||k===l.key)return{id:m,list:n,index:h,entry:l}}return{id:m,list:n,index:-1,entry:void 0}},e=function(h,k){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.H;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.H=h.next=h.head=h},g=0;return c},"es6");w("Set",function(a){if(function(){if(!a||typeof a!="function"||!v(a.prototype,"entries")||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(y([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=v(d,"entries").call(d),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.g=new u.Map;if(c){c=y(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size};b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return v(this.g,"entries").call(this.g)};b.prototype.values=function(){return v(this.g,"values").call(this.g)};b.prototype.keys=v(b.prototype,"values");b.prototype[v(u.Symbol,"iterator")]=v(b.prototype,"values");b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b},"es6");w("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)C(b,d)&&c.push(b[d]);return c}},"es8");w("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}},"es6");w("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||v(Object,"is").call(Object,f,b))return!0}return!1}},"es7");var ta=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};w("String.prototype.includes",function(a){return a?a:function(b,c){return ta(this,b,"includes").indexOf(b,c||0)!==-1}},"es6");w("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof u.Symbol!="undefined"&&v(u.Symbol,"iterator")&&b[v(u.Symbol,"iterator")];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}},"es6");w("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)C(b,d)&&c.push([d,b[d]]);return c}},"es8");w("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}},"es6");w("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991},"es6");w("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991},"es6");w("Number.isInteger",function(a){return a?a:function(b){return v(Number,"isFinite").call(Number,b)?b===Math.floor(b):!1}},"es6");w("Number.isSafeInteger",function(a){return a?a:function(b){return v(Number,"isInteger").call(Number,b)&&Math.abs(b)<=v(Number,"MAX_SAFE_INTEGER")}},"es6");w("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ta(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}},"es6");var ua=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[v(u.Symbol,"iterator")]=function(){return e};return e};w("Array.prototype.entries",function(a){return a?a:function(){return ua(this,function(b,c){return[b,c]})}},"es6");w("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}},"es6");w("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}},"es6");w("Array.prototype.keys",function(a){return a?a:function(){return ua(this,function(b){return b})}},"es6");w("Array.prototype.values",function(a){return a?a:function(){return ua(this,function(b,c){return c})}},"es8");w("String.prototype.repeat",function(a){return a?a:function(b){var c=ta(this,null,"repeat");if(b<0||b>**********)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}},"es6");w("String.prototype.padStart",function(a){return a?a:function(b,c){var d=ta(this,null,"padStart");b-=d.length;c=c!==void 0?String(c):" ";return(b>0&&c?v(c,"repeat").call(c,Math.ceil(b/c.length)).substring(0,b):"")+d}},"es8");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var D=this||self,wa=function(a,b){var c=va("CLOSURE_FLAGS");a=c&&c[a];return a!=null?a:b},va=function(a){a=a.split(".");for(var b=D,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},xa=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},ya=function(a,b,c){a=a.split(".");c=c||D;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function za(a){D.setTimeout(function(){throw a;},0)};var Aa=function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};function Ba(a,b){var c=0;a=Aa(String(a)).split(".");b=Aa(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(f[0].length==0&&g[0].length==0)break;c=Ca(f[1].length==0?0:parseInt(f[1],10),g[1].length==0?0:parseInt(g[1],10))||Ca(f[2].length==0,g[2].length==0)||Ca(f[2],g[2]);f=f[3];g=g[3]}while(c==0)}return c}function Ca(a,b){return a<b?-1:a>b?1:0};var Da=wa(610401301,!1),Ea=wa(748402147,wa(1,!0));var Fa,Ga=D.navigator;Fa=Ga?Ga.userAgentData||null:null;function Ha(a){if(!Da||!Fa)return!1;for(var b=0;b<Fa.brands.length;b++){var c=Fa.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function E(a){var b;a:{if(b=D.navigator)if(b=b.userAgent)break a;b=""}return b.indexOf(a)!=-1};function Ia(){return Da?!!Fa&&Fa.brands.length>0:!1}function Ja(){return Ia()?!1:E("Opera")}function Ka(){return E("Firefox")||E("FxiOS")}function La(){return E("Safari")&&!(Ma()||(Ia()?0:E("Coast"))||Ja()||(Ia()?0:E("Edge"))||(Ia()?Ha("Microsoft Edge"):E("Edg/"))||(Ia()?Ha("Opera"):E("OPR"))||Ka()||E("Silk")||E("Android"))}function Ma(){return Ia()?Ha("Chromium"):(E("Chrome")||E("CriOS"))&&!(Ia()?0:E("Edge"))||E("Silk")};var Na=function(a,b){return Array.prototype.map.call(a,b,void 0)};function Oa(a,b){a:{for(var c=typeof a==="string"?a.split(""):a,d=a.length-1;d>=0;d--)if(d in c&&b.call(void 0,c[d],d,a)){b=d;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]};var Pa=function(a){Pa[" "](a);return a};Pa[" "]=function(){};var Qa=null,Sa=function(a){var b=[];Ra(a,function(c){b.push(c)});return b},Ra=function(a,b){function c(k){for(;d<a.length;){var m=a.charAt(d++),n=Qa[m];if(n!=null)return n;if(!/^[\s\xa0]*$/.test(m))throw Error("Unknown base64 encoding at char: "+m);}return k}Ta();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}},Ta=function(){if(!Qa){Qa={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++)for(var d=a.concat(b[c].split("")),e=0;e<d.length;e++){var f=d[e];Qa[f]===void 0&&(Qa[f]=e)}}};function Ua(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Va=void 0,Wa;function Xa(a){if(Wa)throw Error("");Wa=function(b){D.setTimeout(function(){a(b)},0)}}function Ya(a){if(Wa)try{Wa(a)}catch(b){throw b.cause=a,b;}}function Za(a){a=Error(a);Ua(a,"warning");Ya(a);return a};var $a=typeof u.Symbol==="function"&&typeof(0,u.Symbol)()==="symbol";function ab(a,b,c){return typeof u.Symbol==="function"&&typeof(0,u.Symbol)()==="symbol"?(c===void 0?0:c)&&u.Symbol.for&&a?u.Symbol.for(a):a!=null?(0,u.Symbol)(a):(0,u.Symbol)():b}var db=ab("jas",void 0,!0),eb=ab(void 0,"0di"),fb=ab(void 0,"1oa"),gb=ab(void 0,"0actk"),hb=ab("m_m","Ib",!0);var ib={bb:{value:0,configurable:!0,writable:!0,enumerable:!1}},jb=Object.defineProperties,F=$a?db:"bb",kb,lb=[];G(lb,7);kb=Object.freeze(lb);function mb(a,b){$a||F in a||jb(a,ib);a[F]|=b}function G(a,b){$a||F in a||jb(a,ib);a[F]=b}function nb(a){if(4&a)return 512&a?512:1024&a?1024:0}function ob(a){mb(a,32);return a};function pb(){return typeof BigInt==="function"};var qb={};function H(a,b){return b===void 0?a.g!==rb&&!!(2&(a.i[F]|0)):!!(2&b)&&a.g!==rb}var rb={};function sb(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();}var tb=Object.freeze({}),ub=Object.freeze({});function vb(a){var b=wb;if(!a)throw Error((typeof b==="function"?b():b)||String(a));}function xb(a){a.Hb=!0;return a}var wb=void 0;var yb=xb(function(a){return typeof a==="number"}),zb=xb(function(a){return typeof a==="string"}),Ab=xb(function(a){return typeof a==="boolean"});var Bb=typeof D.BigInt==="function"&&typeof D.BigInt(0)==="bigint";function Cb(a){var b=a;if(zb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(yb(b)&&!v(Number,"isSafeInteger").call(Number,b))throw Error(String(b));return Bb?BigInt(a):a=Ab(a)?a?"1":"0":zb(a)?a.trim()||"0":String(a)}var Ib=xb(function(a){return Bb?a>=Db&&a<=Eb:a[0]==="-"?Fb(a,Gb):Fb(a,Hb)}),Gb=v(Number,"MIN_SAFE_INTEGER").toString(),Db=Bb?BigInt(v(Number,"MIN_SAFE_INTEGER")):void 0,Hb=v(Number,"MAX_SAFE_INTEGER").toString(),Eb=Bb?BigInt(v(Number,"MAX_SAFE_INTEGER")):void 0;function Fb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var I=0,J=0;function Jb(a){var b=a>>>0;I=b;J=(a-b)/**********>>>0}function Kb(a){if(a<0){Jb(-a);var b=y(Lb(I,J));a=b.next().value;b=b.next().value;I=a>>>0;J=b>>>0}else Jb(a)}function Mb(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(***********b+a);else pb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+Nb(c)+Nb(a));return c}function Nb(a){a=String(a);return"0000000".slice(a.length)+a}function Ob(){var a=I,b=J;b&2147483648?pb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=y(Lb(a,b)),a=b.next().value,b=b.next().value,a="-"+Mb(a,b)):a=Mb(a,b);return a}function Lb(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function Pb(a){return Array.prototype.slice.call(a)};function Qb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var Rb=typeof BigInt==="function"?BigInt.asIntN:void 0,Sb=v(Number,"isSafeInteger"),Tb=v(Number,"isFinite"),Ub=v(Math,"trunc");function Vb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Wb(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+xa(a)+": "+a);return a}var Xb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Yb(a){switch(typeof a){case "bigint":return!0;case "number":return Tb(a);case "string":return Xb.test(a);default:return!1}}function Zb(a){if(!Tb(a))throw Za("enum");return a|0}function $b(a){return a==null?a:Tb(a)?a|0:void 0}function ac(a){if(typeof a!=="number")throw Za("int32");if(!Tb(a))throw Za("int32");return a|0}function bc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Tb(a)?a|0:void 0}function cc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Tb(a)?a>>>0:void 0}function dc(a){var b=0;b=b===void 0?0:b;if(!Yb(a))throw Za("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return ec(a);case "bigint":return String(Rb(64,a));default:return fc(a)}case 1024:switch(c){case "string":return hc(a);case "bigint":return Cb(Rb(64,a));default:return ic(a)}case 0:switch(c){case "string":return ec(a);case "bigint":return Cb(Rb(64,a));default:return jc(a)}default:return Qb(b,"Unknown format requested type for int64")}}function kc(a){return a==null?a:dc(a)}function lc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function mc(a){if(lc(a))return a;if(a.length<16)Kb(Number(a));else if(pb())a=BigInt(a),I=Number(a&BigInt(4294967295))>>>0,J=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");J=I=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),J*=1E6,I=I*1E6+d,I>=**********&&(J+=v(Math,"trunc").call(Math,I/**********),J>>>=0,I>>>=0);b&&(b=y(Lb(I,J)),a=b.next().value,b=b.next().value,I=a,J=b)}return Ob()}function jc(a){a=Ub(a);if(!Sb(a)){Kb(a);var b=I,c=J;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c***********+(b>>>0);b=v(Number,"isSafeInteger").call(Number,d)?d:Mb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function fc(a){a=Ub(a);if(Sb(a))a=String(a);else{var b=String(a);lc(b)?a=b:(Kb(a),a=Ob())}return a}function ec(a){var b=Ub(Number(a));if(Sb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return mc(a)}function hc(a){var b=Ub(Number(a));if(Sb(b))return Cb(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return pb()?Cb(Rb(64,BigInt(a))):Cb(mc(a))}function ic(a){return Sb(a)?Cb(jc(a)):Cb(fc(a))}function nc(a){if(typeof a!=="string")throw Error();return a}function oc(a){if(a!=null&&typeof a!=="string")throw Error();return a}function pc(a){return a==null||typeof a==="string"?a:void 0}function qc(a,b,c,d){if(a!=null&&a[hb]===qb)return a;if(!Array.isArray(a))return c?d&2?b[eb]||(b[eb]=rc(b)):new b:void 0;c=a[F]|0;d=c|d&32|d&2;d!==c&&G(a,d);return new b(a)}function rc(a){a=new a;mb(a.i,34);return a};function sc(a){return a};function tc(a,b,c,d){var e=d!==void 0;d=!!d;var f=[],g=a.length,h=4294967295,k=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var l=g&&a[g-1];l!=null&&typeof l==="object"&&l.constructor===Object?(g--,h=g):l=void 0;if(m&&!(b&128)&&!e){k=!0;var p;h=((p=uc)!=null?p:sc)(h-n,n,a,l,void 0)+n}}b=void 0;for(e=0;e<g;e++)if(p=a[e],p!=null&&(p=c(p,d))!=null)if(m&&e>=h){var r=e-n,t=void 0;((t=b)!=null?t:b={})[r]=p}else f[e]=p;if(l)for(var z in l)Object.prototype.hasOwnProperty.call(l,z)&&(a=l[z],a!=null&&(a=c(a,d))!=null&&(g=+z,e=void 0,m&&!v(Number,"isNaN").call(Number,g)&&(e=g+n)<h?f[e]=a:(g=void 0,((g=b)!=null?g:b={})[z]=a)));b&&(k?f.push(b):f[h]=b);return f}function vc(a){switch(typeof a){case "number":return v(Number,"isFinite").call(Number,a)?a:""+a;case "bigint":return Ib(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[F]|0;return a.length===0&&b&1?void 0:tc(a,b,vc)}if(a!=null&&a[hb]===qb)return K(a);return}return a}var wc=typeof structuredClone!="undefined"?structuredClone:function(a){return tc(a,0,vc)},uc;function K(a){a=a.i;return tc(a,a[F]|0,vc)};function L(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[F]|0;if(Ea&&1&e)throw Error("rfarr");2048&e&&!(2&e)&&xc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||G(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var k in h)Object.prototype.hasOwnProperty.call(h,k)&&(f=+k,f<g&&(c[f+b]=h[k],delete h[k]));e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);G(a,e);return a}function xc(){if(Ea)throw Error("carr");if(gb!=null){var a;var b=(a=Va)!=null?a:Va={};a=b[gb]||0;a>=5||(b[gb]=a+1,b=Error(),Ua(b,"incident"),Wa?Ya(b):za(b))}};function yc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=zc(a,c,!1,b&&!(c&16)):(mb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[hb]===qb)return b=a.i,c=b[F]|0,H(a,c)?a:Ac(a,b,c)?Bc(a,b):zc(b,c)}function Bc(a,b,c){a=new a.constructor(b);c&&(a.g=rb);a.j=rb;return a}function zc(a,b,c,d){d!=null||(d=!!(34&b));a=tc(a,b,yc,d);d=32;c&&(d|=2);b=b&8380609|d;G(a,b);return a}function Cc(a){var b=a.i,c=b[F]|0;return H(a,c)?Ac(a,b,c)?Bc(a,b,!0):new a.constructor(zc(b,c,!1)):a}function Dc(a){if(a.g!==rb)return!1;var b=a.i;b=zc(b,b[F]|0);mb(b,2048);a.i=b;a.g=void 0;a.j=void 0;return!0}function Ec(a){if(!Dc(a)&&H(a,a.i[F]|0))throw Error();}function Fc(a,b){b===void 0&&(b=a[F]|0);b&32&&!(b&4096)&&G(a,b|4096)}function Ac(a,b,c){return c&2?!0:c&32&&!(c&4096)?(G(b,c|2),a.g=rb,!0):!1};var Gc=Cb(0),M=function(a,b,c,d){a=Hc(a.i,b,c,d);if(a!==null)return a},Hc=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!v(Object,"is").call(Object,d,c))return h?g[b]=d:a[e]=d,d}return c}},O=function(a,b,c){Ec(a);var d=a.i;N(d,d[F]|0,b,c);return a};function N(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[F]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}var Jc=function(a,b,c){a=a.i;return Ic(a,a[F]|0,b,c)!==void 0},P=function(a){return a===tb?2:4};function Kc(a,b,c,d,e){var f=a.i,g=f[F]|0;d=H(a,g)?1:d;e=!!e||d===3;d===2&&Dc(a)&&(f=a.i,g=f[F]|0);a=Hc(f,b);a=Array.isArray(a)?a:kb;var h=a===kb?7:a[F]|0,k=Lc(h,g);var m=4&k?!1:!0;if(m){4&k&&(a=Pb(a),h=0,k=Mc(k,g),g=N(f,g,b,a));for(var n=0,l=0;n<a.length;n++){var p=c(a[n]);p!=null&&(a[l++]=p)}l<n&&(a.length=l);c=(k|4)&-513;k=c&=-1025;k&=-4097}k!==h&&(G(a,k),2&k&&Object.freeze(a));return a=Nc(a,k,f,g,b,d,m,e)}function Nc(a,b,c,d,e,f,g,h){var k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Oc(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==k&&G(a,b),Object.freeze(a)):(f===2&&Oc(b)&&(a=Pb(a),k=0,b=Mc(b,d),d=N(c,d,e,a)),Oc(b)||(h||(b|=16),b!==k&&G(a,b)));2&b||!(4096&b||16&b)||Fc(c,d);return a}function Lc(a,b){2&b&&(a|=2);return a|1}function Oc(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function Pc(a,b,c,d){Ec(a);var e=a.i,f=e[F]|0;if(c==null)return N(e,f,b),a;var g=c===kb?7:c[F]|0,h=g,k=Oc(g),m=k||Object.isFrozen(c);k||(g=0);m||(c=Pb(c),h=0,g=Mc(g,f),m=!1);g|=5;var n;k=(n=nb(g))!=null?n:0;for(n=0;n<c.length;n++){var l=c[n],p=d(l,k);v(Object,"is").call(Object,l,p)||(m&&(c=Pb(c),h=0,g=Mc(g,f),m=!1),c[n]=p)}g!==h&&(m&&(c=Pb(c),g=Mc(g,f)),G(c,g));N(e,f,b,c);return a}function Qc(a,b,c,d){Ec(a);var e=a.i;N(e,e[F]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}var Uc=function(a,b,c,d){Ec(a);var e=a.i,f=e[F]|0;if(d==null){var g=Rc(e);if(Sc(g,e,f,c)===b)g.set(c,0);else return a}else f=Tc(e,f,c,b);N(e,f,b,d);return a},Wc=function(a,b,c){return Vc(a,b)===c?c:-1},Vc=function(a,b){a=a.i;return Sc(Rc(a),a,void 0,b)};function Rc(a){if($a){var b;return(b=a[fb])!=null?b:a[fb]=new u.Map}if(fb in a)return a[fb];b=new u.Map;Object.defineProperty(a,fb,{value:b});return b}function Tc(a,b,c,d){var e=Rc(a),f=Sc(e,a,b,c);f!==d&&(f&&(b=N(a,b,f)),e.set(c,d));return b}function Sc(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var g=d[f];Hc(b,g)!=null&&(e!==0&&(c=N(b,c,e)),e=g)}a.set(d,e);return e}var Xc=function(a,b,c){Ec(a);a=a.i;var d=a[F]|0,e=Hc(a,c),f=void 0===ub;b=qc(e,b,!f,d);if(!f||b)return b=Cc(b),e!==b&&(d=N(a,d,c,b),Fc(a,d)),b};function Ic(a,b,c,d){var e=!1;d=Hc(a,d,void 0,function(f){var g=qc(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!H(d)&&Fc(a,b),d}var Yc=function(a,b,c){a=a.i;return Ic(a,a[F]|0,b,c)||b[eb]||(b[eb]=rc(b))},Q=function(a,b,c){var d=a.i,e=d[F]|0;b=Ic(d,e,b,c);if(b==null)return b;e=d[F]|0;if(!H(a,e)){var f=Cc(b);f!==b&&(Dc(a)&&(d=a.i,e=d[F]|0),b=f,e=N(d,e,c,b),Fc(d,e))}return b};function Zc(a,b,c,d,e,f,g,h){var k=H(a,c);f=k?1:f;g=!!g||f===3;k=h&&!k;(f===2||k)&&Dc(a)&&(b=a.i,c=b[F]|0);a=Hc(b,e);a=Array.isArray(a)?a:kb;var m=a===kb?7:a[F]|0,n=Lc(m,c);if(h=!(4&n)){var l=a,p=c,r=!!(2&n);r&&(p|=2);for(var t=!r,z=!0,A=0,pa=0;A<l.length;A++){var bb=qc(l[A],d,!1,p);if(bb instanceof d){if(!r){var cb=H(bb);t&&(t=!cb);z&&(z=cb)}l[pa++]=bb}}pa<A&&(l.length=pa);n|=4;n=z?n&-4097:n|4096;n=t?n|8:n&-9}n!==m&&(G(a,n),2&n&&Object.freeze(a));if(k&&!(8&n||!a.length&&(f===1||(f!==4?0:2&n||!(16&n)&&32&c)))){Oc(n)&&(a=Pb(a),n=Mc(n,c),c=N(b,c,e,a));d=a;k=n;for(m=0;m<d.length;m++)l=d[m],n=Cc(l),l!==n&&(d[m]=n);k|=8;n=k=d.length?k|4096:k&-4097;G(a,n)}return a=Nc(a,n,b,c,e,f,h,g)}var R=function(a,b,c,d){var e=a.i;return Zc(a,e,e[F]|0,b,c,d,!1,!0)};function $c(a){a==null&&(a=void 0);return a}var ad=function(a,b,c){c=$c(c);O(a,b,c);c&&!H(c)&&Fc(a.i);return a},bd=function(a,b,c,d){d=$c(d);Uc(a,b,c,d);d&&!H(d)&&Fc(a.i);return a},cd=function(a,b,c){Ec(a);var d=a.i,e=d[F]|0;if(c==null)return N(d,e,b),a;for(var f=c===kb?7:c[F]|0,g=f,h=Oc(f),k=h||Object.isFrozen(c),m=!0,n=!0,l=0;l<c.length;l++){var p=c[l];h||(p=H(p),m&&(m=!p),n&&(n=p))}h||(f=m?13:5,f=n?f&-4097:f|4096);k&&f===g||(c=Pb(c),g=0,f=Mc(f,e));f!==g&&G(c,f);e=N(d,e,b,c);2&f||!(4096&f||16&f)||Fc(d,e);return a};function Mc(a,b){return a=(2&b?a|2:a&-3)&-273}function dd(a,b){Ec(a);a=Kc(a,4,pc,2,!0);var c,d=(c=nb(a===kb?7:a[F]|0))!=null?c:0;if(Array.isArray(b)){c=b.length;for(var e=0;e<c;e++)a.push(nc(b[e],d))}else for(b=y(b),c=b.next();!c.done;c=b.next())a.push(nc(c.value,d))}var ed=function(a,b){var c=c===void 0?!1:c;a=M(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c},fd=function(a,b){var c=c===void 0?0:c;a=bc(M(a,b));return a!=null?a:c},gd=function(a,b){var c=c===void 0?0:c;a=cc(M(a,b));return a!=null?a:c},hd=function(a,b){var c=c===void 0?Gc:c;a=M(a,b);b=typeof a;a=a==null?a:b==="bigint"?Cb(Rb(64,a)):Yb(a)?b==="string"?hc(a):ic(a):void 0;return a!=null?a:c},id=function(a,b){var c=c===void 0?0:c;a=M(a,b,void 0,Vb);return a!=null?a:c},S=function(a,b){var c=c===void 0?"":c;var d;return(d=pc(M(a,b)))!=null?d:c},T=function(a,b){var c=c===void 0?0:c;a=$b(M(a,b));return a!=null?a:c},jd=function(a,b,c){a=Kc(a,b,bc,3,!0);sb(a,c);return a[c]},kd=function(a,b,c){return T(a,Wc(a,c,b))},ld=function(a,b,c){return Qc(a,b,c==null?c:ac(c),0)},md=function(a,b,c){return Qc(a,b,kc(c),"0")},nd=function(a,b,c){return Qc(a,b,oc(c),"")},od=function(a,b,c){return O(a,b,c==null?c:Zb(c))},pd=function(a,b,c){return Qc(a,b,c==null?c:Zb(c),0)},qd=function(a,b,c,d){return Uc(a,b,c,d==null?d:Zb(d))};var U=function(a,b,c){this.i=L(a,b,c)};U.prototype.toJSON=function(){return K(this)};var rd=function(a){var b=a.i,c=b[F]|0;return H(a,c)?a:Ac(a,b,c)?Bc(a,b):new a.constructor(zc(b,c,!0))};U.prototype[hb]=qb;function sd(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(ob(b))};function td(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(ob(b))}return b}};var ud=function(a){this.i=L(a)};x(ud,U);var vd=function(a){return S(a,1)};var wd=function(a){this.i=L(a)};x(wd,U);function xd(a){var b=b===void 0?!1:b;var c=c===void 0?D:c;for(var d=0;c&&d++<40;){var e;if(!(e=b))try{var f;if(f=!!c&&c.location.href!=null)b:{try{Pa(c.foo);f=!0;break b}catch(h){}f=!1}e=f}catch(h){e=!1}if(e&&a(c))break;a:{try{var g=c.parent;if(g&&g!=c){c=g;break a}}catch(h){}c=null}}}function yd(a){var b=a;xd(function(c){b=c;return!1});return b};var zd=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};var Ad=function(){return Da&&Fa?!Fa.mobile&&(E("iPad")||E("Android")||E("Silk")):E("iPad")||E("Android")&&!E("Mobile")||E("Silk")};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var Bd;function Cd(){Bd===void 0&&(Bd=null);return Bd};var Dd=function(a){this.g=a};Dd.prototype.toString=function(){return this.g+""};function Ed(a){var b=Cd();a=b?b.createScriptURL(a):a;return new Dd(a)}function Fd(a){if(a instanceof Dd)return a.g;throw Error("");};var Gd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;var Hd=function(a){this.g=a};Hd.prototype.toString=function(){return this.g+""};function Id(a){a=a===void 0?document:a;var b,c;a=(c=(b=a).querySelector)==null?void 0:c.call(b,"script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function Jd(a,b){a.src=Fd(b);(b=Id(a.ownerDocument))&&a.setAttribute("nonce",b)};var Kd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Ld(a,b){var c=a.write;if(b instanceof Hd)b=b.g;else throw Error("");c.call(a,b)};var Md=zd(function(){return(Da&&Fa?Fa.mobile:!Ad()&&(E("iPod")||E("iPhone")||E("Android")||E("IEMobile")))?2:Ad()?1:0});var Nd,Od=64;function Pd(){try{return Nd!=null||(Nd=new Uint32Array(64)),Od>=64&&(crypto.getRandomValues(Nd),Od=0),Nd[Od++]}catch(a){return Math.floor(Math.random()***********)}};function Qd(a,b){if(!yb(a.goog_pvsid))try{var c=Pd()+(Pd()&2097151)***********;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(d){b.G({methodName:784,I:d})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.G({methodName:784,I:Error("Invalid correlator, "+a)});return a||-1};function Rd(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};function Sd(a,b){a=Fd(a).toString();a='<script src="'+Td(a)+'"';if(b==null?0:b.async)a+=" async";(b==null?void 0:b.attributionSrc)!==void 0&&(a+=' attributionsrc="'+Td(b.attributionSrc)+'"');if(b==null?0:b.Ta)a+=' custom-element="'+Td(b.Ta)+'"';if(b==null?0:b.defer)a+=" defer";if(b==null?0:b.id)a+=' id="'+Td(b.id)+'"';if(b==null?0:b.nonce)a+=' nonce="'+Td(b.nonce)+'"';if(b==null?0:b.type)a+=' type="'+Td(b.type)+'"';if(b==null?0:b.Ea)a+=' crossorigin="'+Td(b.Ea)+'"';b=a+">\x3c/script>";b=(a=Cd())?a.createHTML(b):b;return new Hd(b)}function Td(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")};function Ud(a){var b=sa.apply(1,arguments);if(b.length===0)return Ed(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Ed(c)}function Vd(a,b){a=Fd(a).toString();var c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Wd(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)}function Wd(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(function(k){return e(k,h)}):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}var f=b.length?"&":"?";d.constructor===Object&&(d=v(Object,"entries").call(Object,d));Array.isArray(d)?d.forEach(function(g){return e(g[1],g[0])}):d.forEach(e);return Ed(a+b+c)};var Xd=function(){if(!u.globalThis.crypto)return Math.random();try{var a=new Uint32Array(1);u.globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch(b){return Math.random()}},Yd=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};function Zd(a,b){if(a.length&&b.head){a=y(a);for(var c=a.next();!c.done;c=a.next())if((c=c.value)&&b.head){var d=$d("META");b.head.appendChild(d);d.httpEquiv="origin-trial";d.content=c}}}var ae=function(a){return Qd(a,{G:function(){}})},$d=function(a,b){b=b===void 0?document:b;return b.createElement(String(a).toLowerCase())};var be={Cb:0,Bb:1,yb:2,sb:3,zb:4,tb:5,Ab:6,wb:7,xb:8,rb:9,ub:10,Db:11};var ce={Fb:0,Gb:1,Eb:2};var de=function(a){this.i=L(a)};x(de,U);de.prototype.getVersion=function(){return fd(this,2)};function ee(a){return Sa(a.length%4!==0?a+"A":a).map(function(b){return(q=b.toString(2),v(q,"padStart")).call(q,8,"0")}).join("")}function fe(a){if(!/^[0-1]+$/.test(a))throw Error("Invalid input ["+a+"] not a bit string.");return parseInt(a,2)}function ge(a){if(!/^[0-1]+$/.test(a))throw Error("Invalid input ["+a+"] not a bit string.");for(var b=[1,2,3,5],c=0,d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function he(a){var b=ee(a),c=fe(b.slice(0,6));a=fe(b.slice(6,12));var d=new de;c=ld(d,1,c);a=ld(c,2,a);b=b.slice(12);c=fe(b.slice(0,12));d=[];for(var e=b.slice(12).replace(/0+$/,""),f=0;f<c;f++){if(e.length===0)throw Error("Found "+f+" of "+c+" sections ["+d+"] but reached end of input ["+b+"]");var g=fe(e[0])===0;e=e.slice(1);var h=ie(e,b),k=d.length===0?0:d[d.length-1];k=ge(h)+k;e=e.slice(h.length);if(g)d.push(k);else{g=ie(e,b);h=ge(g);for(var m=0;m<=h;m++)d.push(k+m);e=e.slice(g.length)}}if(e.length>0)throw Error("Found "+c+" sections ["+d+"] but has remaining input ["+e+"], entire input ["+b+"]");return Pc(a,3,d,ac)}function ie(a,b){var c=a.indexOf("11");if(c===-1)throw Error("Expected section bitstring but not found in ["+a+"] part of ["+b+"]");return a.slice(0,c+2)};var je="a".charCodeAt(),ke=Rd(be),le=Rd(ce);var me=function(a){this.i=L(a)};x(me,U);var ne=function(){var a=new me;return md(a,1,0)},oe=function(a){var b=Number;var c=c===void 0?"0":c;var d=M(a,1);var e=!0;e=e===void 0?!1:e;var f=typeof d;d=d==null?d:f==="bigint"?String(Rb(64,d)):Yb(d)?f==="string"?ec(d):e?fc(d):jc(d):void 0;b=b(d!=null?d:c);a=fd(a,2);return new Date(b*1E3+a/1E6)};var pe=function(a){if(/[^01]/.test(a))throw Error("Input bitstring "+a+" is malformed!");this.j=a;this.g=0},se=function(a){var b=V(a,16);return!!V(a,1)===!0?(a=qe(a),a.forEach(function(c){if(c>b)throw Error("ID "+c+" is past MaxVendorId "+b+"!");}),a):re(a,b)},qe=function(a){for(var b=V(a,12),c=[];b--;){var d=!!V(a,1)===!0,e=V(a,16);if(d)for(d=V(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort(function(f,g){return f-g});return c},re=function(a,b,c){for(var d=[],e=0;e<b;e++)if(V(a,1)){var f=e+1;if(c&&c.indexOf(f)===-1)throw Error("ID: "+f+" is outside of allowed values!");d.push(f)}return d},V=function(a,b){if(a.g+b>a.j.length)throw Error("Requested length "+b+" is past end of string.");var c=a.j.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)};pe.prototype.skip=function(a){this.g+=a};var ue=function(a){try{var b=Sa(a.split(".")[0]).map(function(d){return(q=d.toString(2),v(q,"padStart")).call(q,8,"0")}).join(""),c=new pe(b);b={};b.tcString=a;b.gdprApplies=!0;c.skip(78);b.cmpId=V(c,12);b.cmpVersion=V(c,12);c.skip(30);b.tcfPolicyVersion=V(c,6);b.isServiceSpecific=!!V(c,1);b.useNonStandardStacks=!!V(c,1);b.specialFeatureOptins=te(re(c,12,le),le);b.purpose={consents:te(re(c,24,ke),ke),legitimateInterests:te(re(c,24,ke),ke)};b.purposeOneTreatment=!!V(c,1);b.publisherCC=String.fromCharCode(je+V(c,6))+String.fromCharCode(je+V(c,6));b.vendor={consents:te(se(c),null),legitimateInterests:te(se(c),null)};return b}catch(d){return null}},te=function(a,b){var c={};if(Array.isArray(b)&&b.length!==0){b=y(b);for(var d=b.next();!d.done;d=b.next())d=d.value,c[d]=a.indexOf(d)!==-1}else for(a=y(a),b=a.next();!b.done;b=a.next())c[b.value]=!0;delete c[0];return c};var ve=function(a){this.i=L(a)};x(ve,U);var we=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};function xe(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=$d("IMG",a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=Array.prototype.indexOf.call(g,e,void 0);h>=0&&Array.prototype.splice.call(g,h,1)}typeof e.removeEventListener==="function"&&e.removeEventListener("load",f,!1);typeof e.removeEventListener==="function"&&e.removeEventListener("error",f,!1)};typeof e.addEventListener==="function"&&e.addEventListener("load",f,!1);typeof e.addEventListener==="function"&&e.addEventListener("error",f,!1)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}function ye(a){var b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Yd(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});ze(c,b)}function ze(a,b){var c=window;b=b===void 0?!1:b;var d=d===void 0?!1:d;c.fetch?(b={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"},d&&(b.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?b.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:b.headers={"Attribution-Reporting-Eligible":"event-source"}),c.fetch(a,b)):xe(c,a,b===void 0?!1:b,d===void 0?!1:d)};function Ae(a,b){try{var c=function(d){var e={};return[(e[d.aa]=d.X,e)]};return JSON.stringify([a.filter(function(d){return d.N}).map(c),K(b),a.filter(function(d){return!d.N}).map(c)])}catch(d){return Be(d,b),""}}function Be(a,b){try{var c=a instanceof Error?a:Error(String(a)),d=c.toString();c.name&&d.indexOf(c.name)==-1&&(d+=": "+c.name);c.message&&d.indexOf(c.message)==-1&&(d+=": "+c.message);if(c.stack)a:{var e=c.stack;a=d;try{e.indexOf(a)==-1&&(e=a+"\n"+e);for(var f;e!=f;)f=e,e=e.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");d=e.replace(RegExp("\n *","g"),"\n");break a}catch(g){d=a;break a}d=void 0}ye({m:d,b:T(b,1)||null,v:S(b,2)||null})}catch(g){}}var Ce=function(a,b){var c=new ve;a=pd(c,1,a);b=nd(a,2,b);this.o=rd(b)};var De=function(a){this.i=L(a)};x(De,U);var Fe=function(a,b){return Uc(a,3,Ee,b==null?b:Wb(b))},Ee=[1,2,3];var Ge=function(a){this.i=L(a)};x(Ge,U);var Ie=function(a,b){return Uc(a,2,He,kc(b))},He=[2,4];var Je=function(a){this.i=L(a)};x(Je,U);var Ke=function(a){var b=new Je;return nd(b,1,a)},Le=function(a,b){return ad(a,3,b)},Me=function(a,b){var c=b;Ec(a);b=a.i;var d=Zc(a,b,b[F]|0,De,4,2,!0);c=c!=null?c:new De;d.push(c);var e=d===kb?7:d[F]|0,f=e;(c=H(c))?(e&=-9,d.length===1&&(e&=-4097)):e|=4096;e!==f&&G(d,e);c||Fc(b);return a};var Ne=function(a){this.i=L(a)};x(Ne,U);var Oe=function(a){this.i=L(a)};x(Oe,U);var Pe=function(a,b){return pd(a,1,b)},Qe=function(a,b){return pd(a,2,b)};var Re=function(a){this.i=L(a)};x(Re,U);var Se=[1,2];var Te=function(a){this.i=L(a)};x(Te,U);var Ue=function(a,b){return ad(a,1,b)},Ve=function(a,b){return cd(a,2,b)},We=function(a,b){return Pc(a,4,b,ac)},Xe=function(a,b){return cd(a,5,b)},Ye=function(a,b){return pd(a,6,b)};var Ze=function(a){this.i=L(a)};x(Ze,U);var $e=[1,2,3,4,6];var af=function(a){this.i=L(a)};x(af,U);var bf=function(a){this.i=L(a)};x(bf,U);var cf=[2,3,4];var df=function(a){this.i=L(a)};x(df,U);var ef=[3,4,5],ff=[6,7];var gf=function(a){this.i=L(a)};x(gf,U);var hf=[4,5];var jf=function(a){this.i=L(a)};x(jf,U);jf.prototype.getTagSessionCorrelator=function(){return hd(this,2)};var lf=function(a){var b=new jf;return bd(b,4,kf,a)},kf=[4,5,7,8,9];var mf=function(a){this.i=L(a)};x(mf,U);var nf=function(a){this.i=L(a)};x(nf,U);var of=[1,2,4,5,6,9,10,11];var pf=function(a){this.i=L(a)};x(pf,U);pf.prototype.getTagSessionCorrelator=function(){return hd(this,2)};pf.prototype.ca=function(a){return jd(this,4,a)};var qf=function(a){this.i=L(a)};x(qf,U);qf.prototype.Ya=function(){return fd(this,2)};qf.prototype.Xa=function(a){var b=Kc(this,3,pc,3,!0);sb(b,a);return b[a]};var rf=function(a){this.i=L(a)};x(rf,U);var sf=function(a){this.i=L(a)};x(sf,U);sf.prototype.getTagSessionCorrelator=function(){return hd(this,1)};sf.prototype.ca=function(a){return jd(this,2,a)};var tf=function(a){this.i=L(a)};x(tf,U);var uf=[1,7],vf=[4,6,8];var xf=function(a){this.g=a;this.Qa=new wf(this.g)},wf=function(a){this.g=a;this.Ga=new yf(this.g)},yf=function(a){this.g=a;this.outstream=new zf;this.request=new Af;this.threadYield=new Bf;this.ab=new Cf(this.g);this.eb=new Df(this.g);this.lb=new Ef(this.g)},Cf=function(a){this.g=a};Cf.prototype.W=function(a){this.g.C(Le(Me(Me(Ke("JwITQ"),Fe(new De,a.la)),Fe(new De,a.na)),Ie(new Ge,Math.round(a.Z))))};var Df=function(a){this.g=a};Df.prototype.W=function(a){this.g.C(Le(Me(Me(Ke("Pn3Upd"),Fe(new De,a.la)),Fe(new De,a.na)),Ie(new Ge,Math.round(a.Z))))};var Ef=function(a){this.g=a};Ef.prototype.W=function(a){var b=this.g,c=b.C,d=Ke("rkgGzc");var e=new De;e=Uc(e,2,Ee,kc(a.source));d=Me(d,e);e=new De;e=Uc(e,2,Ee,kc(a.Sa));c.call(b,Le(Me(d,e),Ie(new Ge,Math.round(a.Z))))};var zf=function(){},Af=function(){},Bf=function(){},Ff=function(){Ce.apply(this,arguments);this.Ja=new xf(this)};x(Ff,Ce);var Gf=function(){Ff.apply(this,arguments)};x(Gf,Ff);Gf.prototype.pb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:2,X:K(a)}})))};Gf.prototype.ob=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:29,X:K(a)}})))};Gf.prototype.ea=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:4,X:K(a)}})))};Gf.prototype.qb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:15,X:K(a)}})))};Gf.prototype.C=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!1,aa:1,X:K(a)}})))};function Hf(a,b){if(u.globalThis.fetch)u.globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(function(){});else{var c=new XMLHttpRequest;c.open("POST",a,!0);c.send(b)}};var If=function(a,b,c,d,e,f,g,h){Gf.call(this,a,b);this.T=c;this.S=d;this.U=e;this.P=f;this.R=g;this.J=h;this.g=[];this.j=null;this.L=!1};x(If,Gf);var Jf=function(a){a.j!==null&&(clearTimeout(a.j),a.j=null);if(a.g.length){var b=Ae(a.g,a.o);a.S(a.T+"?e=1",b);a.g=[]}};If.prototype.l=function(){var a=sa.apply(0,arguments),b=this;try{this.R&&Ae(this.g.concat(a),this.o).length>=65536&&Jf(this),this.J&&!this.L&&(this.L=!0,this.J.g(function(){Jf(b)})),this.g.push.apply(this.g,B(a)),this.g.length>=this.P&&Jf(this),this.g.length&&this.j===null&&(this.j=setTimeout(function(){Jf(b)},this.U))}catch(c){Be(c,this.o)}};var Kf=function(a,b,c,d,e,f){If.call(this,a,b,"https://pagead2.googlesyndication.com/pagead/ping",Hf,c===void 0?1E3:c,d===void 0?100:d,(e===void 0?!1:e)&&!!u.globalThis.fetch,f)};x(Kf,If);var Lf=function(a){this.g=a;this.defaultValue=!1},Mf=function(a,b){this.g=a;this.defaultValue=b===void 0?0:b};var Nf=new Mf(695925491,20),Of=new Lf(45624259),Pf=new Mf(635239304,100),Qf=new Lf(662101539),Rf=new Mf(682056200,100),Sf=new Mf(24),Tf=new function(a,b){b=b===void 0?[]:b;this.g=a;this.defaultValue=b}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9nrunKdU5m96PSN1XsSGr3qOP0lvPFUB2AiAylCDlN5DTl17uDFkpQuHj1AFtgWLxpLaiBZuhrtb2WOu7ofHwEAAACKeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","A93bovR+QVXNx2/38qDbmeYYf1wdte9EO37K9eMq3r+541qo0byhYU899BhPB7Cv9QqD7wIbR1B6OAc9kEfYCA4AAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","A1S5fojrAunSDrFbD8OfGmFHdRFZymSM/1ss3G+NEttCLfHkXvlcF6LGLH8Mo5PakLO1sCASXU1/gQf6XGuTBgwAAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXRhZ3NlcnZpY2VzLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]);var Uf=function(a){this.i=L(a)};x(Uf,U);var Vf=function(a){this.i=L(a)};x(Vf,U);var Wf=function(a){this.i=L(a)};x(Wf,U);var Xf=function(a){this.i=L(a)};x(Xf,U);var Yf=td(Xf);var Zf=function(a){this.g=a||{cookie:""}};Zf.prototype.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Jb;d=c.Kb||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.gb}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');h===void 0&&(h=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(h<0?"":h==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+h*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};Zf.prototype.get=function(a,b){for(var c=a+"=",d=(this.g.cookie||"").split(";"),e=0,f;e<d.length;e++){f=Aa(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};Zf.prototype.isEmpty=function(){return!this.g.cookie};Zf.prototype.clear=function(){for(var a=(this.g.cookie||"").split(";"),b=[],c=[],d,e,f=0;f<a.length;f++)e=Aa(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(a=b.length-1;a>=0;a--)c=b[a],this.get(c),this.set(c,"",{gb:0,path:void 0,domain:void 0})};function $f(a){a=ag(a);try{var b=a?Yf(a):null}catch(c){b=null}return b?Q(b,Wf,4)||null:null}function ag(a){a=(new Zf(a)).get("FCCDCF","");if(a)if(v(a,"startsWith").call(a,"%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};Rd(be).map(function(a){return Number(a)});Rd(ce).map(function(a){return Number(a)});var bg=function(a){this.g=a},dg=function(a){a.__tcfapiPostMessageReady||cg(new bg(a))},cg=function(a){a.j=function(b){var c=typeof b.data==="string";try{var d=c?JSON.parse(b.data):b.data}catch(f){return}var e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.g.__tcfapi)(e.command,e.version,function(f,g){var h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f},e.parameter)};a.g.addEventListener("message",a.j);a.g.__tcfapiPostMessageReady=!0};var eg=function(a){this.g=a;this.j=null},gg=function(a){a.__uspapiPostMessageReady||fg(new eg(a))},fg=function(a){a.j=function(b){var c=typeof b.data==="string";try{var d=c?JSON.parse(b.data):b.data}catch(f){return}var e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.g.__uspapi(e.command,e.version,function(f,g){var h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.g.addEventListener("message",a.j);a.g.__uspapiPostMessageReady=!0};var hg=function(a){this.i=L(a)};x(hg,U);var ig=function(a){this.i=L(a)};x(ig,U);var jg=td(ig);function kg(a,b){function c(l){if(l.length<10)return null;var p=h(l.slice(0,4));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function d(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function e(l){if(l.length<12)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(8,12));l=m(l);return"1"+p+l+"N"}function f(l){if(l.length<18)return null;var p=h(l.slice(0,8));p=k(p);l=h(l.slice(12,18));l=m(l);return"1"+p+l+"N"}function g(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function h(l){for(var p=[],r=0,t=0;t<l.length/2;t++)p.push(fe(l.slice(r,r+2))),r+=2;return p}function k(l){return l.every(function(p){return p===1})?"Y":"N"}function m(l){return l.some(function(p){return p===1})?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=ee(a[0]);var n=fe(a.slice(0,6));a=a.slice(6);if(n!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a);case 13:return g(a);default:return null}};function lg(a,b){var c=a.document,d=function(){if(!a.frames[b])if(c.body){var e=$d("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function mg(a){if(a!=null)return ng(a)}function ng(a){return Ib(a)?Number(a):String(a)};var qg=function(a){this.g=a;var b=ag(this.g.document);try{var c=b?Yf(b):null}catch(e){c=null}(b=c)?(c=Q(b,Vf,5)||null,b=R(b,Uf,7,P()),b=og(b!=null?b:[]),c={Da:c,Fa:b}):c={Da:null,Fa:null};b=c;c=pg(b.Fa);b=b.Da;if(b!=null&&pc(M(b,2))!=null&&S(b,2).length!==0){var d=Jc(b,me,1)?Q(b,me,1):ne();b={uspString:S(b,2),ia:oe(d)}}else b=null;this.l=b&&c?c.ia>b.ia?c.uspString:b.uspString:b?b.uspString:c?c.uspString:null;this.tcString=(c=$f(a.document))&&pc(M(c,1))!=null?S(c,1):null;this.j=(a=$f(a.document))&&pc(M(a,2))!=null?S(a,2):null},tg=function(a){a===a.top&&(a=new qg(a),rg(a),sg(a))},rg=function(a){!a.l||a.g.__uspapi||a.g.frames.__uspapiLocator||(a.g.__uspapiManager="fc",lg(a.g,"__uspapiLocator"),ya("__uspapi",function(b,c,d){typeof d==="function"&&b==="getUSPData"&&d({version:1,uspString:a.l},!0)},a.g),gg(a.g))},og=function(a){a=v(a,"find").call(a,function(b){return b&&T(b,1)===13});if(a==null?0:pc(M(a,2))!=null)try{return jg(S(a,2))}catch(b){}return null},pg=function(a){if(a==null||pc(M(a,1))==null||S(a,1).length===0||R(a,hg,2,P()).length===0)return null;var b=S(a,1);try{var c=he(b.split("~")[0]);var d=v(b,"includes").call(b,"~")?b.split("~").slice(1):[]}catch(e){return null}a=R(a,hg,2,P()).reduce(function(e,f){var g=ug(e);g=hd(g,1);g=ng(g);var h=ug(f);h=hd(h,1);return g>ng(h)?e:f});c=Kc(c,3,bc,P()).indexOf(fd(a,1));return c===-1||c>=d.length?null:{uspString:kg(d[c],fd(a,1)),ia:oe(ug(a))}},ug=function(a){return Jc(a,me,2)?Q(a,me,2):ne()},sg=function(a){!a.tcString||a.g.__tcfapi||a.g.frames.__tcfapiLocator||(a.g.__tcfapiManager="fc",lg(a.g,"__tcfapiLocator"),a.g.__tcfapiEventListeners=a.g.__tcfapiEventListeners||[],ya("__tcfapi",function(b,c,d,e){if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else switch(c=a.g.__tcfapiEventListeners,b){case "ping":d({gdprApplies:!0,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":b=c.push(d)-1;a.tcString?(e=ue(a.tcString),e.addtlConsent=a.j!=null?a.j:void 0,e.cmpStatus="loaded",e.eventStatus="tcloaded",b!=null&&(e.listenerId=b),b=e):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&c[e]?(c[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}},a.g),dg(a.g))};var vg=qa(["https://pagead2.googlesyndication.com/pagead/managed/dict/","/gpt"]),wg=qa(["https://securepubads.g.doubleclick.net/pagead/managed/dict/","/gpt"]);function xg(a,b,c){try{var d=a.createElement("link"),e,f;if(((e=d.relList)==null?0:(f=e.supports)==null?0:f.call(e,"compression-dictionary"))&&Ma()){if(b instanceof Dd)d.href=Fd(b).toString(),d.rel="compression-dictionary";else{if(Kd.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var g=Gd.test(b)?b:void 0;g!==void 0&&(d.href=g,d.rel="compression-dictionary")}a.head.appendChild(d)}}catch(h){c.G({methodName:1296,I:h})}}function yg(a,b){return b?Ud(vg,a):Ud(wg,a)};var zg=null;function Ag(a,b){var c=R(a,df,2,P());if(!c.length)return Bg(a,b);a=T(a,1);if(a===1){var d=Ag(c[0],b);return d.success?{success:!0,value:!d.value}:d}c=Na(c,function(h){return Ag(h,b)});switch(a){case 2:var e;return(e=(d=v(c,"find").call(c,function(h){return h.success&&!h.value}))!=null?d:v(c,"find").call(c,function(h){return!h.success}))!=null?e:{success:!0,value:!0};case 3:var f,g;return(g=(f=v(c,"find").call(c,function(h){return h.success&&h.value}))!=null?f:v(c,"find").call(c,function(h){return!h.success}))!=null?g:{success:!0,value:!1};default:return{success:!1,B:3}}}function Bg(a,b){var c=Vc(a,ef);a:{switch(c){case 3:var d=kd(a,3,ef);break a;case 4:d=kd(a,4,ef);break a;case 5:d=kd(a,5,ef);break a}d=void 0}if(!d)return{success:!1,B:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,O:d,Y:c,B:1};try{var e=b.apply;var f=Kc(a,8,pc,P());var g=e.call(b,null,B(f))}catch(h){return{success:!1,O:d,Y:c,B:2}}e=T(a,1);if(e===4)return{success:!0,value:!!g};if(e===5)return{success:!0,value:g!=null};if(e===12)a=S(a,Wc(a,ff,7));else a:{switch(c){case 4:a=id(a,Wc(a,ff,6));break a;case 5:a=S(a,Wc(a,ff,7));break a}a=void 0}if(a==null)return{success:!1,O:d,Y:c,B:3};if(e===6)return{success:!0,value:g===a};if(e===9)return{success:!0,value:g!=null&&Ba(String(g),a)===0};if(g==null)return{success:!1,O:d,Y:c,B:4};switch(e){case 7:c=g<a;break;case 8:c=g>a;break;case 12:c=zb(a)&&zb(g)&&(new RegExp(a)).test(g);break;case 10:c=g!=null&&Ba(String(g),a)===-1;break;case 11:c=g!=null&&Ba(String(g),a)===1;break;default:return{success:!1,B:3}}return{success:!0,value:c}}function Cg(a,b){return a?b?Ag(a,b):{success:!1,B:1}:{success:!0,value:!0}};var Dg=function(a){this.i=L(a)};x(Dg,U);var Eg=function(a){return Kc(a,4,pc,P())};var Fg=function(a){this.i=L(a)};x(Fg,U);Fg.prototype.getValue=function(){return Q(this,Dg,2)};var Gg=function(a){this.i=L(a)};x(Gg,U);var Hg=td(Gg),Ig=[1,2,3,6,7,8];var Jg=function(a,b,c){var d=d===void 0?new Kf(6,"unknown",b):d;this.C=a;this.o=c;this.j=d;this.g=[];this.l=a>0&&Xd()<1/a},Lg=function(a,b,c,d,e,f){if(a.l){var g=Qe(Pe(new Oe,b),c);b=Ye(Ve(Ue(Xe(We(new Te,d),e),g),a.g.slice()),f);b=lf(b);a.j.ea(Kg(a,b));if(f===1||f===3||f===4&&!a.g.some(function(h){return T(h,1)===T(g,1)&&T(h,2)===c}))a.g.push(g),a.g.length>100&&a.g.shift()}},Mg=function(a,b,c,d){if(a.l){var e=new Ne;b=O(e,1,b==null?b:ac(b));c=O(b,2,c==null?c:ac(c));d=od(c,3,d);c=new jf;d=bd(c,8,kf,d);a.j.ea(Kg(a,d))}},Ng=function(a,b,c,d,e){if(a.l){var f=new gf;b=ad(f,1,b);c=od(b,2,c);d=O(c,3,d==null?d:ac(d));if(e.Y===void 0)qd(d,4,hf,e.B);else switch(e.Y){case 3:c=new bf;c=qd(c,2,cf,e.O);e=od(c,1,e.B);bd(d,5,hf,e);break;case 4:c=new bf;c=qd(c,3,cf,e.O);e=od(c,1,e.B);bd(d,5,hf,e);break;case 5:c=new bf,c=qd(c,4,cf,e.O),e=od(c,1,e.B),bd(d,5,hf,e)}e=new jf;e=bd(e,9,kf,d);a.j.ea(Kg(a,e))}},Kg=function(a,b){var c=Date.now();c=v(Number,"isFinite").call(Number,c)?Math.round(c):0;b=md(b,1,c);c=ae(window);b=md(b,2,c);return md(b,6,a.C)};var W=function(a){var b="ka";if(a.ka&&a.hasOwnProperty(b))return a.ka;b=new a;return a.ka=b};var Og=function(){var a={};this.A=(a[3]={},a[4]={},a[5]={},a)};var Pg=/^true$/.test("false");function Qg(a,b){switch(b){case 1:return kd(a,1,Ig);case 2:return kd(a,2,Ig);case 3:return kd(a,3,Ig);case 6:return kd(a,6,Ig);case 8:return kd(a,8,Ig);default:return null}}function Rg(a,b){if(!a)return null;switch(b){case 1:return ed(a,1);case 7:return S(a,3);case 2:return id(a,2);case 3:return S(a,3);case 6:return Eg(a);case 8:return Eg(a);default:return null}}var Sg=zd(function(){if(!Pg)return{};try{var a=a===void 0?window:a;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch(c){b=null}if(b)return JSON.parse(b)}catch(c){}return{}});function Tg(a,b,c,d){var e=d=d===void 0?0:d,f,g;W(Ug).l[e]=(g=(f=W(Ug).l[e])==null?void 0:f.add(b))!=null?g:(new u.Set).add(b);e=Sg();if(e[b]!=null)return e[b];b=Vg(d)[b];if(!b)return c;b=Hg(JSON.stringify(b));b=Wg(b);a=Rg(b,a);return a!=null?a:c}function Wg(a){var b=W(Og).A;if(b&&Vc(a,Ig)!==8){var c=Oa(R(a,Fg,5,P()),function(f){f=Cg(Q(f,df,1),b);return f.success&&f.value});if(c){var d;return(d=c.getValue())!=null?d:null}}var e;return(e=Q(a,Dg,4))!=null?e:null}var Ug=function(){this.j={};this.o=[];this.l={};this.g=new u.Map};function Xg(a,b,c){return!!Tg(1,a,b===void 0?!1:b,c)}function Yg(a,b,c){b=b===void 0?0:b;a=Number(Tg(2,a,b,c));return isNaN(a)?b:a}function Zg(a,b,c){b=b===void 0?"":b;a=Tg(3,a,b,c);return typeof a==="string"?a:b}function $g(a,b,c){b=b===void 0?[]:b;a=Tg(6,a,b,c);return Array.isArray(a)?a:b}function ah(a,b,c){b=b===void 0?[]:b;a=Tg(8,a,b,c);return Array.isArray(a)?a:b}function Vg(a){return W(Ug).j[a]||(W(Ug).j[a]={})}function bh(a,b){var c=Vg(b);Yd(a,function(d,e){if(c[e]){d=Hg(JSON.stringify(d));var f=Wc(d,Ig,8);if($b(M(d,f))!=null){var g=Hg(JSON.stringify(c[e]));f=Xc(d,Dg,4);g=Eg(Yc(g,Dg,4));dd(f,g)}c[e]=K(d)}else c[e]=d})}function ch(a,b,c,d,e){e=e===void 0?!1:e;var f=[],g=[];b=y(b);for(var h=b.next();!h.done;h=b.next()){h=h.value;for(var k=Vg(h),m=y(a),n=m.next();!n.done;n=m.next()){n=n.value;var l=Vc(n,Ig),p=Qg(n,l);if(p){var r=void 0,t=void 0,z=void 0;var A=(r=(z=W(Ug).g.get(h))==null?void 0:(t=z.get(p))==null?void 0:t.slice(0))!=null?r:[];a:{r=p;t=l;z=new Ze;switch(t){case 1:qd(z,1,$e,r);break;case 2:qd(z,2,$e,r);break;case 3:qd(z,3,$e,r);break;case 6:qd(z,4,$e,r);break;case 8:qd(z,6,$e,r);break;default:A=void 0;break a}Pc(z,5,A,ac);A=z}if(r=A)t=void 0,r=!((t=W(Ug).l[h])==null||!t.has(p));r&&f.push(A);if(l===8&&k[p])A=Hg(JSON.stringify(k[p])),l=Xc(n,Dg,4),A=Eg(Yc(A,Dg,4)),dd(l,A);else{if(l=A)r=void 0,l=!((r=W(Ug).g.get(h))==null||!r.has(p));l&&g.push(A)}e||(l=p,A=h,r=d,t=W(Ug),t.g.has(A)||t.g.set(A,new u.Map),t.g.get(A).has(l)||t.g.get(A).set(l,[]),r&&t.g.get(A).get(l).push(r));k[p]=K(n)}}}if(f.length||g.length)a=d!=null?d:void 0,c.l&&c.o&&(d=new af,f=cd(d,2,f),g=cd(f,3,g),a&&ld(g,1,a),f=new jf,g=bd(f,7,kf,g),c.j.ea(Kg(c,g)))}function dh(a,b){b=Vg(b);a=y(a);for(var c=a.next();!c.done;c=a.next()){c=c.value;var d=Hg(JSON.stringify(c)),e=Vc(d,Ig);(d=Qg(d,e))&&(b[d]||(b[d]=c))}}function eh(){return v(Object,"keys").call(Object,W(Ug).j).map(function(a){return Number(a)})}function fh(a){(q=W(Ug).o,v(q,"includes")).call(q,a)||bh(Vg(4),a)};function X(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Y(a,b,c){return b[a]||c}function gh(a){X(5,Xg,a);X(6,Yg,a);X(7,Zg,a);X(8,$g,a);X(17,ah,a);X(13,dh,a);X(15,fh,a)}function hh(a){X(4,function(b){W(Og).A=b},a);X(9,function(b,c){var d=W(Og);d.A[3][b]==null&&(d.A[3][b]=c)},a);X(10,function(b,c){var d=W(Og);d.A[4][b]==null&&(d.A[4][b]=c)},a);X(11,function(b,c){var d=W(Og);d.A[5][b]==null&&(d.A[5][b]=c)},a);X(14,function(b){for(var c=W(Og),d=y([3,4,5]),e=d.next();!e.done;e=d.next())e=e.value,v(Object,"assign").call(Object,c.A[e],b[e])},a)}function ih(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};var jh=function(){};jh.prototype.g=function(){};jh.prototype.j=function(){};jh.prototype.l=function(){return[]};var kh=function(a,b,c){a.j=function(d,e){Y(2,b,function(){return[]})(d,c,e)};a.l=function(d){return Y(3,b,function(){return[]})(d!=null?d:c)};a.g=function(d){Y(16,b,function(){})(d,c)}};function lh(a){W(jh).g(a)}function mh(a){return W(jh).l(a)};function nh(a,b){try{var c=a.split(".");a=D;for(var d=0,e;a!=null&&d<c.length;d++)e=a,a=a[c[d]],typeof a==="function"&&(a=e[c[d]]());var f=a;if(typeof f===b)return f}catch(g){}}var oh={},ph={},qh={},rh={},sh=(rh[3]=(oh[8]=function(a){try{return va(a)!=null}catch(b){}},oh[9]=function(a){try{var b=va(a)}catch(c){return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},oh[10]=function(){return window===window.top},oh[6]=function(a,b){b=mh(b?Number(b):void 0);return Array.prototype.indexOf.call(b,Number(a),void 0)>=0},oh[27]=function(a){a=nh(a,"boolean");return a!==void 0?a:void 0},oh[60]=function(a){try{return!!D.document.querySelector(a)}catch(b){}},oh[80]=function(a){try{return!!D.matchMedia(a).matches}catch(b){}},oh[69]=function(a){var b=D.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!(q=c.features(),v(q,"includes")).call(q,a))},oh[70]=function(a){var b=D.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!(q=c.allowedFeatures(),v(q,"includes")).call(q,a))},oh[79]=function(a){var b=D.navigator;b=b===void 0?navigator:b;try{var c,d;var e=!!((c=b.protectedAudience)==null?0:(d=c.queryFeatureSupport)==null?0:d.call(c,a))}catch(f){e=!1}return e},oh),rh[4]=(ph[3]=function(){return Md()},ph[6]=function(a){a=nh(a,"number");return a!==void 0?a:void 0},ph),rh[5]=(qh[2]=function(){return window.location.href},qh[3]=function(){try{return window.top.location.hash}catch(a){return""}},qh[4]=function(a){a=nh(a,"string");return a!==void 0?a:void 0},qh[12]=function(a){try{var b=nh(a,"string");if(b!==void 0)return atob(b)}catch(c){}},qh),rh);function th(){var a=a===void 0?D:a;return a.ggeac||(a.ggeac={})};var uh=function(a){this.i=L(a)};x(uh,U);uh.prototype.getId=function(){return fd(this,1)};var vh=function(a){this.i=L(a)};x(vh,U);var wh=function(a){return R(a,uh,2,P())};var xh=function(a){this.i=L(a)};x(xh,U);var yh=function(a){this.i=L(a)};x(yh,U);var zh=function(a){this.i=L(a)};x(zh,U);function Ah(a){var b={};return Bh((b[0]=new u.Map,b[1]=new u.Map,b[2]=new u.Map,b),a)}function Bh(a,b){for(var c=new u.Map,d=y(v(a[1],"entries").call(a[1])),e=d.next();!e.done;e=d.next()){var f=y(e.value);e=f.next().value;f=f.next().value;f=f[f.length-1];c.set(e,f.Pa+f.Ka*f.La)}b=y(b);for(d=b.next();!d.done;d=b.next())for(d=d.value,e=R(d,vh,2,P()),e=y(e),f=e.next();!f.done;f=e.next())if(f=f.value,wh(f).length!==0){var g=gd(f,8);if(T(f,4)&&!T(f,13)&&!T(f,14)){var h=void 0;g=(h=c.get(T(f,4)))!=null?h:0;h=gd(f,1)*wh(f).length;c.set(T(f,4),g+h)}h=[];for(var k=0;k<wh(f).length;k++){var m={Pa:g,Ka:gd(f,1),La:wh(f).length,hb:k,ba:T(d,1),fa:f,F:wh(f)[k]};h.push(m)}Ch(a[2],T(f,10),h)||Ch(a[1],T(f,4),h)||Ch(a[0],wh(f)[0].getId(),h)}return a}function Ch(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);var d;(d=a.get(b)).push.apply(d,B(c));return!0};var Dh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Eh=function(a){return a?decodeURI(a):a},Fh=/#|$/,Gh=function(a,b){var c=a.search(Fh);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Hh(a){var b=a.length;if(b===0)return 0;for(var c=305419896,d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:**********+c};function Ih(){var a=ae(window);a=a===void 0?Xd():a;return function(b){return Hh(b+" + "+a)%1E3}};var Jh=[12,13,20],Kh=function(a,b,c,d){d=d===void 0?{}:d;var e=d.ja===void 0?!1:d.ja;d=d.nb===void 0?[]:d.nb;this.M=a;this.u=c;this.o={};this.ja=e;a={};this.g=(a[b]=[],a[4]=[],a);this.j={};this.l={};var f=f===void 0?window:f;if(zg===null){zg="";try{b="";try{b=f.top.location.hash}catch(h){b=f.location.hash}if(b){var g=b.match(/\bdeid=([\d,]+)/);zg=g?g[1]:""}}catch(h){}}if(f=zg)for(f=y(f.split(",")||[]),g=f.next();!g.done;g=f.next())(g=Number(g.value))&&(this.j[g]=!0);d=y(d);for(f=d.next();!f.done;f=d.next())this.j[f.value]=!0},Nh=function(a,b,c,d){var e=[],f;if(f=b!==9)a.o[b]?f=!0:(a.o[b]=!0,f=!1);if(f)return Lg(a.u,b,c,e,[],4),e;f=v(Jh,"includes").call(Jh,b);for(var g=[],h=[],k=y([0,1,2]),m=k.next();!m.done;m=k.next()){m=m.value;for(var n=y(v(a.M[m],"entries").call(a.M[m])),l=n.next();!l.done;l=n.next()){var p=y(l.value);l=p.next().value;p=p.next().value;var r=l,t=p;l=new Re;p=t.filter(function(cb){return cb.ba===b&&a.j[cb.F.getId()]&&Lh(a,cb)});if(p.length)for(l=y(p),p=l.next();!p.done;p=l.next())h.push(p.value.F);else if(!a.ja){p=void 0;m===2?(p=d[1],qd(l,2,Se,r)):p=d[0];var z=void 0,A=void 0;p=(A=(z=p)==null?void 0:z(String(r)))!=null?A:m===2&&T(t[0].fa,11)===1?void 0:d[0](String(r));if(p!==void 0){r=y(t);for(t=r.next();!t.done;t=r.next())if(t=t.value,t.ba===b){z=p-t.Pa;var pa=t;A=pa.Ka;var bb=pa.La;pa=pa.hb;z<0||z>=A*bb||z%bb!==pa||!Lh(a,t)||(z=T(t.fa,13),z!==0&&z!==void 0&&(A=a.l[String(z)],A!==void 0&&A!==t.F.getId()?Mg(a.u,a.l[String(z)],t.F.getId(),z):a.l[String(z)]=t.F.getId()),h.push(t.F))}Vc(l,Se)!==0&&(ld(l,3,p),g.push(l))}}}}d=y(h);for(h=d.next();!h.done;h=d.next())h=h.value,k=h.getId(),e.push(k),Mh(a,k,f?4:c),ch(R(h,Gg,2,P()),f?eh():[c],a.u,k);Lg(a.u,b,c,e,g,1);return e},Mh=function(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];v(a,"includes").call(a,b)||a.push(b)},Lh=function(a,b){var c=W(Og).A,d=Cg(Q(b.fa,df,3),c);if(!d.success)return Ng(a.u,Q(b.fa,df,3),b.ba,b.F.getId(),d),!1;if(!d.value)return!1;c=Cg(Q(b.F,df,3),c);return c.success?c.value?!0:!1:(Ng(a.u,Q(b.F,df,3),b.ba,b.F.getId(),c),!1)},Oh=function(a,b){b=b.map(function(c){return new xh(c)}).filter(function(c){return!v(Jh,"includes").call(Jh,T(c,1))});a.M=Bh(a.M,b)},Ph=function(a,b){X(1,function(c){a.j[c]=!0},b);X(2,function(c,d,e){return Nh(a,c,d,e)},b);X(3,function(c){return(a.g[c]||[]).concat(a.g[4])},b);X(12,function(c){return void Oh(a,c)},b);X(16,function(c,d){return void Mh(a,c,d)},b)};var Qh=function(){var a={};this.g=function(b,c){return a[b]!=null?a[b]:c};this.j=function(b,c){return a[b]!=null?a[b]:c};this.J=function(b,c){return a[b]!=null?a[b]:c};this.l=function(b,c){return a[b]!=null?a[b]:c};this.C=function(b,c){return a[b]!=null?c.concat(a[b]):c};this.o=function(){}};function Rh(a){return W(Qh).j(a.g,a.defaultValue)};var Sh=function(){this.g=function(){}},Th=function(a,b){a.g=Y(14,b,function(){})};function Uh(a){W(Sh).g(a)};var Vh,Wh,Xh,Yh,Zh,$h;function ai(a){var b=a.Wa;var c=a.A;var d=a.config;var e=a.Ra===void 0?th():a.Ra;var f=a.Ca===void 0?0:a.Ca;var g=a.u===void 0?new Jg((Yh=mg((Vh=Q(b,yh,5))==null?void 0:hd(Vh,2)))!=null?Yh:0,(Zh=mg((Wh=Q(b,yh,5))==null?void 0:hd(Wh,4)))!=null?Zh:0,($h=(Xh=Q(b,yh,5))==null?void 0:ed(Xh,3))!=null?$h:!1):a.u;a=a.M===void 0?Ah(R(b,xh,2,P(tb))):a.M;e.hasOwnProperty("init-done")?(Y(12,e,function(){})(R(b,xh,2,P()).map(function(h){return K(h)})),Y(13,e,function(){})(R(b,Gg,1,P()).map(function(h){return K(h)}),f),c&&Y(14,e,function(){})(c),bi(f,e)):(Ph(new Kh(a,f,g,d),e),gh(e),hh(e),ih(e),bi(f,e),ch(R(b,Gg,1,P(tb)),[f],g,void 0,!0),Pg=Pg||!(!d||!d.ma),Uh(sh),c&&Uh(c))}function bi(a,b){var c=b=b===void 0?th():b;kh(W(jh),c,a);ci(b,a);a=b;Th(W(Sh),a);W(Qh).o()}function ci(a,b){var c=W(Qh);c.g=function(d,e){return Y(5,a,function(){return!1})(d,e,b)};c.j=function(d,e){return Y(6,a,function(){return 0})(d,e,b)};c.J=function(d,e){return Y(7,a,function(){return""})(d,e,b)};c.l=function(d,e){return Y(8,a,function(){return[]})(d,e,b)};c.C=function(d,e){return Y(17,a,function(){return[]})(d,e,b)};c.o=function(){Y(15,a,function(){})(b)}};var di=qa(["https://pagead2.googlesyndication.com/pagead/js/err_rep.js"]),ei=function(){var a=a===void 0?"jserror":a;var b=b===void 0?.01:b;var c=c===void 0?Ud(di):c;this.g=a;this.l=b;this.j=c};var fi=function(){var a;this.V=a=a===void 0?{ib:Pd()+(Pd()&2097151)***********,Ua:v(Number,"MAX_SAFE_INTEGER")}:a};function gi(a,b){return b>0&&a.ib*b<=a.Ua};var hi=function(a){this.i=L(a)};x(hi,U);var ii=function(a){return ed(a,1)},ji=function(a){return ed(a,2)};function ki(a){a=a===void 0?D:a;return(a=a.performance)&&a.now?a.now():null};function li(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)}function mi(a,b){var c=ki(b);c&&li({label:a,type:9,value:c},b)}function ni(a,b,c){var d=!1;d=d===void 0?!1:d;var e=window,f=typeof queueMicrotask!=="undefined";return function(){var g=sa.apply(0,arguments);d&&f&&queueMicrotask(function(){e.google_rum_task_id_counter=e.google_rum_task_id_counter||1;e.google_rum_task_id_counter+=1});var h=ki(),k=3;try{var m=b.apply(this,g)}catch(n){k=13;if(!c)throw n;c(a,n)}finally{e.google_measure_js_timing&&h&&li(v(Object,"assign").call(Object,{},{label:a.toString(),value:h,duration:(ki()||0)-h,type:k},d&&f&&{taskId:e.google_rum_task_id_counter=e.google_rum_task_id_counter||1}),e)}return m}}function oi(a,b){return ni(a,b,function(c,d){var e=new ei;var f=f===void 0?e.l:f;var g=g===void 0?e.g:g;Math.random()>f||(d.error&&d.meta&&d.id||(d=new we(d,{context:c,id:g})),D.google_js_errors=D.google_js_errors||[],D.google_js_errors.push(d),D.error_rep_loaded||(f=D.document,c=$d("SCRIPT",f),Jd(c,e.j),(e=f.getElementsByTagName("script")[0])&&e.parentNode&&e.parentNode.insertBefore(c,e),D.error_rep_loaded=!0))})};function Z(a,b){return b==null?"&"+a+"=null":"&"+a+"="+Math.floor(b)}function pi(a,b){return"&"+a+"="+b.toFixed(3)}function qi(){var a=new u.Set;var b=window.googletag;b=(b==null?0:b.apiReady)?b:void 0;try{if(!b)return a;for(var c=b.pubads(),d=y(c.getSlots()),e=d.next();!e.done;e=d.next())a.add(e.value.getSlotId().getDomId())}catch(f){}return a}function ri(a){a=a.id;return a!=null&&(qi().has(a)||v(a,"startsWith").call(a,"google_ads_iframe_")||v(a,"startsWith").call(a,"aswift"))}function si(a,b,c){if(!a.sources)return!1;switch(ti(a)){case 2:var d=ui(a);if(d)return c.some(function(f){return vi(d,f)});break;case 1:var e=wi(a);if(e)return b.some(function(f){return vi(e,f)})}return!1}function ti(a){if(!a.sources)return 0;a=a.sources.filter(function(b){return b.previousRect&&b.currentRect});if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function wi(a){return xi(a,function(b){return b.currentRect})}function ui(a){return xi(a,function(b){return b.previousRect})}function xi(a,b){return a.sources.reduce(function(c,d){d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function vi(a,b){var c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50}var yi=function(){this.l=this.j=this.U=this.S=this.L=0;this.ya=this.va=Number.NEGATIVE_INFINITY;this.g=[];this.P={};this.sa=0;this.T=Infinity;this.qa=this.ta=this.ua=this.wa=this.Ba=this.C=this.Aa=this.ha=this.o=0;this.ra=!1;this.ga=this.R=this.J=0;this.u=null;this.xa=!1;this.pa=function(){};var a=document.querySelector("[data-google-query-id]");this.za=a?a.getAttribute("data-google-query-id"):null},zi,Ai,Di=function(){var a=new yi;if(W(Qh).g(Qf.g,Qf.defaultValue)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=y(["layout-shift","largest-contentful-paint","first-input","longtask","event"]);for(var c=b.next();!c.done;c=b.next()){c=c.value;var d={type:c,buffered:!0};c==="event"&&(d.durationThreshold=40);Bi(a).observe(d)}Ci(a)}}},Bi=function(a){a.u||(a.u=new PerformanceObserver(oi(640,function(b){Ei(a,b)})));return a.u},Ci=function(a){var b=oi(641,function(){var d=document;if(d.prerendering)d=3;else{var e;d=(e={visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""])!=null?e:0}d===2&&Fi(a)}),c=oi(641,function(){return void Fi(a)});document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.pa=function(){document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);Bi(a).disconnect()}},Fi=function(a){if(!a.xa){a.xa=!0;Bi(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=pi("cls",a.L),b+=pi("mls",a.S),b+=Z("nls",a.U),window.LayoutShiftAttribution&&(b+=pi("cas",a.C),b+=Z("nas",a.wa),b+=pi("was",a.Ba)),b+=pi("wls",a.ha),b+=pi("tls",a.Aa));window.LargestContentfulPaint&&(b+=Z("lcp",a.ua),b+=Z("lcps",a.ta));window.PerformanceEventTiming&&a.ra&&(b+=Z("fid",a.qa));window.PerformanceLongTaskTiming&&(b+=Z("cbt",a.J),b+=Z("mbt",a.R),b+=Z("nlt",a.ga));for(var c=0,d=y(document.getElementsByTagName("iframe")),e=d.next();!e.done;e=d.next())ri(e.value)&&c++;b+=Z("nif",c);c=window.google_unique_id;b+=Z("ifi",typeof c==="number"?c:0);c=mh();b+="&eid="+encodeURIComponent(c.join());b+="&top="+(D===D.top?1:0);b+=a.za?"&qqid="+encodeURIComponent(a.za):Z("pvsid",ae(D));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.u?a.sa:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=Z("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"});a.pa()}},Gi=function(a,b,c,d){if(!b.hadRecentInput){a.L+=Number(b.value);Number(b.value)>a.S&&(a.S=Number(b.value));a.U+=1;if(c=si(b,c,d))a.C+=b.value,a.wa++;if(b.startTime-a.va>5E3||b.startTime-a.ya>1E3)a.va=b.startTime,a.j=0,a.l=0;a.ya=b.startTime;a.j+=b.value;c&&(a.l+=b.value);a.j>a.ha&&(a.ha=a.j,a.Ba=a.l,a.Aa=b.startTime+b.duration)}},Ei=function(a,b){var c=zi!==window.scrollX||Ai!==window.scrollY?[]:Hi,d=Ii();b=y(b.getEntries());for(var e=b.next(),f={};!e.done;f={D:void 0},e=b.next())switch(f.D=e.value,e=f.D.entryType,e){case "layout-shift":Gi(a,f.D,c,d);break;case "largest-contentful-paint":f=f.D;a.ua=Math.floor(f.renderTime||f.loadTime);a.ta=f.size;break;case "first-input":e=f.D;a.qa=Number((e.processingStart-e.startTime).toFixed(3));a.ra=!0;a.g.some(function(g){return function(h){return v(h,"entries").some(function(k){return g.D.duration===k.duration&&g.D.startTime===k.startTime})}}(f))||Ji(a,f.D);break;case "longtask":f=Math.max(0,f.D.duration-50);a.J+=f;a.R=Math.max(a.R,f);a.ga+=1;break;case "event":Ji(a,f.D);break;default:Qb(e)}},Ji=function(a,b){Ki(a,b);var c=a.g[a.g.length-1],d=a.P[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(v(d,"entries").push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.P[b.id]=b,a.g.push(b)),a.g.sort(function(e,f){return f.latency-e.latency}),a.g.splice(10).forEach(function(e){delete a.P[e.id]})},Ki=function(a,b){b.interactionId&&(a.T=Math.min(a.T,b.interactionId),a.o=Math.max(a.o,b.interactionId),a.sa=a.o?(a.o-a.T)/7+1:0)},Ii=function(){var a=v(Array,"from").call(Array,document.getElementsByTagName("iframe")).filter(ri),b=[].concat(B(qi())).map(function(c){return document.getElementById(c)}).filter(function(c){return c!==null});zi=window.scrollX;Ai=window.scrollY;return Hi=[].concat(B(a),B(b)).map(function(c){return c.getBoundingClientRect()})},Hi=[];var Li=function(a){this.i=L(a)};x(Li,U);Li.prototype.getVersion=function(){return S(this,2)};var Mi=function(a){this.i=L(a)};x(Mi,U);var Ni=function(a,b){return O(a,2,oc(b))},Oi=function(a,b){return O(a,3,oc(b))},Pi=function(a,b){return O(a,4,oc(b))},Qi=function(a,b){return O(a,5,oc(b))},Ri=function(a,b){return O(a,9,oc(b))},Si=function(a,b){return cd(a,10,b)},Ti=function(a,b){return O(a,11,b==null?b:Wb(b))},Ui=function(a,b){return O(a,1,oc(b))},Vi=function(a,b){return O(a,7,b==null?b:Wb(b))};var Wi="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Xi(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Yi(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Zi(a){if(!Yi(a))return null;var b=Xi(a);if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Wi).then(function(c){b.uach!=null||(b.uach=c);return c});return b.uach_promise=a}function $i(a){var b;return Ti(Si(Qi(Ni(Ui(Pi(Vi(Ri(Oi(new Mi,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new Li;d=O(d,1,oc(c.brand));return O(d,2,oc(c.version))}))||[]),a.wow64||!1)}function aj(a){var b,c;return(c=(b=Zi(a))==null?void 0:b.then(function(d){return $i(d)}))!=null?c:null};var bj=function(a){this.i=L(a)};x(bj,U);var cj=function(a){this.i=L(a)};x(cj,U);var dj=function(a){var b=new cj;return ad(b,1,a)};function ej(a,b,c){try{vb(!b._b_);var d={d:K(a.data),p:a.jb};b._b_=d}catch(e){c.G({methodName:1298,I:e})}};var fj=function(a,b,c){this.g=a;this.K=b;this.j=c};fj.prototype.G=function(a){var b=gi(this.K.V,1E3),c=Rh(Nf),d=gi(this.K.V,c);if(b||d){var e=this.j,f=e.Na,g=e.Ma,h=e.Ha,k=e.ca,m=e.Ya;e=e.Xa;k=k();var n=a.I;try{var l=zb(n==null?void 0:n.name)?n.name:"Unknown error"}catch(z){l="e.name threw"}try{var p=zb(n==null?void 0:n.message)?n.message:"Caught "+n}catch(z){p="e.message threw"}try{var r=zb(n==null?void 0:n.stack)?n.stack:Error().stack;var t=r?r.split(/\n\s*/):[]}catch(z){t=["e.stack threw"]}r=t;t=new tf;t=md(t,5,1E3);n=new rf;a=pd(n,1,a.methodName);a=nd(a,2,l);a=nd(a,3,p);a=Pc(a,4,r,nc);a=rd(a);a=bd(t,1,uf,a);l=new sf;f=md(l,1,f);f=Pc(f,2,k,ac);g=nd(f,3,g);g=rd(g);g=ad(a,2,g);g=rd(g);g=Cc(g);f=new qf;h=nd(f,1,h);m=m==null?void 0:m();m=ld(h,2,m);e=e==null?void 0:e();e=Pc(m,3,e,nc);e=rd(e);e=bd(g,4,vf,e);b&&this.g.pb(e);if(d){md(e,5,c);a:{Ec(e);if(void 0===ub){if(Wc(e,uf,1)!==1){b=void 0;break a}}else Tc(e.i,void 0,uf,1);b=Xc(e,rf,1)}b!=null&&O(b,4);this.g.ob(e)}}};function gj(a){var b={};b=(b[0]=Ih(),b);W(jh).j(a,b)};var hj={},ij=(hj[253]=!1,hj[246]=[],hj[150]="",hj[263]=!1,hj[36]=/^true$/.test("false"),hj[172]=null,hj[260]=void 0,hj[251]=null,hj),jj=function(){this.g=!1};function kj(a){W(jj).g=!0;return ij[a]}function lj(a,b){W(jj).g=!0;ij[a]=b};var mj=/^(?:https?:)?\/\/(?:www\.googletagservices\.com|securepubads\.g\.doubleclick\.net|(pagead2\.googlesyndication\.com))(\/tag\/js\/gpt(?:_[a-z]+)*\.js)/;function nj(a){var b=a.Ia;var c=a.fb;var d=a.Oa;var e=a.cb;var f=a.Va;var g=a.Za;var h=b?!mj.test(b.src):!0;a={};b={};var k={};return k[3]=(a[3]=function(){return!h},a[59]=function(){var m=sa.apply(0,arguments),n=v(m,"includes"),l=String,p;var r=r===void 0?window:r;var t;r=(t=(p=Eh(r.location.href.match(Dh)[3]||null))==null?void 0:p.split("."))!=null?t:[];p=r.length<2?null:(q=["uk","br","nz","mx"],v(q,"includes")).call(q,r[r.length-1])?r.length<3?null:Hh(r.splice(r.length-3).join(".")):Hh(r.splice(r.length-2).join("."));return n.call(m,l(p))},a[74]=function(){return v(sa.apply(0,arguments),"includes").call(sa.apply(0,arguments),String(Hh(window.location.href)))},a[61]=function(){return e},a[63]=function(){return e||g===".google.ch"},a[73]=function(m){return v(d,"includes").call(d,Number(m))},a),k[4]=(b[1]=function(){return f},b[13]=function(){return c},b),k[5]={},k};function oj(a,b){if(W(Qh).g(Of.g,Of.defaultValue)){var c=function(d){d.data!=null&&d.data!==""||d.origin.indexOf("android-app://")!==0||(b(),a.removeEventListener("message",c))};a.addEventListener("message",c)}};function pj(a){return!(a==null||!a.src)&&Eh(a.src.match(Dh)[3]||null)==="pagead2.googlesyndication.com"};var qj=qa(["https://pagead2.googlesyndication.com/pagead/managed/js/gpt/","/pubads_impl.js"]),rj=qa(["https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/","/pubads_impl.js"]);function sj(a){a=pj(a)?Ud(qj,"m202508260101"):Ud(rj,"m202508260101");var b=Rh(Sf);return b?Vd(a,new u.Map([["cb",b]])):a};function tj(a,b){var c=kj(246);c=wc(c);c=sd(zh,c);if(!R(c,Gg,1,P()).length&&R(a,Gg,1,P()).length){var d=R(a,Gg,1,P());cd(c,1,d)}!R(c,xh,2,P()).length&&R(a,xh,2,P()).length&&(d=R(a,xh,2,P()),cd(c,2,d));!Jc(c,yh,5)&&Jc(a,yh,5)&&(a=Q(a,yh,5),ad(c,5,a));lj(246,K(c));ai({Wa:c,A:nj(b),Ca:2,config:{ma:b.ma}});b.Oa.forEach(lh)};var uj=function(a,b,c){fj.call(this,a,b,c);this.j=c};x(uj,fj);var vj=qa(["https://pagead2.googlesyndication.com/pagead/ppub_config"]),wj=qa(["https://securepubads.g.doubleclick.net/pagead/ppub_config"]);function xj(a,b,c,d,e){a=a.location.host;var f=Gh(b.src,"domain");b=Gh(b.src,"network-code");if(a||f||b){var g=new u.Map;a&&g.set("ippd",a);f&&g.set("pppd",f);b&&g.set("pppnc",b);a=g}else a=void 0;a?(c=c?Ud(vj):Ud(wj),c=Vd(c,a),yj(c,d,e)):e(new u.globalThis.Error("no provided or inferred data"))}function yj(a,b,c){u.globalThis.fetch(a.toString(),{method:"GET",credentials:"omit"}).then(function(d){d.status<300?(mi("13",window),d.status===204?b(""):d.text().then(function(e){b(e)})):c(new u.globalThis.Error("resp:"+d.status))}).catch(function(d){d instanceof Error?c(new u.globalThis.Error(d.message)):c(new u.globalThis.Error("fetch error: "+d))})};var zj=function(a,b,c){this.K=a;this.oa=b;this.da=c;this.g=[]},Dj=function(a,b,c,d,e){var f=e==null?void 0:vd(Yc(e,ud,1));(f==null?0:f.length)&&v(b.location.hostname,"includes").call(b.location.hostname,f)?(Aj(a),Bj(a,{kb:e})):(q=["http:","https:"],v(q,"includes")).call(q,b.location.protocol)?c?(Aj(a),xj(yd(b),c,d,function(g){return void Bj(a,{mb:g})},function(g){Bj(a,{error:g})})):Cj(a,5):Cj(a,4)},Aj=function(a){kj(260);lj(260,function(b){a.j!==void 0||a.l?b(a.j,a.l):a.g.push(b)})},Bj=function(a,b){var c=b.mb;var d=b.kb;b=b.error;a.j=c!=null?c:d==null?void 0:JSON.stringify(K(d));a.l=b;d=y(a.g);for(var e=d.next();!e.done;e=d.next())e=e.value,e(a.j,a.l);a.g.length=0;Cj(a,b?6:c?3:2)},Cj=function(a,b){var c=Rh(Rf);gi(a.K.V,c)&&a.oa.Ja.Qa.Ga.lb.W({Z:c,source:b,Sa:!E("Android")||Ma()||Ka()||Ja()||E("Silk")?Ma()?2:(Ia()?0:E("Edge"))?3:Ka()?4:(Ia()?0:E("Trident")||E("MSIE"))?5:!E("iPad")&&!E("iPhone")||La()||Ma()||(Ia()?0:E("Coast"))||Ka()||!E("AppleWebKit")?Ja()?6:La()?7:E("Silk")?8:0:9:1});a=a.da;var d=Rh(Rf);if(gi(a.K.V,d)){var e=a.j,f=e.Ha,g=e.ca;c=e.Ma;e=e.Na;var h=new pf;e=md(h,2,e);f=nd(e,3,f);e=Math;h=e.trunc;a:{if(u.globalThis.performance){var k=performance.timeOrigin+performance.now();if(v(Number,"isFinite").call(Number,k)&&k>0)break a}k=Date.now();k=v(Number,"isFinite").call(Number,k)&&k>0?k:0}f=md(f,1,h.call(e,k));g=g();g=Pc(f,4,g,ac);d=md(g,5,d);c=nd(d,6,c);d=new nf;g=new mf;b=od(g,1,b);b=rd(b);b=bd(d,10,of,b);b=rd(b);b=ad(c,7,b);b=rd(b);a.g.qb(b)}};var Ej=function(a){return function(b){b=JSON.parse(b);if(!Array.isArray(b))throw Error("Expected jspb data to be an array, got "+xa(b)+": "+b);mb(b,34);return new a(b)}}(bj),Fj=function(a){return function(){return a[eb]||(a[eb]=rc(a))}}(bj);function Gj(a,b){try{var c=wb;if(!zb(a)){var d,e,f=(e=(d=typeof c==="function"?c():c)==null?void 0:d.concat("\n"))!=null?e:"";throw Error(f+String(a));}return Ej(a)}catch(g){return b.G({methodName:838,I:g}),Fj()}};function Hj(){var a;return(a=D.googletag)!=null?a:D.googletag={cmd:[]}}function Ij(a,b){var c=Hj();c.hasOwnProperty(a)||(c[a]=b)};function Jj(){var a=sttc,b=Kj(),c=b.K,d=b.oa,e=b.da;Xa(function(A){e.G({methodName:1189,I:A})});b=Hj();a=Gj(a,e);vb(!W(jj).g);v(Object,"assign").call(Object,ij,b._vars_);b._vars_=ij;a&&(ed(a,3)&&lj(36,!0),S(a,6)&&lj(150,S(a,6)),ji(Yc(a,hi,13))&&lj(263,!0));var f=Yc(a,zh,1),g={cb:ii(Yc(a,hi,13)),fb:fd(a,2),Oa:Kc(a,10,bc,P()),Va:fd(a,7),Za:S(a,6),ma:ed(a,4)},h=Q(a,wd,9),k=window,m=k.document;Ij("_loaded_",!0);Ij("cmd",[]);var n,l=(n=Lj(m))!=null?n:Mj(m);Nj(f,k,v(Object,"assign").call(Object,{},{Ia:l},g));try{Di()}catch(A){}mi("1",k);n=sj(l);f=(l==null?void 0:l.crossOrigin)==="anonymous";g=Rh(Pf);if(gi(c.V,g)){var p=d.Ja.Qa.Ga;p.eb.W({Z:g,la:(l==null?void 0:l.crossOrigin)==="anonymous",na:pj(l)});p.ab.W({Z:g,la:f,na:Eh(n.toString().match(Dh)[3]||null)==="pagead2.googlesyndication.com"})}var r=!1;ej({data:rd(dj(a)),jb:function(){return r}},b,e);if(!Oj(m)){g="gpt-impl-"+Math.random();try{Ld(m,Sd(n,{id:g,nonce:Id(document),Ea:f?"anonymous":void 0}))}catch(A){}m.getElementById(g)&&(b._loadStarted_=!0)}if(!b._loadStarted_){g=$d("SCRIPT");Jd(g,n);g.async=!0;f&&(g.crossOrigin="anonymous");n=m.body;f=m.documentElement;var t,z;((z=(t=m.head)!=null?t:n)!=null?z:f).appendChild(g);b._loadStarted_=!0}if(k===k.top)try{tg(k,Yc(a,hi,13))}catch(A){e.G({methodName:1209,I:A})}Dj(new zj(c,d,e),k,l,pj(l),h);oj(k,function(){r=!0});S(a,14)&&xg(k.document,yg(S(a,14),pj(l)),e)}function Kj(){var a=ae(window),b=new fi,c=new Kf(11,"m202508260101",1E3);return{oa:c,K:b,da:new uj(c,b,{Na:a,Ma:window.document.URL,Ha:"m202508260101",ca:mh})}}function Lj(a){return(a=a.currentScript)?a:null}function Mj(a){var b;a=y((b=a.scripts)!=null?b:[]);for(b=a.next();!b.done;b=a.next())if(b=b.value,v(b.src,"includes").call(b.src,"/tag/js/gpt"))return b;return null}function Nj(a,b,c){lj(172,c.Ia);tj(a,c);gj(12);gj(5);(a=aj(b))&&a.then(function(d){return void lj(251,JSON.stringify(K(d)))});Zd(W(Qh).l(Tf.g,Tf.defaultValue),b.document)}function Oj(a){var b=Lj(a);return a.readyState==="complete"||a.readyState==="loaded"||!(b==null||!b.async)};try{Jj()}catch(a){try{Kj().da.G({methodName:420,I:a})}catch(b){}};}).call(this,"[[[[772510493,null,null,[1]],[null,7,null,[null,0.1]],[null,null,null,[],[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\",\"26 bidderRequests.bids bidder transactionId\",\"27 bidderRequests.bids.userIdAsEids source mm\",\"28 bidderRequests.bids.userIdAsEids source matcher\",\"29 bidderRequests.bids.userIdAsEids source inserter\"]]]],*********],[null,*********,null,null,[[[4,null,83],[null,1]]]],[null,*********,null,null,[[[4,null,83],[null,100]]]],[null,*********,null,[null,-1]],[null,*********,null,[null,-1],[[[4,null,83],[null,5000]]]],[null,*********,null,[null,-1],[[[4,null,83],[null,60000]]]],[null,null,null,[null,null,null,[\"1 dbm\/(ad|clkk)\"]],[[[4,null,83],[null,null,null,[\"1 dbm\/(ad|clkk)\",\"2 (adsrvr|adserver)\\\\.org\/bid\/\",\"3 criteo.com\/(delivery|[a-z]+\/auction)\",\"4 yahoo.com\/bw\/[a-z]+\/imp\/\",\"5 (adnxs|adnxs-simple).com\/it\",\"6 amazon-adsystem.com\/[a-z\/]+\/impb\"]]]],655300591],[null,643045660,null,null,[[[4,null,83],[null,1]]]],[null,741624914,null,[null,100]],[741624915,null,null,[1]],[null,612427113,null,[null,100]],[null,699982590,null,null,[[[4,null,83],[null,100]]]],[null,720326000,null,[null,1]],[null,749055567,null,[null,100]],[null,732179314,null,[null,10]],[45681222,null,null,[]],[null,578655462,null,[null,1000]],[698519058,null,null,[1]],[667794963,null,null,[]],[736254283,null,null,[1]],[697841467,null,null,[1]],[null,704895900,null,[null,1000]],[null,770246397,null,[null,1000]],[null,797753679,null,[null,40]],[null,797753680,null,[null,160]],[null,797753681,null,[null,600]],[null,797753678,null,[null,20]],[null,625028672,null,[null,100]],[null,629733890,null,[null,1000]],[null,695925491,null,[null,20]],[null,null,null,[],null,489560439],[null,null,null,[],null,505762507],[null,1921,null,[null,72]],[null,1920,null,[null,12]],[null,426169222,null,[null,1000]],[null,377289019,null,[null,10000]],[null,750987462,null,[null,10000]],[null,529,null,[null,20]],[null,573282293,null,[null,0.01]],[null,684553008,null,[null,100]],[776685356,null,null,[]],[45624259,null,null,[],[[[4,null,59,null,null,null,null,[\"2452487976\"]],[1]]]],[45627954,null,null,[1]],[45646888,null,null,[]],[45622305,null,null,[1]],[null,447000223,null,[null,0.01]],[360245597,null,null,[1]],[null,716359135,null,[null,10]],[null,716359138,null,[null,50]],[null,716359132,null,[null,100]],[null,716359134,null,[null,1000]],[null,716359137,null,[null,0.25]],[null,716359136,null,[null,10]],[null,716359133,null,[null,5]],[629201869,null,null,[1]],[null,729624435,null,[null,10000]],[null,794150638,null,[null,5000]],[null,729624436,null,[null,500]],[null,794664882,null,[null,-1]],[null,550718589,null,[null,250],[[[3,[[4,null,15,null,null,null,null,[\"22814497764\"]],[4,null,15,null,null,null,null,[\"6581\"]],[4,null,15,null,null,null,null,[\"18190176\"]],[4,null,15,null,null,null,null,[\"21881754602\"]],[4,null,15,null,null,null,null,[\"6782\"]],[4,null,15,null,null,null,null,[\"309565630\"]],[4,null,15,null,null,null,null,[\"22306534072\"]],[4,null,15,null,null,null,null,[\"7229\"]],[4,null,15,null,null,null,null,[\"28253241\"]],[4,null,15,null,null,null,null,[\"1254144\"]],[4,null,15,null,null,null,null,[\"21732118914\"]],[4,null,15,null,null,null,null,[\"5441\"]],[4,null,15,null,null,null,null,[\"162717810\"]],[4,null,15,null,null,null,null,[\"51912183\"]],[4,null,15,null,null,null,null,[\"23202586\"]],[4,null,15,null,null,null,null,[\"44520695\"]],[4,null,15,null,null,null,null,[\"1030006\"]],[4,null,15,null,null,null,null,[\"21830601346\"]],[4,null,15,null,null,null,null,[\"23081961\"]],[4,null,15,null,null,null,null,[\"21880406607\"]],[4,null,15,null,null,null,null,[\"93656639\"]],[4,null,15,null,null,null,null,[\"1020351\"]],[4,null,15,null,null,null,null,[\"5931321\"]],[4,null,15,null,null,null,null,[\"3355436\"]],[4,null,15,null,null,null,null,[\"22106840220\"]],[4,null,15,null,null,null,null,[\"22875833199\"]],[4,null,15,null,null,null,null,[\"32866417\"]],[4,null,15,null,null,null,null,[\"8095840\"]],[4,null,15,null,null,null,null,[\"71161633\"]],[4,null,15,null,null,null,null,[\"22668755367\"]],[4,null,15,null,null,null,null,[\"6177\"]],[4,null,15,null,null,null,null,[\"147246189\"]],[4,null,15,null,null,null,null,[\"22152718\"]],[4,null,15,null,null,null,null,[\"21751243814\"]],[4,null,15,null,null,null,null,[\"22013536576\"]],[4,null,15,null,null,null,null,[\"4444\"]],[4,null,15,null,null,null,null,[\"44890869\"]],[4,null,15,null,null,null,null,[\"248415179\"]],[4,null,15,null,null,null,null,[\"5293\"]],[4,null,15,null,null,null,null,[\"21675937462\"]],[4,null,15,null,null,null,null,[\"21726375739\"]],[4,null,15,null,null,null,null,[\"1002212\"]],[4,null,15,null,null,null,null,[\"6718395\"]]]],[null,500]]]],[null,575880738,null,[null,10]],[null,586681283,null,[null,100]],[null,45679761,null,[null,30000]],[null,732179799,null,[null,250]],[null,745376890,null,[null,1]],[null,745376891,null,[null,2]],[null,635239304,null,[null,100]],[788110566,null,null,[]],[null,740510593,null,[null,0.3]],[null,618260805,null,[null,10]],[752401957,null,null,null,[[[1,[[4,null,59,null,null,null,null,[\"473346114\"]]]],[1]]]],[null,532520346,null,[null,30]],[null,723123766,null,[null,100]],[null,735866756,null,[null,100]],[45690337,null,null,[1]],[761958081,null,null,[1]],[null,758860411,null,[null,60]],[null,751161866,null,[null,30000]],[null,630428304,null,[null,100]],[730909248,null,null,null,[[[1,[[4,null,59,null,null,null,null,[\"473346114\"]]]],[1]]]],[746075837,null,null,[]],[null,624264750,null,[null,-1]],[759731353,null,null,[1]],[607106222,null,null,[1]],[792821490,null,null,[1]],[null,398776877,null,[null,60000]],[null,374201269,null,[null,60000]],[null,371364213,null,[null,60000]],[null,682056200,null,[null,100]],[null,376149757,null,[null,0.0025]],[570764855,null,null,[1]],[null,null,579921177,[null,null,\"control_1\\\\.\\\\d\"]],[null,570764854,null,[null,50]],[578725095,null,null,[1]],[791241077,null,null,[1]],[684149381,null,null,[1]],[377936516,null,null,[1]],[null,599575765,null,[null,1000]],[null,null,2,[null,null,\"1-0-45\"]],[783765187,null,null,[1]],[null,707091695,null,[null,100]],[null,626653285,null,[null,1000]],[null,626653286,null,[null,2]],[null,642407444,null,[null,10]],[715057423,null,null,[1]],[null,741215712,null,[null,100]],[738482525,null,null,[1]],[null,506394061,null,[null,100]],[null,733365673,null,[null,2],[[[4,null,59,null,null,null,null,[\"1282204929\"]],[null,1]]]],[null,null,null,[null,null,null,[\"95335247\"]],null,631604025],[null,694630217,null,[null,670]],[null,783316493,null,[null,4000]],[783316492,null,null,[1]],[null,null,null,[],null,489],[null,754057781,null,null,[[[6,null,null,3,null,2],[null,2]]]],[392065905,null,null,null,[[[3,[[4,null,68],[4,null,83]]],[1]]]],[null,360245595,null,[null,500]],[null,762520832,null,[null,1000]],[null,715129739,null,[null,30]],[null,681088477,null,[null,100]],[676934885,null,null,[1],[[[4,null,59,null,null,null,null,[\"4214592683\",\"3186860772\",\"2930824068\",\"4127372480\",\"3985777170\",\"2998550476\",\"1946945953\",\"2901923877\",\"1931583037\",\"733037847\",\"287365726\",\"396735883\",\"2445204049\"]],[]]]],[776823724,null,null,[]],[751082905,null,null,[1]],[788470829,null,null,[1]],[703102329,null,null,[1]],[703102335,null,null,[1]],[703102334,null,null,[1]],[703102333,null,null,[1]],[703102330,null,null,[1]],[703102332,null,null,[1]],[555237688,null,null,[],[[[2,[[4,null,70,null,null,null,null,[\"browsing-topics\"]],[1,[[4,null,27,null,null,null,null,[\"isSecureContext\"]]]]]],[1]]]],[555237686,null,null,[]],[null,638742197,null,[null,500]],[null,514795754,null,[null,2]],[null,697023992,null,[null,500]],[null,null,null,[null,null,null,[\"679602798\",\"965728666\",\"3786422334\",\"4071951799\"]],null,603422363],[590730356,null,null,null,[[[12,null,null,null,4,null,\"Chrome\\\\\/((?!1[0-1]\\\\d)(?!12[0-3])\\\\d{3,})\",[\"navigator.userAgent\"]],[1]]]],[564724551,null,null,null,[[[12,null,null,null,4,null,\"Chrome\\\\\/((?!10\\\\d)(?!11[0-6])\\\\d{3,})\",[\"navigator.userAgent\"]],[1]]]],[null,596918936,null,[null,500]],[null,607730666,null,[null,10]],[616896918,null,null,[1]],[767691923,null,null,[1]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9nrunKdU5m96PSN1XsSGr3qOP0lvPFUB2AiAylCDlN5DTl17uDFkpQuHj1AFtgWLxpLaiBZuhrtb2WOu7ofHwEAAACKeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A93bovR+QVXNx2\/38qDbmeYYf1wdte9EO37K9eMq3r+541qo0byhYU899BhPB7Cv9QqD7wIbR1B6OAc9kEfYCA4AAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A1S5fojrAunSDrFbD8OfGmFHdRFZymSM\/1ss3G+NEttCLfHkXvlcF6LGLH8Mo5PakLO1sCASXU1\/gQf6XGuTBgwAAACQeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXRhZ3NlcnZpY2VzLmNvbTo0NDMiLCJmZWF0dXJlIjoiQUlQcm9tcHRBUElNdWx0aW1vZGFsSW5wdXQiLCJleHBpcnkiOjE3NzQzMTA0MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[485990406,null,null,[]]],[[3,[[null,[[1337,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]],[84,null,null,[1]],[188,null,null,[1]]]]]],[10,[[31088080],[31088081]]],[470,[[83321072],[83321073]],null,136],[10,[[83321253],[83321254]],null,136],[10,[[83321442],[83321443]],null,136],[10,[[83322116],[83322117]],null,136],[null,[[676982960],[676982998]]]]],[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[662101537,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[662101539,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[5,[[50,[[31067420],[31067421,[[360245597,null,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[1000,[[31084129,null,[2,[[2,[[8,null,null,1,null,-1],[7,null,null,1,null,10]]],[4,null,3]]]]],null,80,null,null,null,null,null,null,null,null,4],[1000,[[31084130,null,[2,[[2,[[8,null,null,1,null,9],[7,null,null,1,null,20]]],[4,null,3]]]]],null,80,null,null,null,null,null,null,null,null,4],[50,[[31085776],[31085777,[[45624259,null,null,[1]]]]],null,114],[100,[[31086814],[31086815,[[null,665058368,null,[null,1]]]]]],[50,[[31093080],[31093081,[[768109354,null,null,[1]]]]]],[10,[[31093082],[31093083,[[767688524,null,null,[1]]]]]],[10,[[31093611],[31093612,[[760666257,null,null,[1]],[null,758465301,null,[null,10]]]]]],[1,[[31093709],[31093710,[[null,784887894,null,[null,1]]]]]],[10,[[31093711],[31093712,[[785453655,null,null,[1]]]],[31094046,[[792252173,null,null,[1]]]]]],[50,[[31093745],[31093746,[[null,754057781,null,[]]]]],[6,null,null,3,null,2]],[1000,[[31093856,null,[2,[[2,[[8,null,null,1,null,84],[7,null,null,1,null,86]]],[4,null,3]]]]],null,151,null,null,null,null,null,null,null,null,34],[1000,[[31093857,[[782893618,null,null,[1]],[null,753762684,null,[null,500]]],[2,[[2,[[8,null,null,1,null,89],[7,null,null,1,null,91]]],[4,null,3]]]]],null,151,null,null,null,null,null,null,null,null,34],[1000,[[31093858,[[782893618,null,null,[1]],[null,753762684,null,[null,1000]]],[2,[[2,[[8,null,null,1,null,94],[7,null,null,1,null,96]]],[4,null,3]]]]],null,151,null,null,null,null,null,null,null,null,34],[1000,[[31093941,[[782893618,null,null,[1]],[null,753762684,null,[null,2000]]],[2,[[2,[[8,null,null,1,null,98],[7,null,null,1,null,100]]],[4,null,3]]]]],null,151,null,null,null,null,null,null,null,null,34],[10,[[31094130,null,[2,[[6,null,null,3,null,2],[3,[[4,null,59,null,null,null,null,[\"4266216755\"]],[4,null,59,null,null,null,null,[\"2873384857\"]],[4,null,59,null,null,null,null,[\"534638854\"]],[4,null,59,null,null,null,null,[\"3453837838\"]],[4,null,59,null,null,null,null,[\"209085618\"]],[4,null,59,null,null,null,null,[\"1755614695\"]],[4,null,59,null,null,null,null,[\"2629955493\"]],[4,null,59,null,null,null,null,[\"209085618\"]],[4,null,59,null,null,null,null,[\"787328721\"]],[4,null,59,null,null,null,null,[\"2777520706\"]]]]]]],[31094131,[[null,null,null,[null,null,null,[\"\/21707781519\/ls-mweb\/ls_mweb_hp\",\"\/1211\/br.terra.homepage\/home360\/ancora\",\"\/1211\/br.terra.news\/brasil.articles\/ancora\",\"\/34616581\/m.20minutos.es\/home\/<USER>\/Sticky\",\"85406138\/Mobile_HP_Anchor\",\"\/15748617,22365852633\/Loteriasdominicanascom\/Loteriasdominicanascom-Mobile-pushup\",\"\/8804\/uol\/home\/320x50_footer\",\"\/424397508\/dailymail.uk\/dm_dmhome_homehp\/sticky_banner_monews\",\"\/424397508\/dailymail.uk\/dm_dmnews_newsart\/sticky_banner\",\"\/15748617,22365852633\/Loteriasdominicanascom\/Loteriasdominicanascom-Mobile-pushup\",\"\/35821442\/gazzetta.it\/homepage\/TopLeft\",\"\/138855687\/nacional-spo\/portada\"]],null,771226568]],[2,[[6,null,null,3,null,2],[3,[[4,null,59,null,null,null,null,[\"4266216755\"]],[4,null,59,null,null,null,null,[\"2873384857\"]],[4,null,59,null,null,null,null,[\"534638854\"]],[4,null,59,null,null,null,null,[\"3453837838\"]],[4,null,59,null,null,null,null,[\"209085618\"]],[4,null,59,null,null,null,null,[\"1755614695\"]],[4,null,59,null,null,null,null,[\"2629955493\"]],[4,null,59,null,null,null,null,[\"209085618\"]],[4,null,59,null,null,null,null,[\"787328721\"]],[4,null,59,null,null,null,null,[\"2777520706\"]]]]]]],[31094132,null,[2,[[6,null,null,3,null,2],[3,[[4,null,59,null,null,null,null,[\"4266216755\"]],[4,null,59,null,null,null,null,[\"2873384857\"]],[4,null,59,null,null,null,null,[\"534638854\"]],[4,null,59,null,null,null,null,[\"3453837838\"]],[4,null,59,null,null,null,null,[\"209085618\"]],[4,null,59,null,null,null,null,[\"1755614695\"]],[4,null,59,null,null,null,null,[\"2629955493\"]],[4,null,59,null,null,null,null,[\"209085618\"]],[4,null,59,null,null,null,null,[\"787328721\"]],[4,null,59,null,null,null,null,[\"2777520706\"]]]]]]]],null,149],[20,[[31094234],[31094235,[[null,740510593,null,[null,0.5]]]],[31094236,[[null,740510593,null,[null,0.5]]]],[31094267,[[null,740510593,null,[null,0.5]]]]]],[1,[[31094265],[31094266,[[794152180,null,null,[1]]]]]],[1000,[[31094272,[[782893618,null,null,[1]],[null,753762684,null,[null,5000]]],[2,[[2,[[8,null,null,1,null,96],[7,null,null,1,null,98]]],[4,null,3]]]]],null,151,null,null,null,null,null,null,null,null,34],[1000,[[31094273,[[null,753762684,null,[null,5000]]],[2,[[2,[[8,null,null,1,null,97],[7,null,null,1,null,99]]],[4,null,3]]]]],null,151,null,null,null,null,null,null,null,null,34],[1000,[[31094345,[[null,24,null,[null,31094345]]],[6,null,null,13,null,31094345]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31094346,[[null,24,null,[null,31094346]]],[6,null,null,13,null,31094346]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1,[[31094355],[31094356,[[797429200,null,null,[1]]]]]],[1000,[[31094377,[[null,24,null,[null,31094377]]],[6,null,null,13,null,31094377]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31094378,[[null,24,null,[null,31094378]]],[6,null,null,13,null,31094378]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[50,[[95366805],[95366806,[[776823724,null,null,[1]]]]]],[10,[[95370038],[95370039,[[788110566,null,null,[1]]]]]]]],[9,[[1000,[[31083577]],[4,null,13,null,null,null,null,[\"cxbbhbxm\",\"hzwxrfqd\"]]],[1000,[[31091121,null,[4,null,13,null,null,null,null,[\"zxcvbnms\"]]]],null,141,null,null,null,null,null,null,null,null,33],[1000,[[31091122,[[45681222,null,null,[1]]],[4,null,13,null,null,null,null,[\"qwrtplkj\"]]]],null,141,null,null,null,null,null,null,null,null,33]]],[2,[[10,[[31084489],[31084490]],null,null,null,null,32,null,null,142,1],[1000,[[31084739,[[null,*********,null,[null,100]]]]],[4,null,6,null,null,null,null,[\"31065645\"]]],[10,[[31084865],[31084866]],null,null,null,null,35,null,null,166,1],[1000,[[31087377,null,[2,[[4,null,86],[4,null,6,null,null,null,null,[\"31065644\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087378,null,[2,[[4,null,86],[4,null,6,null,null,null,null,[\"31065645\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087490,null,[2,[[1,[[4,null,86]]],[4,null,6,null,null,null,null,[\"31065644\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087491,null,[2,[[1,[[4,null,86]]],[4,null,6,null,null,null,null,[\"31065645\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[50,[[31090916,null,[1,[[4,null,15,null,null,null,null,[\"18190176\"]]]]]],[2,[[1,[[4,null,6,null,null,null,null,[\"31091121\"]]]],[1,[[4,null,6,null,null,null,null,[\"31091122\"]]]]]],141,null,null,null,null,null,null,null,null,33],[50,[[31090917,[[45681222,null,null,[1]]],[1,[[4,null,15,null,null,null,null,[\"18190176\"]]]]]],[2,[[1,[[4,null,6,null,null,null,null,[\"31091121\"]]]],[1,[[4,null,6,null,null,null,null,[\"31091122\"]]]]]],141,null,null,null,100,null,null,null,null,33],[10,[[31092998],[31092999,[[729624434,null,null,[1]]]],[31093000,[[725693774,null,null,[1]]]],[31094359,[[729624434,null,null,[1]],[null,794664882,null,[null,0.5]],[797954106,null,null,[1]]]],[31094360,[[729624434,null,null,[1]],[725693774,null,null,[1]],[null,794664882,null,[null,0.5]],[797954106,null,null,[1]]]],[31094395,[[775698922,null,null,[1]],[729624434,null,null,[1]],[null,794664882,null,[null,0.5]],[797954106,null,null,[1]]]],[31094396,[[775698922,null,null,[1]],[725693774,null,null,[1]]]],[31094397,[[729624434,null,null,[1]],[725693774,null,null,[1]],[null,794664882,null,[null,0.5]],[797954106,null,null,[1]]]],[95367712,[[775698922,null,null,[1]]]]],null,null,null,null,null,null,null,198,1],[800,[[31093004,[[729624434,null,null,[1]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,200,null,198,1],[90,[[31093009,[[729624434,null,null,[1]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[3,[[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,110,null,198,1],[10,[[31093114,[[729624434,null,null,[]],[725693774,null,null,[]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[3,[[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,90,null,198,1],[10,[[31093115,[[729624434,null,null,[1]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[3,[[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,100,null,198,1],[50,[[31094102],[31094103,[[45690337,null,null,[]]]]],null,null,null,null,null,null,null,222],[10,[[31094320,null,[4,null,15,null,null,null,null,[\"18190176\"]]]],[2,[[1,[[4,null,6,null,null,null,null,[\"31091121\"]]]],[1,[[4,null,6,null,null,null,null,[\"31091122\"]]]]]],141,null,null,null,200,null,null,null,null,33],[10,[[31094321,[[45681222,null,null,[1]]],[4,null,15,null,null,null,null,[\"18190176\"]]]],[2,[[1,[[4,null,6,null,null,null,null,[\"31091121\"]]]],[1,[[4,null,6,null,null,null,null,[\"31091122\"]]]]]],141,null,null,null,300,null,null,null,null,33],[10,[[95342027],[95342028]],[4,null,83],129],[50,[[95349880],[95349881,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 dbm\/(ad|clkk)\",\"2 (adsrvr|adserver)\\\\.org\/bid\/\",\"3 criteo.com\/(delivery|[a-z]+\/auction)\",\"4 yahoo.com\/bw\/[a-z]+\/imp\/\",\"5 (adnxs|adnxs-simple).com\/it\",\"6 amazon-adsystem.com\/[a-z\/]+\/impb\",\"7 temu.com\/api\/[a-z0-9]+\/ads\",\"8 temu.com\/[a-z0-9]+\/impr\"]]]],655300591]]]],[4,null,83],129],[50,[[95351361],[95351362,[[null,null,null,null,[[[4,null,83],[]]],655300591]]]],[4,null,83],129],[50,[[95351363],[95351364,[[null,null,null,null,[[[4,null,83],[]]],*********]]]],[4,null,83],129],[1,[[95357519],[95357520,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\",\"26 bidderRequests.bids bidder transactionId\",\"27 bidderRequests.bids.userIdAsEids source mm\",\"28 bidderRequests.bids.userIdAsEids source matcher\",\"29 bidderRequests.bids.userIdAsEids source inserter\",\"22 bidsReceived adId ad\"]]]],*********]]]],[4,null,83],129],[20,[[95357665],[95357666],[95357667],[95357668],[95357669],[95357670]],[4,null,89],null,null,null,37,780,null,166,1],[1,[[95365417,null,[4,null,92,null,null,null,null,[\"userId\"]]]]],[1,[[95365418,null,[4,null,92,null,null,null,null,[\"chromeAiRtdProvider\"]]]]],[50,[[95367375],[95367376,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\",\"26 bidderRequests.bids bidder transactionId\",\"27 bidderRequests.bids.userIdAsEids source mm\",\"28 bidderRequests.bids.userIdAsEids source matcher\",\"29 bidderRequests.bids.userIdAsEids source inserter\",\"23 bidderRequests.bids bidder ortb2.site.content.data.ext.segtax\",\"24 bidderRequests.bids bidder ortb2.user.data.ext.segtax\",\"25 bidderRequests.bids bidder ortb2.user.data.name\"]]]],*********]]]],[4,null,83],129],[10,[[95368453],[95368454,[[*********,null,null,[1]]]],[95368455,[[*********,null,null,[1]]]]],null,null,null,null,null,null,null,219,1],[100,[[95369066],[95369067,[[45712479,null,null,[1]]]],[95370082,[[45712479,null,null,[1]],[45690337,null,null,[]]]]],null,null,null,null,null,500,null,222]]],[27,[[50,[[31090502,null,[2,[[4,null,59,null,null,null,null,[\"1282204929\",\"2762681000\",\"1201683087\",\"1405537016\",\"1184328227\",\"3766824835\",\"*********\",\"2849015825\",\"2285744699\",\"*********\",\"1242812256\",\"2369380032\",\"3013643711\",\"77481481\",\"2269399977\",\"3906315807\",\"2791111070\",\"2128463204\",\"3298531905\",\"2399173495\",\"3986628766\",\"*********\",\"*********\",\"*********\",\"1028035958\"]],[8,null,null,17,null,0]]]],[31090503,[[*********,null,null,[1]]],[2,[[4,null,59,null,null,null,null,[\"1282204929\",\"2762681000\",\"1201683087\",\"1405537016\",\"1184328227\",\"3766824835\",\"*********\",\"2849015825\",\"2285744699\",\"*********\",\"1242812256\",\"2369380032\",\"3013643711\",\"77481481\",\"2269399977\",\"3906315807\",\"2791111070\",\"2128463204\",\"3298531905\",\"2399173495\",\"3986628766\",\"*********\",\"*********\",\"*********\",\"1028035958\"]],[8,null,null,17,null,0]]]]]]]],[4,[[null,[[44714449,[[null,7,null,[null,1]]]],[676982961,[[null,7,null,[null,0.4]],[212,null,null,[1]]]],[676982996,[[null,7,null,[null,1]]]]],null,78]]]],null,null,[null,1000,1,1000]],31094345,null,null,null,\".google.com.tr\",505,null,[[\"sahibinden.com\",null,\"https:\/\/secure.sahibinden.com\/giris?return_url=https%3A%2F%2Fwww.sahibinden.com%2Farama%3Faddress_country%3D1%26a27%3D38460%26category%3D16633%26address_city%3D9\",null,null,[\"32607536\"]],[],[[[\"32607536\",null,1,null,[[1]]]]],[31086810],null,[[\"32607536\",[[\"google.com\",null,1]]]],null,[[[\"1036854\",1],[\"1041449\",1],[\"1096601\",1],[\"115145492\",1],[\"11930669\",1],[\"230811316\",1],[\"32607536\",1],[\"9801810\",1]]],[[[\"1036854\",1],[\"1041449\",1],[\"1096601\",1],[\"115145492\",1],[\"11930669\",1],[\"230811316\",1],[\"32607536\",1],[\"9801810\",1]]],null,[[52868,1756753200],[42190,1756754400],[5469,1756755600],[52766,1756756800]],[1]],null,null,null,[0,0,0],\"m202508280101\"]")
