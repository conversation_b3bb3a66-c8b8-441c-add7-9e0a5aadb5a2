/**
 * Copyright (c) 2023 Apple Inc. All rights reserved.
 * 
 * # Sign In with Apple License
 * 
 * **IMPORTANT:** This Sign In with Apple software is supplied to you by Apple Inc. ("Apple") in consideration of your agreement to the following terms, and your use, reproduction, or installation of this Apple software constitutes acceptance of these terms. If you do not agree with these terms, please do not use, reproduce or install this Apple software.
 * 
 * This software is licensed to you only for use with Sign In with Apple that you are authorized or legally permitted to embed or display on your website.
 *
 * The Sign In with Apple software is only licensed and intended for the purposes set forth above and may not be used for other purposes or in other contexts without Apple's prior written permission. For the sake of clarity, you may not and agree not to or enable others to, modify or create derivative works of the Sign In with Apple software.
 *
 * You may only use the Sign In with Apple software if you are enrolled in the Apple Developer Program.
 * 
 * Neither the name, trademarks, service marks or logos of Apple Inc. may be used to endorse or promote products, services without specific prior written permission from Apple. Except as expressly stated in this notice, no other rights or licenses, express or implied, are granted by Apple herein.
 * 
 * The Sign In with Apple software software is provided by Apple on an "AS IS" basis. APPLE MAKES NO WARRANTIES, EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION THE IMPLIED WARRANTIES OF NON-INFRINGEMENT, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE, REGARDING THE SIGN IN WITH APPLE SOFTWARE OR ITS USE AND OPERATION ALONE OR IN COMBINATION WITH YOUR PRODUCTS, SYSTEMS, OR SERVICES.  APPLE DOES NOT WARRANT THAT THE SIGN IN WITH APPLE SOFTWARE WILL MEET YOUR REQUIREMENTS, THAT THE OPERATION OF THE SIGN IN WITH APPLE SOFTWARE WILL BE UNINTERRUPTED OR ERROR-FREE, THAT DEFECTS IN THE SIGN IN WITH APPLE SOFTWARE WILL BE CORRECTED, OR THAT THE SIGN IN WITH APPLE SOFTWARE WILL BE COMPATIBLE WITH FUTURE APPLE PRODUCTS, SOFTWARE OR SERVICES. NO ORAL OR WRITTEN INFORMATION OR ADVICE GIVEN BY APPLE OR AN APPLE AUTHORIZED REPRESENTATIVE WILL CREATE A WARRANTY.
 * 
 * IN NO EVENT SHALL APPLE BE LIABLE FOR ANY DIRECT, SPECIAL, INDIRECT, INCIDENTAL OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) RELATING TO OR ARISING IN ANY WAY OUT OF THE USE, REPRODUCTION, OR INSTALLATION, OF THE SIGN IN WITH APPLE SOFTWARE BY YOU OR OTHERS, HOWEVER CAUSED AND WHETHER UNDER THEORY OF CONTRACT, TORT (INCLUDING NEGLIGENCE), STRICT LIABILITY OR OTHERWISE, EVEN IF APPLE HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. SOME JURISDICTIONS DO NOT ALLOW THE LIMITATION OF LIABILITY FOR PERSONAL INJURY, OR OF INCIDENTAL OR CONSEQUENTIAL DAMAGES, SO THIS LIMITATION MAY NOT APPLY TO YOU. In no event shall Apple's total liability to you for all damages (other than as may be required by applicable law in cases involving personal injury) exceed the amount of fifty dollars ($50.00). The foregoing limitations will apply even if the above stated remedy fails of its essential purpose.
 * 
 * **ACKNOWLEDGEMENTS:**
 * https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/acknowledgements.txt
 * 
 * v1.5.5
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).AppleID={})}(this,function(e){"use strict";function t(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var i,n=function(e){return"[object Array]"===Object.prototype.toString.call(e)},o=function(e,t){var i="string"==typeof e?document.getElementById(e):e;if(null!==i)return i.innerHTML=t,i},A=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";("string"==typeof t||n(t))&&(i=t,t={}),i||(i="");var o="";for(var A in t)void 0!==t[A]&&t.hasOwnProperty(A)&&(o+=" "+A+'="'+t[A]+'"');return n(i)&&(i=i.join("")),"<"+e+o+">"+i+"</"+e+">"},r=function(e){var t="";for(var i in e)e[i]&&e.hasOwnProperty(i)&&(t+=" "+i+": "+e[i]+";");return t},l=function(e){return"number"!=typeof e||isNaN(e)?"100%":Math.floor(e)+"px"},d=function(e){var t=e.color,i=e.borderRadius,n=void 0===i?15:i,o=e.border,d=void 0!==o&&o,a=e.width,h=void 0===a?"100%":a,g=e.height,s=void 0===g?"100%":g,u=e.isSquare,p=void 0!==u&&u;return A("svg",{xmlns:"http://www.w3.org/2000/svg",style:r({overflow:"visible"}),width:l(h),height:l(s),viewBox:p?"0 0 50 50":void 0,preserveAspectRatio:p?"xMidYMin meet":void 0},A("rect",{width:l(h),height:l(s),ry:"".concat(n,"%"),fill:c(t),stroke:d?"black":void 0,"stroke-width":d?"1":void 0,"stroke-linecap":d?"round":void 0}))},a=function(e){return"black"===e?"#fff":"#000"},c=function(e){return"black"===e?"#000":"#fff"},h={"sign-in":{text:"Apple ile Giriş Yap",centerAlignBoundingBox:{x:0,y:-11,width:111.375,height:14},leftAlignBoundingBox:{x:0,y:-12,width:106.921875,height:15.125},fontFamily:"applied-button-font-0",rtl:!1,letterSpacing:"-.022em"},continue:{text:"Apple ile Devam Et",centerAlignBoundingBox:{x:0,y:-11,width:115.234375,height:14},leftAlignBoundingBox:{x:0,y:-12,width:111.0625,height:15},fontFamily:"applied-button-font-0",rtl:!1,letterSpacing:"-.022em"},"sign-up":{text:"Apple ile Kaydol",centerAlignBoundingBox:{x:0,y:-11,width:100.21875,height:14},leftAlignBoundingBox:{x:0,y:-12,width:94.75,height:15},fontFamily:"applied-button-font-0",rtl:!1,letterSpacing:"-.022em"}},g=function(e){return h},s=function(e){var t=e.color,i=void 0===t?"black":t,n=e.type,o=void 0===n?"sign-in":n,c=e.border,h=void 0!==c&&c,s=e.width,u=e.height,p=e.borderRadius,w=(e.locale,g()[o]),f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"black",i=g()[e],n=i.text,o=i.rtl,A=i.fontFamily,r=i.centerAlignBoundingBox,l=r.width,d=r.height,c=r.y,h=r.x;return'\n  <svg xmlns="http://www.w3.org/2000/svg" style="pointer-events: none; overflow: visible;" width="100%" height="100%">\n    <g>\n      <svg xmlns="http://www.w3.org/2000/svg" style="overflow: visible;" width="100%" height="50%" y="25%" viewBox="'.concat(h," ").concat(c," ").concat(l," ").concat(d,'" fill="').concat(a(t),'">\n        <defs>\n          <style>\n            ').concat('\n  @font-face {\n    font-family: "applied-button-font-0";\n    src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff")\n  }','\n          </style>\n        </defs>\n        <text font-size="12px" ').concat("0em"!==i.letterSpacing?'textLength="'.concat(l,'"'):"",' font-family="').concat(A,'" direction="').concat(o?"rtl":"ltr",'"> ').concat(n,"</text>\n      </svg>\n    </g>\n  </svg>\n  ")}(o,i),v=r({"font-synthesis":"none","-moz-font-feature-settings":"kern","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale",width:l(s),height:l(u),"min-width":"130px","max-width":"375px","min-height":"30px","max-height":"64px",position:"relative","letter-spacing":"initial"});return A("div",{style:v,role:"button",tabindex:"0","aria-label":w.text},"\n    ".concat(A("div",{style:r({"padding-right":"8%","padding-left":"8%",position:"absolute","box-sizing":"border-box",width:"100%",height:"100%"})},f),"\n    ").concat(A("div",{style:r({padding:h?"1px":void 0,width:"100%",height:"100%","box-sizing":"border-box"})},d({color:i,borderRadius:p,border:h})),"\n    "))},u=[],p=[],w=function(e,t){var i=u.indexOf(e);if(i>=0){var n=p[i];if(n)return n[t]}},f=function(e,t,i){var n=u.indexOf(e);if(n<0){var o={};o[t]=i,u.push(e),p.push(o)}else p[n]||(p[n]={}),p[n][t]=i},v=[],x=!1,C=function(e){if(null===e)return null;var t=e.getBoundingClientRect();return{width:t.width,height:t.height}},b=function(e){return e.contentBoxSize?{width:e.contentBoxSize.inlineSize,height:e.contentBoxSize.blockSize}:{width:e.contentRect.width,height:e.contentRect.height}},m=function(e){var t,i,n=w(e,"lastScheduleResizeCheckSize"),o=n||C(e),A=w(e,"lastKnownSize");A&&(i=o,(t=A).width===i.width&&t.height===i.height)||(w(e,"resizeCallback")(o),f(e,"lastKnownSize",o));f(e,"resizeCheckIsScheduled",!1)},z=function(e,t){f(e,"lastScheduleResizeCheckSize",t),w(e,"resizeCheckIsScheduled")||(f(e,"resizeCheckIsScheduled",!0),"function"==typeof requestAnimationFrame?window.requestAnimationFrame(function(){m(e)}):setTimeout(function(){m(e)},1e3/60))},B=function(e,t){f(e,"resizeCallback",t),w(e,"isObserved")||(f(e,"isObserved",!0),"undefined"!=typeof ResizeObserver?(i||(i=new ResizeObserver(function(e){var t=!0,i=!1,n=void 0;try{for(var o,A=e[Symbol.iterator]();!(t=(o=A.next()).done);t=!0){var r=o.value;z(r.target,b(r))}}catch(e){i=!0,n=e}finally{try{t||null==A.return||A.return()}finally{if(i)throw n}}})),i.observe(e)):x||(window.addEventListener("resize",function(){v.forEach(function(e){return z(e)})}),"undefined"!=typeof MutationObserver&&new MutationObserver(function(){v.forEach(function(e){return z(e)})}).observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),x=!0),v.push(e))},y={small:{height:44,width:24,logoWidth:12,path:"M12.2337427,16.9879688 C12.8896607,16.9879688 13.7118677,16.5445313 14.2014966,15.9532812 C14.6449341,15.4174609 14.968274,14.6691602 14.968274,13.9208594 C14.968274,13.8192383 14.9590357,13.7176172 14.9405591,13.6344727 C14.2107349,13.6621875 13.3330982,14.1241016 12.8065162,14.7430664 C12.3907935,15.2142188 12.012024,15.9532812 12.012024,16.7108203 C12.012024,16.8216797 12.0305005,16.9325391 12.0397388,16.9694922 C12.0859302,16.9787305 12.1598365,16.9879688 12.2337427,16.9879688 Z M9.92417241,28.1662891 C10.8202857,28.1662891 11.2175318,27.5658008 12.3353638,27.5658008 C13.4716724,27.5658008 13.721106,28.1478125 14.7188404,28.1478125 C15.6980982,28.1478125 16.3540162,27.2424609 16.972981,26.3555859 C17.6658521,25.339375 17.9522388,24.3416406 17.9707154,24.2954492 C17.9060474,24.2769727 16.0306763,23.5101953 16.0306763,21.3576758 C16.0306763,19.491543 17.5088013,18.6508594 17.5919459,18.5861914 C16.612688,17.1819727 15.1253248,17.1450195 14.7188404,17.1450195 C13.6194849,17.1450195 12.7233716,17.8101758 12.1598365,17.8101758 C11.5501099,17.8101758 10.7463794,17.1819727 9.79483648,17.1819727 C7.98413335,17.1819727 6.14571538,18.6785742 6.14571538,21.5054883 C6.14571538,23.2607617 6.8293482,25.1176563 7.67003179,26.3186328 C8.39061773,27.3348438 9.01882085,28.1662891 9.92417241,28.1662891 Z"},medium:{height:44,width:31,logoWidth:17,path:"M15.7099491,14.8846154 C16.5675461,14.8846154 17.642562,14.3048315 18.28274,13.5317864 C18.8625238,12.8312142 19.2852829,11.852829 19.2852829,10.8744437 C19.2852829,10.7415766 19.2732041,10.6087095 19.2490464,10.5 C18.2948188,10.5362365 17.1473299,11.140178 16.4588366,11.9494596 C15.9152893,12.56548 15.4200572,13.5317864 15.4200572,14.5222505 C15.4200572,14.6671964 15.4442149,14.8121424 15.4562937,14.8604577 C15.5166879,14.8725366 15.6133185,14.8846154 15.7099491,14.8846154 Z M12.6902416,29.5 C13.8618881,29.5 14.3812778,28.714876 15.8428163,28.714876 C17.3285124,28.714876 17.6546408,29.4758423 18.9591545,29.4758423 C20.2395105,29.4758423 21.0971074,28.292117 21.9063891,27.1325493 C22.8123013,25.8038779 23.1867451,24.4993643 23.2109027,24.4389701 C23.1263509,24.4148125 20.6743484,23.4122695 20.6743484,20.5979021 C20.6743484,18.1579784 22.6069612,17.0588048 22.7156707,16.974253 C21.4353147,15.1382708 19.490623,15.0899555 18.9591545,15.0899555 C17.5217737,15.0899555 16.3501271,15.9596313 15.6133185,15.9596313 C14.8161157,15.9596313 13.7652575,15.1382708 12.521138,15.1382708 C10.1536872,15.1382708 7.75,17.0950413 7.75,20.7911634 C7.75,23.0861411 8.64383344,25.513986 9.74300699,27.0842339 C10.6851558,28.4129053 11.5065162,29.5 12.6902416,29.5 Z"},large:{height:44,width:39,logoWidth:21,path:"M19.8196726,13.1384615 C20.902953,13.1384615 22.2608678,12.406103 23.0695137,11.4296249 C23.8018722,10.5446917 24.3358837,9.30883662 24.3358837,8.07298156 C24.3358837,7.9051494 24.3206262,7.73731723 24.2901113,7.6 C23.0847711,7.64577241 21.6353115,8.4086459 20.7656357,9.43089638 C20.0790496,10.2090273 19.4534933,11.4296249 19.4534933,12.6807374 C19.4534933,12.8638271 19.4840083,13.0469167 19.4992657,13.1079466 C19.5755531,13.1232041 19.6976128,13.1384615 19.8196726,13.1384615 Z M16.0053051,31.6 C17.4852797,31.6 18.1413509,30.6082645 19.9875048,30.6082645 C21.8641736,30.6082645 22.2761252,31.5694851 23.923932,31.5694851 C25.5412238,31.5694851 26.6245041,30.074253 27.6467546,28.6095359 C28.7910648,26.9312142 29.2640464,25.2834075 29.2945613,25.2071202 C29.1877591,25.1766052 26.0904927,23.9102352 26.0904927,20.3552448 C26.0904927,17.2732359 28.5316879,15.8848061 28.6690051,15.7780038 C27.0517133,13.4588684 24.5952606,13.3978385 23.923932,13.3978385 C22.1082931,13.3978385 20.6283185,14.4963764 19.6976128,14.4963764 C18.6906198,14.4963764 17.36322,13.4588684 15.7917006,13.4588684 C12.8012365,13.4588684 9.765,15.9305785 9.765,20.5993643 C9.765,23.4982835 10.8940528,26.565035 12.2824825,28.548506 C13.4725652,30.2268277 14.5100731,31.6 16.0053051,31.6 Z"}},I=function(e,t,i,n){var o=y[e],r=(o.width-o.logoWidth)/2;return A("svg",{xmlns:"http://www.w3.org/2000/svg",height:l(n),width:l(i),viewBox:"".concat(r," 0 ").concat(o.logoWidth," ").concat(o.height)},A("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},A("path",{fill:a(t),"fill-rule":"nonzero",d:o.path})))},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"black",i=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0,o=g()[e],d=o.text,c=o.rtl,h=o.fontFamily,s=o.leftAlignBoundingBox,u=s.width,p=s.x;return A("svg",{xmlns:"http://www.w3.org/2000/svg",style:r({overflow:"visible"}),width:l(i),height:l(n),preserveAspectRatio:c?"xMaxYMid meet":"xMinYMid meet",viewBox:"".concat(p," ").concat(-30*.655," ").concat(u," ").concat(30),fill:"".concat(a(t))},[A("defs",A("style",'\n  @font-face {\n    font-family: "applied-button-font-0";\n    src: url(data:application/x-font-woff;charset=utf-8;base64,d09GRgABAAAAABfsABEAAAAAJ2gAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHUE9TAAAWIAAAATAAAAJgaB9y+kdTVUIAABdQAAAAhgAAANC1cpsKT1MvMgAADQwAAABNAAAAYHLieipic2xuAAAX2AAAABMAAABI/ykCnmNtYXAAAA1cAAAA4wAAAbTXzI0SY3Z0IAAAFLQAAACGAAAA/h4jsglmcGdtAAAOQAAABcMAAAviP64gqWdhc3AAABYYAAAACAAAAAgAAAAQZ2x5ZgAAAYAAAAp7AAAQUIuoLZhoZWFkAAAMUAAAADYAAAA2FZUeyWhoZWEAAAzsAAAAIAAAACQQagbSaG10eAAADIgAAABkAAAAZHHJCw1sb2NhAAAMHAAAADQAAAA0Lrgzhm1heHAAAAv8AAAAIAAAACABbwyMbmFtZQAAFTwAAADFAAABhhtRNi1wb3N0AAAWBAAAABMAAAAg/tsAmnByZXAAABQEAAAArgAAAMdYpaDHeJzVV3twU2UW/1735tk2N8lNCinYNE2CBErb9GFtoalI+kiBKkttmlIoiqXFhbSRV8tjhTKWijqDsOKKoNtSRxEQtbg768isK7qzOIK6S2fd2Rmc3QXXdRz9Qx2hSfd89yalXWH37+1jkvt9597zO+f7nd85FxEUREioFQaRBhlQMPTq3IamgIwYQpih1YiQDSGBEoyjuN4B64SR7tSmsh7D9eFAOkJ6HTxAI0mSaLT7nNRJXdiPMXyySF/80z1XiO4boovHPsZvfIR3Jh4RBq+tYM/FjxBwgUYAwRpAoEcyultFYGOYUhRGCG0IAQgcRkkIVoRgiyDallpWEBgNBoNssOZ4sgXwb3EWMrNsJczltEjOwpKS4iKPxzVCdJdw/gvPJy6Oxr+/jE1P/ynademgMPhS4uzfriR+c5yNXv+25/Njg1d7EOaYGM+KATWqiGYiiphA2YTfDSERC4KCMYoAWRbcRSgmbSnDie0YUnMEDzNI/EdjnOZzy07JmfxntWMP0QPxevJkvOuDD4TBDxJr30kshef1jP9V6BPeQ160SkXhBhciJWIUiUhgogDOKI4Ams0hxBiKgJMtHIxrwg4jOCZ6/4R90qqXY9JLVk+OZVauxujwYWs6ceV4PKS4yFxS4i9kNrsLkpYjirLVZvNDEkuLncVOoa9p6B9PPPP9yRWtJ797egizFzM/m/37A3veXffQuf5Nb6zBiX2kgZQPXD66vOM9bD/1Cra/37lxdP+ux/Z8++LL3+9pfmHdbW+p+RX8kF8jKgvojAYtwMK4Tg0yDXgF6aU4mVnlGoX5NU/lGbNFMjFjps/pkvySHzsx/GnwwcuXTWQ51sU/Jq2JzxNZvxQG4y1kKP742EXyJLjDqBp8ZoNPHSp6UycQesOjUfWYOkp+iXB38uhGLGYT5ax2ScBp/ktdlxPsB7xOwut+SFASpCviT5KusUHgcuv4V/Rr1ghx+dD7oVezGppGZmGCcJ0DPjXwGVYWA7PgOCjQJApsgSPC/GDECBLFnpAW4iQRjY4Q0kvqHSrCObe2RxpNNHmTDqv3/A/z2FTzcDjgSE/DyOvJyZ6RNT1TtqT50n0aARmxUW+0+ty8gooFlQl24EhxUUmpG2oK2KFxLSD+QptNllzFLlEk6J6ddYnvop1FLbRjqPWTd7C3a33zZvNFw6bf9u59+4ELUsHxwcqHFkYkzZKagkXukYb9Dzw1bJRaG1sr71wxUH/fUCye+cy9Ycjkg+NfsZXCF8iKctElNWl6A2ZYwkggdY7UBYWLZE5nQGl2hTSYMaUkIFQowAgFUJzukMgcMHL+yIjSqGop4pQhz7hzsg0hNPxjw5vaxKbYhCG5dpuM0cwsOdeWa8pIT9NpkBVbtZBYQc0lVJvNDkxWU0gnlRzXLTJ74PyGDecHoq/NMY1+KBWM+CO7gsFdkcju6prd5oGxE6+MDaxpJo3XbK+E19Ch8LFYbDgcHu7uHm5WsvgvdhfwUYtcaEGgHBFBFFRZEAgW2jSAkkWAHIAfXEcAfy+txygnO8thlzPS9DqBIS3WcrQYgGmcecTiV5Uiidwmy055JpCghESW9taJxEMXbn7rgv2R320//FlP72e/OPTnjWPjtPZnjW199Ghw5/LlO4PxR9seX9w/dvLk+L6+746HjKv21d8NhES7oUyfEu1AWgvqVM/LLgBJFWBbeX12hxhWW0GqVVEwAI27f8rmzdZ5owgHzBAQSjMwi2ABNxT6lezjuaeTI7KK4vOX1rkXuFN/rOr6WZbwBDzegMcT8MKX/zO0GA0D2u1QTxQtUrEawVdXiEwByNdwOLU26VKFo4drDoMmYQyPCl9cs3FtPYyQ+ATwzIO2ntFTAmoO0mrnnVzEvOUQCA8i02oECmVEU/6mK/GKShVxNz2TLG65GaMciwl2PcjjsuZYPLmSDloYIJL9yWpSVAlESSpSWhj/ojYz6fAFU+6Rn0YP5UqO1bvqj705+kmwveyT7lVVHRWs6lTL2nXhiqb8t1+Om8mZv8xvD8SXkfMtnVXRYPxBlKynDogzAznQ3MDsVPvtCfGDRxGmVr1kwsgumxySA4o9A2eIU4tdOTJvYYkqoVDsZO3zV7Ztu3r0yNVt264c3Xfo0D74l/vjp08l+vsTp07H+8+ee+30uXOnX38XMBxO/JwdZMsUZXwGmjhmggFjRupSMjghez03hK6XC9TDSj+Zp8ggpd03lOpmhmBzq21F6LqVznFLccPJOEEqvC7Iv3I2ltQwlhQ58tFjf9iw/vzeh1/1SaMXTAVnCluS4lZT3ceW7R07cSK+t60x/o1w/I0VHWMt4eFY7Fi4GdTtWLPKOoohEzLqV2NPhwEM2hYlpM6AUY0jkA4fOLVIU53CjPhhQZNXim0yIW0K5/iMpx5ravemGyoReZHISPa4JT57KiRU2GeX88gE5TIae6ozSyWdr8Byz45qVvViybJ8gf2RiiwQWxzfDrG0J2z0n8CsanQwYHaCPmdjCrkk1IIxYXC2eoDnRxot02pYFEZMLRPWw0QDw/BaJIB2CDDlabWpLg9AWwDoJl7GRTe5C+6I/tfbILAcS16Oze12T9MZZ/iwXT1PjebGuRZPtCmXq7g0j3j9EwteL6e7OhxMmiQJG3n2YMtgZ9XqzbVba911LmOl+7Y73NXVldXrh1bqRq1b2ha2ly+59+1nDyzofral51fr9R9mdLVUrCpdTg7Mzn18S/MTzemdpGlpMHy7Xrud6o2OTGdm+2b9kr77Kmvdd80pqLLMce/fWrO1Kb9276qK6txKX0GAV257wqzkdx7yo4pAGYKkEqykhPCUcEIgznQgvdoH+aQkhiEtm8R6aTrPhSVXa8zyWX4clV3JjdoW/yMH2GXx7/7PUJogCQt7hlbveqd7Ssi498h9n9K0qeCtPOzFO5oLQgNtk0K8vgM/zJW3fPwaOS40oEzUEtBzlqdhRklysM3iFt1cm3BYBAnt5uOO0vCjTHlnudV2jKnvB26XJJn4+4GFE9slWXlspbJy/J5imFj8n49++WVdw7Qc+/Ty7C1PLTxxQmhIfDkSv7h8KSWvC2L5rg7SPgJuFsCkUcKq4DXxzhEBT5r2oVgVz6lp/8a18uJkgC/KqyUz2nwYu5QJnJUktlxNbL6CMyScrrS7qrFjcGzgJ5DYyALgx46q38wwClD73BPXPDN8J/BgqPsWLtTbuLsMeJcFNGiVsgmSFsigyCwpSmandkEZfDwpqpeWJl8BQGi0OpdLnzY7Y3FLlimx6QssSdj090Sna3HbjsRGeNivCVm0rJgtGjtK267vv//Re+ClDNPxr4VrYgYqQ0uQdqRufg4i+SCVM4k9ncnWmdTOK4wTiWtHklB5RC0kPmQtIF6+6lWH8AWklNcie04Khlu91FWYn337/Pk+Yp/ndtb8pHhWmb9y7ty7Su/MLWgIeefWzVnY9VitNxgoy7SXL2owTK9Z6jLoMSl2Zi1u9NJ57IAmq7KMTMsLrqwIdtTlyZhWPVCaH77DkmmxTLeUNJdWvXR2ZffJjRVWh1WeIT+SXTiDUJNufpcnpygbp5nnx/4NFCZFrwAAAQAAABkAagAHAAAAAAACAC4APgB3AAAAqQviAAAAAAAAAAAAAABBAIsAzgE7AXUBpwJMAu8DRgOdA/QEIwSXBNkFXgXEBnQG4Ac0B2gHtwe3CCgAAQAAAAEAAPtyTuRfDzz1AAEIAAAAAADWqHHGAAAAANcNYX3+Iv3qCqgIpAAAAAMAAgAAAAAAAAgAAGMCMAAABY8ARQXfALQE1wC0BgMAeQVqALQFZwBGBIYAXQUFAGUEqABlAhYAggIWAIICIwClByAAmwTPAGUE+wCbAzQAmwRQAGYEUABmAwwAOgR0AD0EgAA/AgAAAAdAAQJ4nGNgZGBgn/dPiIGB2/Sf0t9CruUMQBEUIAkAgZoFUHicY2BmecT4hYGVgYHVmOUMAwPDTAjNdIZhFlMvkM/AxgAHrAxIwDEgwAdIKfz4zz7vnxADA/s8xncKDIzzQXIsjqyLQXIMzADTrA7sAAAAeJyN0L9qAkEQx/E5vVNjToISTeyuOPIeQiSFT2AlB5dCMHjIEbiHCfkDQas0lkLENzFdHsEq49fbSZMqAx92F4bd36yI+CJSRSxuH3PyRLyQtVmeQznVt7jqIDL0ycBUcIuh3XdnAoxMDWNTR2IaSHGPM0zNOWZ4QAtzZLjAwrSRm0s8mi4K0yPqxDnljwJHrkQO6sg1069Yb5jMJ2nEVEMmGJE2IdmUFBkv5txeeJMoOKhq+Q8D5v7tS+mbkfNPn37pTrf6qRv90KW+65u+6os+l55+1vtOvV/+5j/qCGriQwwAeJytVmlz01YUlbzFSchSstCiLk+8OE3tJ5NSCAZMCJJlF9zF2VoJSivFTrov0DLDb9CvuTLtDP3GT+u5km0MSdoZppmM7nnvHb27X5k0JUjb91xfiPYzbXa7TYXdex5dNmjND45EtO9RphT+XdSKWrcrDwzTJM0nzZGNvqZrTmBbpCsSwZFFGSV6gp53KLd6r7+mTzlu16WC65mULfk79z1TmkbkCep0sLXlG4JqjGq+L+KUHfZoDVuDlaB1Pl9n5vOOJ2BNFAqa6ngBdgSfTTHaYLQRGIHv+wbpFd+XpHW8Q9+3KKsE7smVQliWdzoe5aVNBWnDD5/0wKKckrBL9OL8gS34hC02Ugv4SYXA7VK2bOLQEZGIoCBez5fg5LYXdIxwx/ekb/qCtnY9nBns2kC/RXlFE06lr2XSSBWwlLZExKUdUubgiPQurKB82aIJJdjUaaf7LKcdCL6BtgKfKUEjMbWo+hPTmuPaZXMU+0n1ci6m0lv0Ckxw4Hcg3EiGnJckXprBMSVhwMihlciODBupiulTXqcVvKUZL1wbf+mMShzqT09lkWxDmn7ZtGhGxZmMS72wYdGsAlEIOuPc5dcBpO3TDK92sJrByqI5XDOfhEQgAl3opVknEFEgaBZBs2hetfe8ONdr+Cs0cyifWPSGam977d100zCxv5Dsn1WxNufse/HcnEN6aNNchWsWlWzHZ/gxgwfpy8hEttTxYg4evLUj5JfVlk2J14bYSM/5FbQC7/jwpAX7W9h9OVWnJDDWtAWJaDmkbfZ1XU9ytaC0WMu4ex7NSVu4NI3im5IoOFsEUP/X/LyuzWq2HQXx2UKFHleMCwjTInxbqFi0pGKd5TLizPKcirMs31RxjuVbKs6zPK/iAktDxRMs31ZxkeU7Kp5k+YGSw7hDNSIsRZX0B9wgFpXHDpdHhw/Tw8rY4ero8FF6+K7SaKbyGv69B//ehV0C/rE04R/LC/CPpYR/LFfgH8sS/GO5Cv9Yvg//WK7BP5ZKiXpSppaC2vlAOMht4CSpROsprtWqIqtCFrrwIhqgJU7JogxrkifivzIM9n59lFp9mS6W47y+5HoYZOzgh+OROX58SYkrib0fgae7x5WgO09Uzvva8p8a/zU2ZS2+pC/Bo8vwHwafbC+aIqxZdEVVz9Ut2vgvKgq4C/pVpERbLomqaHHjI5R3oqglW5gUHr4QGKyYBhu6vrQI/TVMqGU0F/4TCk06lcOoKoWoR7jr2otjUU3voBzuBEtQwLNia9t7mhFZYTzNrGbP+zbPzyJGsUzYsonOdV5tw4BnWPq5yDhBT1LWCXs4zjihARzw/Hr1nRAmYarLJnIooaEJvyASLbjvBCUynZQ5DAfEPo+Cyh+7FTeyR6XECDw76YR8oQspv84xENjJrw5iIOsIzY1km4poHiGassXKOFv1JGTswCCi2p5XFXV8XdniwaZgW4YhL5SwujP+IU8TdVIFDzIjuYxvDixwhqkJ+Ev/qovDVG5iHlQ5ak0M9bpfjav6Ihrw1mi7M7699TL7RM5tRbXKiZfaiq5VIijmYoG1xzlIS5WqoDqjChtGl4tLotSraJL0ugaGBub/a5Ri6/+qPjaf50tdYoSM5dv0Bza6HIyh/03235SDAAz8GLncgstLaXPilwH6cKFKl9GLH5+yfwczV19coCvAdxVdhWhz1FzEVTTxGRzG6RPF5UhtwE9VH3MG4DMAncHnqq8nOx2AZGebOS7ADnMY7DKHwR5zGOwz5zbAF8xh8CVzGHjMYeAzxwG4xxwG95nD4CvmMHjAnCbA18xh8A1zGATMYRAyxwY4YA6DLnMY9JjD4FDR9VGYj3hBm0DfJugW0HdJPWGxhcX3im6M2D/wImH/mCBm/5Qgpv6sqD6i/sKLhPprgpj6W4KY+lDRzRH1ES8S6u8JYuofCWLqY/V0MpcZ/vCyK1Q8pOxK58nwm2L9Aw8nY10AeJxj8N7BcCIoYiMjY1/kBsadHAwcDMkFGxnYnbZXpYW5GTGwMWiBeA58uRyxbL5slhzq7NIsbBxQwXK2VJZQFmc2A1ZFJrAgr9N+6QbxBuEGvgYuBnYGoHZOoKiw034GByQIFmVOcdmowtgRGLHBoSMCzFMD8XZxNDAwsjh0JIeABSOBwIEvnyOezZ/NmkOTXZaFjUdrB+P/1g0svRuZGFw2s6awMbi4AADrlS9DAAB4nGNgIBncBcJjDMdYShgY2Of9E2KZ+P8xiAaL72fYz7qYgYHFkYHh3zSQKOux/3dZwv6//leKrob1FYvj/3cIVaz/gere/esBq9sNhFMZpjL9Y3z+X/CfAkiMWZDx+3+mf5zI8mwHWFcyXWZ8BBJju8y6k+kI42ls9iC7hk0c4R4AmMtWvAAAeJyNj08LAVEUxX/DIGVtPQtbGv+iZmWjLNQUzcKWCTWMDPKVfBMfxodwzDwiKb3ePeeed+7tPKDEjDyWXQYC3YxbVNVlPEeFyPA8PmfDbWpcDS/gcDO8iGNVuEhp4dKkS92wnrQBO52IUHzEljmNVI10HPasWbLiQJJ2oTAUnlQXck4YSvelxMKp1LO84/R1zZHND4fz4fHUu8rUUF0IQ2XzXnn7yuvSUW0L/9kXpBkTdbF+9L37sSPb8Jyvv8/fASPuNJwAAAB4nGNgZgCDfzcYZjFgAQA4VwJ0AAABAAH//wAPeJyNUbFKA0EUnE3ucrl4EYvT2g+wtxex0kJQK0FErjKkCBYK/oBtfkOwE1KksEiVThALETHR4hALi0AUhHF2DZe7gOA+ZnfuzbzdvX0wAGpYwz7K6xubO4iPzloNLDcOT5pYgScVJKxryk2Ol1A+TlpNxPlZonHwELq16pzQWjLnttq0zQ3mUdcuXSYcscdUOMU/B5/4nfHnXP5SeOEV+7zlxUzNda6mm88Lu9zSPQbsMNHXmO2Jtp25Ut4VdjvI2OPMOeM/b72XsXv2CsqAX3zlG1bh23/QWJgoiZvf+ckh00JNXxjx47cbBaXDB50wdHz6puqVUIXteIRA778kZpSNtYfrJXzlgYpc1heof6E8c4pI3ao716K0SGEEW+MpaopQLs95fUVF1cEPFHJ0pHicY2BkYGDgYihjWMTA7OLmE8IgklxZlMMgl5NYksegxMAClGX4/58BBlgdo1wVGJScg0IUGLR8/X0UGIyC/H0VGKxCgrwVGJzAahmhehhBLDDNBKWZoTQLA2tOfnIOgxoSqYBEqgENAJsCNoOJgY1BCsxnZOAAYjawKCODEBYxbgBjKiG3AAB4nGNgZIACpnn/NRjIBgBrrQHJAA==) format("woff")\n  }')),A("text",{"font-size":"13px",textLength:"0em"!==o.letterSpacing?u:void 0,"font-family":h,direction:c?"rtl":"ltr"},d)])},M=function(e){return"number"==typeof e&&!isNaN(e)},E=function(e,t){return t?"left"===e?"right":"left":e},Y=function(e){var t=e.width,i=e.height,n=e.logoSize,o=e.labelPosition,A=e.logoPosition;(t=Math.floor(t))>375?t=375:t<130&&(t=130),(i=Math.floor(i))>64?i=64:i<30&&(i=30),o=Math.floor(o),A=Math.floor(A);var r,l,d=(l=i/(r=y[n]).height,Math.floor(r.logoWidth*l)),a=Math.floor(.5*d),c=Math.floor(.7*d),h=a+d+c,g=Math.floor(t/2);o>g?o=g:o<h&&(o=h);var s=o-c-d;A>s?A=s:A<a&&(A=a);var u=A,p=Math.floor(.08*t),w=o-u-d;return{width:t,height:i,leftMargin:u,logoWidth:d,middleMargin:w,labelWidth:t-u-p-w-d,rightMargin:p,contentWidth:t-p-u}},D=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.id,n=void 0===i?"appleid-button":i,a=e.color,c=void 0===a?"black":a,h=e.type,s=void 0===h?"sign-in":h,u=e.border,p=void 0!==u&&u,w=e.width,f=void 0===w?"100%":w,v=e.height,x=void 0===v?"100%":v,b=e.borderRadius,m=void 0===b?15:b,z=e.labelPosition,y=void 0===z?0:z,D=e.logoPosition,k=void 0===D?0:D,W=e.logoSize,P=void 0===W?"small":W,G=e.locale,R=void 0===G?"":G,O=document.getElementById(n),X="100%"===f,L="100%"===x;if(X||L){var U=C(O);f=X?U.width:f,x=L?U.height:x,B(O,function(e){!function(e,t){var i=t.width,n=t.height,o=t.logoPosition,A=t.labelPosition,r=t.logoSize,d=(t.locale,t.type),a=g()[d].rtl,c=e.firstChild.childNodes,h=c[0],s=c[1],u=Y({width:i,height:n,logoSize:r,labelPosition:A,logoPosition:o});h.style.width=l(u.contentWidth),h.style.height=l(u.height),h.style["padding-".concat(E("right",a))]=l(u.rightMargin),h.style["padding-".concat(E("left",a))]=l(u.leftMargin);var p=h.childNodes,w=p[0],f=p[1],v=p[2];w.setAttribute("width",l(u.logoWidth)),w.setAttribute("height",l(u.height)),f.style.width=l(u.middleMargin),f.style.height=l(u.height),v.setAttribute("width",l(u.labelWidth)),v.setAttribute("height",l(u.height)),s.setAttribute("width",l(u.width)),s.setAttribute("height",l(u.height)),s.firstChild.setAttribute("width",l(u.width)),s.firstChild.setAttribute("height",l(u.height))}(O,{width:X?e.width:f,height:L?e.height:x,logoPosition:k,labelPosition:y,logoSize:P,locale:R,type:s})})}var F=function(e){var i,n=e.color,o=e.type,a=e.border,c=e.width,h=e.height,s=e.borderRadius,u=e.labelPosition,p=e.logoPosition,w=e.logoSize,f=e.locale;if(!M(c)||!M(h))throw new Error("width and height have to be numbers");if(!M(u)||!M(p))throw new Error("labelPosition and logoPosition have to be numbers");var v=g()[o],x=v.rtl,C=Y({width:c,height:h,logoSize:w,labelPosition:u,logoPosition:p}),b=[I(w,n,C.logoWidth,C.height),A("span",{style:r({display:"inline-block",width:l(C.middleMargin),height:l(C.height)})}),S(o,n,f,C.labelWidth,h)];return x&&b.reverse(),A("div",{style:r({"font-synthesis":"none","-moz-font-feature-settings":"kern","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale",position:"relative","letter-spacing":"initial"}),role:"button",tabindex:"0","aria-label":v.text},[A("div",{style:r((i={position:"absolute","box-sizing":"content-box","-webkit-box-sizing":"content-box","-moz-box-sizing":"content-box",width:l(C.contentWidth),height:l(h)},t(i,"padding-".concat(E("right",x)),l(C.rightMargin)),t(i,"padding-".concat(E("left",x)),l(C.leftMargin)),i))},b),d({color:n,borderRadius:s,border:a,width:C.width,height:C.height})])}({color:c,type:s,border:p,width:f,height:x,borderRadius:m,labelPosition:y,logoPosition:k,logoSize:P,locale:R});o(O,F)},k=function(e){var t=e.color,i=void 0===t?"black":t,n=e.size,o=e.border,c=void 0!==o&&o,h=e.borderRadius,s=(e.locale,g()["sign-in"]),u=function(e){return'\n  <svg xmlns="http://www.w3.org/2000/svg" style="overflow:visible" width="100%" height="100%" viewBox="6 6 44 44">\n      <g fill="none" fill-rule="evenodd">\n          <path fill="'.concat(a(e),'" fill-rule="nonzero" d="M28.2226562,20.3846154 C29.0546875,20.3846154 30.0976562,19.8048315 30.71875,19.0317864 C31.28125,18.3312142 31.6914062,17.352829 31.6914062,16.3744437 C31.6914062,16.2415766 31.6796875,16.1087095 31.65625,16 C30.7304687,16.0362365 29.6171875,16.640178 28.9492187,17.4494596 C28.421875,18.06548 27.9414062,19.0317864 27.9414062,20.0222505 C27.9414062,20.1671964 27.9648438,20.3121424 27.9765625,20.3604577 C28.0351562,20.3725366 28.1289062,20.3846154 28.2226562,20.3846154 Z M25.2929688,35 C26.4296875,35 26.9335938,34.214876 28.3515625,34.214876 C29.7929688,34.214876 30.109375,34.9758423 31.375,34.9758423 C32.6171875,34.9758423 33.4492188,33.792117 34.234375,32.6325493 C35.1132812,31.3038779 35.4765625,29.9993643 35.5,29.9389701 C35.4179688,29.9148125 33.0390625,28.9122695 33.0390625,26.0979021 C33.0390625,23.6579784 34.9140625,22.5588048 35.0195312,22.474253 C33.7773438,20.6382708 31.890625,20.5899555 31.375,20.5899555 C29.9804688,20.5899555 28.84375,21.4596313 28.1289062,21.4596313 C27.3554688,21.4596313 26.3359375,20.6382708 25.1289062,20.6382708 C22.8320312,20.6382708 20.5,22.5950413 20.5,26.2911634 C20.5,28.5861411 21.3671875,31.013986 22.4335938,32.5842339 C23.3476562,33.9129053 24.1445312,35 25.2929688,35 Z"></path>\n      </g>\n  </svg>')}(i),p=r({"font-synthesis":"none","-moz-font-feature-settings":"kern","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale",width:l(n),height:l(n),"min-width":"30px","max-width":"64px","min-height":"30px","max-height":"64px",position:"relative"});return A("div",{style:p,role:"button",tabindex:"0","aria-label":s.text},"\n    ".concat(A("div",{style:r({position:"absolute","box-sizing":"border-box",width:"100%",height:"100%"})},u),"\n    ").concat(A("div",{style:r({padding:c?"1px":void 0,width:"100%",height:"100%","box-sizing":"border-box"})},d({color:i,borderRadius:h,border:c,isSquare:!0})),"\n    "))},W=["0","0"],P=function(){for(var e={},t=0;t<arguments.length;t+=1)for(var i=t<0||arguments.length<=t?void 0:arguments[t],n=Object.keys(i),o=0;o<n.length;o+=1){var A=n[o];e[A]=i[A]}return e},G={isInit:!1},R={baseURI:"https://appleid.apple.com",path:"/auth/authorize",originURI:"",env:"prod",usePopup:!1,responseType:"code id_token",responseMode:"form_post",client:{clientId:"",scope:"",redirectURI:"",state:"",nonce:""}},O="user_trigger_new_signin_flow",X="popup_closed_by_user",L="popup_blocked_by_browser",U="AppleIDSigInLoaded",F="AppleIDSignInOnSuccess",N="AppleIDSignInOnFailure",j=function(e){var t="".concat(e.baseURI).concat(e.path,"?client_id=")+encodeURIComponent(e.client.clientId)+"&redirect_uri="+encodeURIComponent(e.client.redirectURI)+"&response_type="+encodeURIComponent(e.responseType);return["state","scope","nonce"].forEach(function(i){e.client[i]&&(t="".concat(t,"&").concat(i,"=").concat(encodeURIComponent(e.client[i])))}),e.client.codeChallenge&&(t+="&code_challenge=".concat(encodeURIComponent(e.client.codeChallenge))),e.client.codeChallengeMethod&&(t+="&code_challenge_method=".concat(encodeURIComponent(e.client.codeChallengeMethod))),t=(t=(t=e.usePopup?t+"&response_mode="+encodeURIComponent("web_message"):t+"&response_mode="+encodeURIComponent(e.responseMode))+"&frame_id="+"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))+"&m="+W[0]+W[1],t+="&v=1.5.5"},J={},Q={},T={},V=function(e){J[e]&&(J[e]=null),Z(e,"closed"),T[e]&&(clearInterval(T[e]),T[e]=null)},K=function(e){return Q[e]||(Q[e]=[]),Q[e]},Z=function(e,t){K(e).forEach(function(e){return e(t)})},H=function(e,t){var i=window.innerWidth?window.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:screen.width,n=window.innerHeight?window.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:screen.height;return{left:i/2-e/2+window.screenLeft,top:n/2-t/2+window.screenTop}},q=H(700,700).left,_=H(700,700).top,$={strWindowFeatures:"width=".concat(700,",height=").concat(700,",left=").concat(q,",top=").concat(_,",resizable=no,location=no,menubar=no"),windowName:"AppleAuthentication"},ee=function(){var e;J[e=$.windowName]&&("function"==typeof J[e].close&&J[e].close(),V(e))},te=function(e){return ee(),t=e,i=$.windowName,n=$.strWindowFeatures,(o=window.open(t,i,n))&&(J[i]=o,T[i]=setInterval(function(){o.closed&&V(i)},300)),o;var t,i,n,o},ie=function(e){return function(e,t){K(e).push(t)}($.windowName,e)},ne=[],oe=[],Ae=function(e){var t=ne.indexOf(e);ne.splice(t,1),oe.splice(t,1)},re=function(e){var t=ne.indexOf(e);return oe[t]},le=function(e){return-1!==ne.indexOf(e)},de=function(){var e,t,i;return function(e,t){ne.push(e),oe.push(t)}(e=new Promise(function(e,n){i=e,t=n}),{reject:t,resolve:i}),e},ae={},ce=R.baseURI;window.addEventListener("message",function(e){try{if(e.origin!==ce)return;var t=JSON.parse(e.data);t.method in ae&&ae[t.method](t.data)}catch(e){}},!1);var he=function(e){"dev"===e.env&&(ce=e.baseURI)},ge=function(e,t){ae[e]=t},se=function(e,t){document.dispatchEvent(function(e,t){return new CustomEvent(e,{detail:t})}(e,t))},ue=null,pe=!0,we=!1,fe=function(){return le(ue)},ve=function(e){se(F,e),fe()&&pe&&function(e,t){le(e)&&(re(e).resolve(t),Ae(e))}(ue,e)},xe=function(e){var t,i;se(N,e),fe()&&pe&&(i=e,le(t=ue)&&(re(t).reject(i),Ae(t)))};ie(function(e){"closed"===e&&we&&(xe({error:X}),we=!1)}),ge("oauthDone",function(e){!function(e){"error"in e?xe(e):ve(e),we=!1,ee()}(e)});var Ce,be,me=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];fe()&&xe({error:O}),pe=t,"2"!==W[1]&&(W[1]="1");var i=j(e);W[1]="0";var n,o,A=!!window.Promise;if(e.usePopup){if(t&&!A)throw new Error("Promise is required to use popup, please use polyfill.");if(te(i)){if(we=!0,t)return o=de(),ue=o}else if(se(N,{error:L}),t)return Promise.reject({error:L})}else n=i,window.location.assign(n)},ze=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:R;["scope","state","nonce","usePopup","codeChallenge","codeChallengeMethod"].forEach(function(i){if(e[i])if("usePopup"===i){if("boolean"!=typeof e[i])throw new Error('The "'+i+'" should be boolean.');t[i]=e[i]}else{if("string"!=typeof e[i])throw new Error('The "'+i+'" should be a string.');t.client[i]=e[i]}})},Be=function(){var e,t,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=R;if(!G.isInit)throw new Error('The "init" function must be called first.');if(i){if(!(i instanceof Object)||Array.isArray(i))throw new Error('The "signinConfig" must be "object".');e=i,(t=Object.create(R)).client=Object.create(R.client),e.scope&&"string"==typeof e.scope&&(t.client.scope=e.scope),e.redirectURI&&"string"==typeof e.redirectURI&&(t.client.redirectURI=e.redirectURI),ze(i,o=t)}return me(o,n)},ye=function(e){if(!e.clientId||"string"!=typeof e.clientId)throw new Error('The "clientId" should be a string.');if(R.client.clientId=e.clientId,!e.redirectURI||"string"!=typeof e.redirectURI)throw new Error('The "redirectURI" should be a string.');R.client.redirectURI=e.redirectURI,ze(e),Ye(),G.isInit=!0},Ie=function(){W[1]="2",Be(null,!1)},Se=function(){Ie()},Me=function(e){32===e.keyCode?e.preventDefault():13===e.keyCode&&(e.preventDefault(),Ie())},Ee=function(e){32===e.keyCode&&(e.preventDefault(),Ie())},Ye=function(){var e,t,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(e=i.id,document.getElementById(e||"appleid-signin"));if(n){(t=n)&&t.firstChild&&t.removeChild(t.firstChild);var A=function(e){var t,i,n,o,A,r=e.dataset,l="center-align",d="black",a=!0,c="sign-in",h="small",g=15;return null!=r&&(r.locale&&(l=r.locale),r.mode&&(l=r.mode),r.color&&(d=r.color),r.border&&(a="false"!==r.border),r.type&&(c=r.type),r.logoSize&&(h=r.logoSize),r.borderRadius&&!isNaN(parseInt(r.borderRadius,10))&&(g=parseInt(r.borderRadius,10)),"100%"===r.width?t=r.width:r.width&&!isNaN(parseInt(r.width,10))&&(t=parseInt(r.width,10)),"100%"===r.height?i=r.height:r.height&&!isNaN(parseInt(r.height,10))&&(i=parseInt(r.height,10)),"100%"===r.size?n=r.size:r.size&&!isNaN(parseInt(r.size,10))&&(n=parseInt(r.size,10)),r.logoPosition&&!isNaN(parseInt(r.logoPosition,10))&&(o=parseInt(r.logoPosition,10)),r.labelPosition&&!isNaN(parseInt(r.labelPosition,10))&&(A=parseInt(r.labelPosition,10))),"sign in"===c&&(c="sign-in"),{mode:l,locale:"",color:d,border:a,type:c,borderRadius:g,width:t,height:i,size:n,logoPosition:o,labelPosition:A,logoSize:h}}(n),r=P({id:"appleid-signin"},A,i);"center-align"===A.mode?function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.id,i=void 0===t?"appleid-button":t,n=e.color,A=void 0===n?"black":n,r=e.type,l=void 0===r?"sign-in":r,d=e.border,a=void 0!==d&&d,c=e.width,h=void 0===c?"100%":c,g=e.height,u=void 0===g?"100%":g,p=e.borderRadius,w=void 0===p?15:p,f=e.locale,v=s({color:A,type:l,border:a,width:h,height:u,borderRadius:w,locale:void 0===f?"":f});o(i,v)}(r):"left-align"===A.mode?D(r):"logo-only"===A.mode&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.id,i=void 0===t?"appleid-button":t,n=e.color,A=void 0===n?"black":n,r=e.size,l=void 0===r?"100%":r,d=e.border,a=void 0!==d&&d,c=e.borderRadius,h=void 0===c?15:c,g=e.locale,s=k({color:A,size:l,border:a,borderRadius:h,locale:void 0===g?"":g});o(i,s)}(r),n.addEventListener("click",Se),n.addEventListener("keydown",Me),n.addEventListener("keyup",Ee)}};!function(e){e.ClientId="appleid-signin-client-id",e.Scope="appleid-signin-scope",e.RedirectURI="appleid-signin-redirect-uri",e.State="appleid-signin-state",e.Nonce="appleid-signin-nonce",e.UsePopup="appleid-signin-use-popup",e.CodeChallenge="appleid-signin-code-challenge",e.CodeChallengeMethod="appleid-signin-code-challenge-method",e.DEV_URI="appleid-signin-dev-uri",e.DEV_ENV="appleid-signin-dev-env",e.DEV_PATH="appleid-signin-dev-path"}(Ce||(Ce={}));var De,ke=function(){if(!be){be={};for(var e=function(){var e={};return Object.keys(Ce).forEach(function(t){return e[Ce[t]]=!0}),e}(),t=document.getElementsByTagName("meta"),i="",n=0;n<t.length;n++)e[i=t[n].getAttribute("name")]&&(be[i]=t[n].getAttribute("content"))}return be},We={},Pe={init:function(e){"2"===W[0]?W[0]="3":W[0]="1",We=P({},We,e),ye(e)},signIn:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return Be(e)},renderButton:Ye},Ge=function(){if(t=ke(),Object.keys(t).length>0){"1"===W[0]?W[0]="4":W[0]="2";var e=function(){var e={clientId:"",scope:"",redirectURI:"",state:"",nonce:""},t=ke();t[Ce.ClientId]&&(e.clientId=t[Ce.ClientId]),t[Ce.Scope]&&(e.scope=t[Ce.Scope]),t[Ce.RedirectURI]&&(e.redirectURI=t[Ce.RedirectURI]),t[Ce.State]&&(e.state=t[Ce.State]),t[Ce.Nonce]&&(e.nonce=t[Ce.Nonce]),t[Ce.UsePopup]&&(e.usePopup="true"===t[Ce.UsePopup]),t[Ce.CodeChallenge]&&(e.codeChallenge=t[Ce.CodeChallenge]),t[Ce.CodeChallengeMethod]&&(e.codeChallengeMethod=t[Ce.CodeChallengeMethod]);var i=t[Ce.DEV_ENV],n=t[Ce.DEV_PATH],o=t[Ce.DEV_URI];return(i||n||o)&&(i&&(R.env=i),n&&(R.path=n),o&&(R.baseURI=o,he(R))),e}();ye(P({},e,We))}var t};"complete"===document.readyState||"loaded"===document.readyState||"interactive"===document.readyState?Ge():document.addEventListener("DOMContentLoaded",function(){Ge()}),De=U,setTimeout(function(){return se(De)}),e.auth=Pe,Object.defineProperty(e,"__esModule",{value:!0})});
