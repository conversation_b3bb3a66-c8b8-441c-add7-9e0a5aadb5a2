from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import time

def sayfa_tipini_algilayin(driver):
    """Açık sayfanın tipini algılar"""
    try:
        # Say<PERSON> başlığını ve URL'yi al
        title = driver.title.lower()
        url = driver.current_url.lower()
        page_source = driver.page_source.lower()

        print(f"Debug - Title: {title}")
        print(f"Debug - URL: {url}")

        # 1. Tarayıcı kontrol sayfası - çok spesifik kontrol
        if ("tarayıcınızı kontrol ediyoruz" in page_source or
            "devam et butonuna tıklayarak" in page_source or
            "cs-loading" in page_source):
            return "Tarayıcı Kontrol Sayfası"

        # 2. Login isteme ekranı - çok spesifik kontrol
        if ("sahibinden.com giriş" in title or
            "individual-login-body" in page_source or
            "login_isteme" in url):
            return "Login İsteme Ekranı"

        # 3. <PERSON>lan detay sayfası
        if ("detay" in url or "ilan_detay" in url or
            "classified-detail" in page_source):
            return "İlan Detay Sayfası"

        # 4. Satılık ilan sayfası (arama sonuçları)
        if ("satilik" in url or "arama" in url or
            "search-result" in page_source):
            return "Satılık İlan Sayfası"

        # 5. Ana sayfa
        if (url.endswith("sahibinden.com/") or url.endswith("sahibinden.com") or
            "türkiye'nin online" in page_source):
            return "Ana Sayfa"

        return "Bilinmeyen Sayfa Tipi"

    except Exception as e:
        return f"Hata oluştu: {str(e)}"

def main():
    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    chrome_options.binary_location = chrome_path
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--guest")  # misafir modu
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument(
        "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/********* Safari/537.36"
    )

    # Chrome driver'ı başlat
    driver = webdriver.Chrome(options=chrome_options)
    
    
    try:
        print("Chrome tarayıcısı açıldı!")
        print("Lütfen istediğiniz sayfaya gidin...")
        
        while True:
            # 1. Kullanıcıdan onay bekle
            input("\nDevam etmek için Enter tuşuna basın...")
            
            # 2. Sayfa tipini algıla
            sayfa_tipi = sayfa_tipini_algilayin(driver)
            print(f"\nAlgılanan sayfa tipi: {sayfa_tipi}")
            
            # 3. Tekrar kullanıcıdan onay bekle
            input("\nBir sonraki kontrol için Enter tuşuna basın...")
            
    except KeyboardInterrupt:
        print("\nProgram sonlandırılıyor...")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
