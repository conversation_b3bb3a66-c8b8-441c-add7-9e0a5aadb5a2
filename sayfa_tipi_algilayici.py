import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
import time

def sayfa_tipini_algi<PERSON><PERSON>(driver):
    """<PERSON><PERSON>ık sayfanın tipini algılar - find_element ile spesifik elementleri kontrol eder"""
    try:
        from selenium.webdriver.common.by import By
        from selenium.common.exceptions import NoSuchElementException

        # 1. Tarayıcı kontrol sayfası - "btn-continue" butonu sadece bu sayfada var
        try:
            driver.find_element(By.ID, "btn-continue")
            return "Tarayıcı Kontrol Sayfası"
        except NoSuchElementException:
            pass

        # 2. Login isteme ekranı - "individual-login-body" class sadece bu sayfada var
        try:
            driver.find_element(By.CLASS_NAME, "individual-login-body")
            return "Login İsteme Ekranı"
        except NoSuchElementException:
            pass

        # 3. <PERSON><PERSON> detay sayfası - "classified-detail" class sadece bu sayfada var
        try:
            driver.find_element(By.CLASS_NAME, "classified-detail")
            return "<PERSON>lan Detay Sayfası"
        except NoSuchElementException:
            pass

        # 4. Satı<PERSON>ı<PERSON> say<PERSON> - "search-result" class sadece bu sayfada var
        try:
            driver.find_element(By.CLASS_NAME, "search-result")
            return "Satılık İlan Sayfası"
        except NoSuchElementException:
            pass

        # 5. Ana sayfa - "homepage-container" class sadece ana sayfada var
        try:
            driver.find_element(By.CLASS_NAME, "homepage-container")
            return "Ana Sayfa"
        except NoSuchElementException:
            pass

        return "Bilinmeyen Sayfa Tipi"

    except Exception as e:
        return f"Hata oluştu: {str(e)}"

def main():
    # Undetected Chrome seçenekleri
    chrome_options = uc.ChromeOptions()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--guest")  # misafir modu
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument(
        "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/********* Safari/537.36"
    )

    # Undetected Chrome driver'ı başlat
    driver = uc.Chrome(options=chrome_options)
    
    
    try:
        print("Chrome tarayıcısı açıldı!")
        print("Lütfen istediğiniz sayfaya gidin...")
        
        while True:
            # 1. Kullanıcıdan onay bekle
            input("\nDevam etmek için Enter tuşuna basın...")
            
            # 2. Sayfa tipini algıla
            sayfa_tipi = sayfa_tipini_algilayin(driver)
            print(f"\nAlgılanan sayfa tipi: {sayfa_tipi}")
            
            # 3. Tekrar kullanıcıdan onay bekle
            input("\nBir sonraki kontrol için Enter tuşuna basın...")
            
    except KeyboardInterrupt:
        print("\nProgram sonlandırılıyor...")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
