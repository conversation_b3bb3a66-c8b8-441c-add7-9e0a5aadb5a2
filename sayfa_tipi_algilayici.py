from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import time

def sayfa_tipini_algilayin(driver):
    """Açık sayfanın tipini algılar"""
    try:
        # Say<PERSON> başlığını ve URL'yi al
        title = driver.title.lower()
        url = driver.current_url.lower()
        page_source = driver.page_source.lower()
        
        # 1. Tarayıcı kontrol sayfası
        if "tarayıcınızı kontrol ediyoruz" in page_source or "tarayici_kontrol" in url:
            return "Tarayıcı Kontrol Sayfası"
        
        # 2. Login isteme ekranı
        if ("giriş yap" in page_source or "login" in page_source or 
            "üye girişi" in page_source or "login_isteme" in url):
            return "Login İsteme Ekranı"
        
        # 3. İlan detay sayfası
        if ("ilan detay" in page_source or "fiyat:" in page_source or 
            "ilan_detay" in url or "detay" in url):
            return "İlan Detay Sayfası"
        
        # 4. Satılık ilan sayfası (arama sonuçları)
        if ("satılık" in page_source or "arama sonuç" in page_source or 
            "satilik" in url or "arama" in url):
            return "Satılık İlan Sayfası"
        
        # 5. Ana sayfa
        if ("sahibinden.com" in title and ("ana sayfa" in page_source or 
            "türkiye'nin online" in page_source or url.endswith("sahibinden.com/"))):
            return "Ana Sayfa"
        
        return "Bilinmeyen Sayfa Tipi"
        
    except Exception as e:
        return f"Hata oluştu: {str(e)}"

def main():
    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    # Chrome driver'ı başlat
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("Chrome tarayıcısı açıldı!")
        print("Lütfen istediğiniz sayfaya gidin...")
        
        while True:
            # 1. Kullanıcıdan onay bekle
            input("\nDevam etmek için Enter tuşuna basın...")
            
            # 2. Sayfa tipini algıla
            sayfa_tipi = sayfa_tipini_algilayin(driver)
            print(f"\nAlgılanan sayfa tipi: {sayfa_tipi}")
            
            # 3. Tekrar kullanıcıdan onay bekle
            input("\nBir sonraki kontrol için Enter tuşuna basın...")
            
    except KeyboardInterrupt:
        print("\nProgram sonlandırılıyor...")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
