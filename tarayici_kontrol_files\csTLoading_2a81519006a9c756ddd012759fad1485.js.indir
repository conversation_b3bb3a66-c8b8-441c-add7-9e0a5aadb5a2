$(function(){var d=$("#btn-continue"),e=$("#continueRequestError"),l=$("#returnUrl").val(),b=$("#sitekeyEnterprise").val(),f=!1,g=!1;$(document).ready(function(){a()});d.on("click",function(){d.attr("disabled","disabled");"undefined"!==typeof b&&null!==b?(f&&(turnstile.reset(),a(),e.hide(),f=!1),m()):h("")});var h=function(a){$.ajax({type:"POST",url:"/ajax/cs/checkT/CS_LOADING",data:{captchaValueEnterprise:a},dataType:"json",success:function(a){var d=a.success;a=a.data;"undefined"!==typeof b&&null!==
b?d&&!0===a?setTimeout(k,2E3):c():setTimeout(k,2E3)},error:c})},k=function(){window.location.href=l},m=function(){try{setTimeout(a,2E4),g||(turnstile.render("#turnStileWidget",{sitekey:b,callback:function(a){e.hide();h(a)},"expired-callback":function(){c()},"timeout-callback":function(){c()},"error-callback":function(){c()},error:function(){c()}}),g=!0)}catch(d){c()}},a=function(){d.removeAttr("disabled")},c=function(){a();e.show();"undefined"!==typeof b&&null!==b&&(f=!0)}});